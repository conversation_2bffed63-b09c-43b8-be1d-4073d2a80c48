{"version": 3, "names": [], "mappings": "", "sources": ["slide.js"], "sourcesContent": ["\n$(window).on('load resize orientationchange', function() {\n\tslideSet();\n\tsingleSlideSet();\n});\n\n// top slide\nfunction slideSet() {\n\t$('.js-topSlide').not('.slick-initialized').slick({\n\t\tarrows: false,\n\t\tautoplay: true,\n\t\tinfinite: true,\n\t\ttouchThreshold: 15,\n\t\tslidesToShow: 1,\n\t\tcenterMode: true,\n\t\tautoplaySpeed: 6000,\n\t\tspeed: 800,\n\t\tdots: true,\n\t\tpauseOnFocus: false, //スライダーを止めない\n\t\tpauseOnHover: false, //スライダーを止めない\n\t\tpauseOnDotsHover: false,\n\t\tcenterPadding: '0',\n\t\tfade: true,\n\t\tcustomPaging: function(slick, index){\n\t\t\tvar num = slick.$slides.eq(index).html();\n\t\t\treturn '<div class=\"slick-dots__dot\">' + '</div>';\n\t\t},\n\t\t\n\t})\n\t.on({\n\t\t\n\t\tbeforeChange: function(event, slick, currentSlide, nextSlide) {\n\t\t\t$(\".slick-slide\", this).eq(currentSlide).addClass(\"preve-slide\");\n\t\t\t$(\".slick-slide\", this).eq(nextSlide).addClass(\"slide-animation\");\n\t\t},\n\t\tafterChange: function() {\n\t\t\t$(\".preve-slide\", this).removeClass(\"preve-slide slide-animation\");\n\t\t},\n\t\ttouchmove: function(event, slick, currentSlide, nextSlide){\n\t\t\t$('.js-topSlide').slick('slickPlay');\n\t\t}\n\t});\n\t$('.js-topSlide').find(\".slick-current\").eq(0).addClass(\"slide-animation\");\n\t\n}\n\n// single slide\nfunction singleSlideSet() {\n\t$('.js-slide').not('.slick-initialized').slick({\n\t\tarrows: true,\n\t\tautoplay: true,\n\t\tinfinite: true,\n\t\ttouchThreshold: 15,\n\t\tslidesToShow: 1,\n\t\tcenterMode: true,\n\t\tautoplaySpeed: 2500,\n\t\tpauseOnFocus: false,\n\t\tpauseOnHover: false,\n\t\tcenterPadding: '0',\n\t\tspeed: 800,\n\t\tdots: true,\n\t\tprevArrow: '<button class=\"m_slick__arrow m_slick__arrow_prev\"></button>',\n    nextArrow: '<button class=\"m_slick__arrow m_slick__arrow_next\"></button>',\n\t\tresponsive: [\n\t\t\t{\n\t\t\t\tbreakpoint: 750,\n\t\t\t\tsettings: {\n\t\t\t\t\tslidesToShow: 1,\n\t\t\t\t\tcenterPadding: '0'\n\t\t\t\t}\n\t\t\t}\n\t\t],\n\t\tcustomPaging: function(slick, index){\n\t\t\tvar num = slick.$slides.eq(index).html();\n\t\t\treturn '<div class=\"slick-dots__dot m_slick__dot\">' + '</div>';\n\t\t}\n\t});\n};"], "file": "slide.min.js"}