{"version": 3, "names": [], "mappings": "", "sources": ["contact/mm.validation.js"], "sourcesContent": ["var Mm = Mm || {};\n\n(function() {\n\tvar Obj = {};\n\n\tObj.check = function(funcName, value, fld_name, option, form) {\n\t\tvar result = false;\n\n\t\tvar func = this[funcName];\n\n\t\t// 用意されているチェックのみを実施\n\t\tif (func && (typeof(func) === 'function')) {\n\t\t\tresult = func(value, fld_name, option, form);\n\t\t}\n\n\t\treturn result;\n\t};\n\n\t/* check関数\n\t * チェックNGの場合、true（エラーあり）\n\t * チェックOKの場合、false\n\t --------------------------------------------*/\n\t/**\n\t * 未入力チェック\n\t *\n\t */\n\tObj.is_empty = function(str) {\n\t\tif (str) {\n\t\t\tif (Obj.trim(str) !== '') {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\treturn true;\n\t};\n\n\t/**\n\t * 未入力チェック\n\t * ※いずれかの項目に値がセットされているかのcheck（すべて未入力の場合エラー）\n\t *\n\t */\n\tObj.is_empty_all = function(str, fld_name, option, form) {\n\n\t\tvar fieldName, tmpStr;\n\n\t\t// optionに指定されたフィールドのチェック\n\t\t// optionに判定対象のフィールドがセットされているか\n\t\t// ※要素名 'key' にセットされている\n\t\tif (option && option['key']) {\n\t\t\t// 配列で指定されている場合\n\t\t\tif (Obj.isArray(option['key'])) {\n\n\t\t\t\tfor(var i=0,imax=option['key'].length; i<imax; i++) {\n\t\t\t\t\tfieldName = option['key'][i];\n\t\t\t\t\ttmpStr    = form[fieldName].value;\n\n\t\t\t\t\tif (!Obj.is_empty(tmpStr)) {\n\t\t\t\t\t\treturn false;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t// １つのみの場合\n\t\t\t} else if (typeof(option['key']) === 'string') {\n\n\t\t\t\tfieldName = option['key'];\n\t\t\t\ttmpStr    = form[fieldName].value;\n\n\t\t\t\tif (!Obj.is_empty(tmpStr)) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\n\t\t// 該当フィールドのチェック\n\t\tif (!Obj.is_empty(str)) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t};\n\n\t/**\n\t * 未入力チェック\n\t * ※すべての項目に値がセットされているかのcheck（１つでも未入力がある場合エラー）\n\t *\n\t */\n\tObj.is_empty_any = function(str, fld_name, option, form) {\n\n\t\tvar fieldName, tmpStr;\n\n\t\t// optionに指定されたフィールドのチェック\n\t\t// optionに判定対象のフィールドがセットされているか\n\t\t// ※要素名 'key' にセットされている\n\t\tif (option && option['key']) {\n\t\t\t// 配列で指定されている場合\n\t\t\tif (Obj.isArray(option['key'])) {\n\n\t\t\t\tfor(var i=0,imax=option['key'].length; i<imax; i++) {\n\t\t\t\t\tfieldName = option['key'][i];\n\t\t\t\t\ttmpStr    = form[fieldName].value;\n\n\t\t\t\t\tif (Obj.is_empty(tmpStr)) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t// １つのみの場合\n\t\t\t} else if (typeof(option['key']) === 'string') {\n\n\t\t\t\tfieldName = option['key'];\n\t\t\t\ttmpStr    = form[fieldName].value;\n\n\t\t\t\tif (Obj.is_empty(tmpStr)) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\n\t\t}\n\n\t\t// 該当フィールドのチェック\n\t\tif (Obj.is_empty(str)) {\n\t\t\treturn true;\n\t\t}\n\n\t\treturn false;\n\t};\n\n\n\t/**\n\t * 未入力チェック\n\t * ※相関チェック／指定されたフィールドの値が、指定された値の場合のみ必須\n\t *\n\t */\n\tObj.is_empty_selected = function(str, fld_name, option, form) {\n\n\t\tvar fieldName, fieldValue, tmpStr;\n\n\t\t// optionに指定されたフィールドのチェック\n\t\t// optionに判定対象のフィールドがセットされているか\n\t\t// ※要素名 'key' にセットされている\n\t\tif (option && option['key'] && option['value']) {\n\n\t\t\tfieldName  = option['key'];\n\t\t\tfieldValue = option['value'];\n\n\t\t\ttmpStr    = form[fieldName].value;\n\n\t\t\tif (tmpStr != fieldValue) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t}\n\n\t\t// 該当フィールドのチェック\n\t\tif (Obj.is_empty(str)) {\n\t\t\treturn true;\n\t\t}\n\n\t\treturn false;\n\t};\n\n\n\t/**\n\t * 全角カタカナチェック\n\t *\n\t */\n\tObj.is_not_katakana = function(str) {\n\t\t// Unicode : 0x30a0 ～ 0x30ff\n\t\t// if (Obj.is_empty(str) || str.match(/^[\\u30A0-\\u30FF]+$/)) {\n\t\tif (Obj.is_empty(str) || (Obj.convertHankana2Zenkana(str) + '').match(/^[ァ-ヶー・　 ]+$/)) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t};\n\n\t/**\n\t * 全角ひらがなチェック\n\t *\n\t */\n\tObj.is_not_hiragana = function(str) {\n\t\t// Unicode : 0x3040 ～ 0x309f\n\t\t// if (Obj.is_empty(str) || str.match(/^[\\u3040-\\u309f]+$/)) {\n\t\tif (Obj.is_empty(str) || str.match(/^[ぁ-んー・　 ]+$/)) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t};\n\n\t/**\n\t * 電話番号形式チェック\n\t *\n\t */\n\tObj.is_invalid_phone = function(str) {\n\t\tif (Obj.is_empty(str) || Obj.convertZen2Han(str).match(/^(\\+)?([0-9]+[-]?){2,}[0-9]+$/)) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t};\n\n\t/**\n\t * 電話番号形式チェック（上桁と中桁と下桁で分かれている場合）\n\t * ※option['key'] = array('中桁のフィールドキー', '下桁のフィールドキー')の形式で渡されること\n\t *\n\t */\n\tObj.is_invalid_phone_separate = function(str, fld_name, option, form) {\n\n\t\tvar fieldName;\n\t\tvar phoneno = Obj.convertZen2Han(str);\n\n\t\tif (option && option['key'] && Obj.isArray(option['key'])) {\n\t\t\tfor(var i=0,imax=option['key'].length; i<imax; i++) {\n\t\t\t\tfieldName = option['key'][i];\n\t\t\t\tphoneno += '-' + Obj.convertZen2Han(form[fieldName].value);\n\t\t\t}\n\t\t}\n\n\t\treturn Obj.is_valid_phone(phoneno);\n\t};\n\n\t/**\n\t * 郵便番号形式チェック\n\t *\n\t */\n\tObj.is_invalid_zipcode = function(str) {\n\n\t\tif (Obj.is_empty(str) || Obj.convertZen2Han(str).match(/^([0-9]+[-]?)[0-9]+$/)) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t};\n\n\t/**\n\t * 郵便番号形式チェック（上桁と下桁で分かれている場合）\n\t * ※option['key'] = '下桁のフィールドキー'の形式で渡されること\n\t *\n\t */\n\tObj.is_invalid_zipcode_separate = function(str, fld_name, option, form) {\n\n\t\tvar fieldName;\n\t\tvar zipcode = Obj.convertZen2Han(str);\n\n\t\tif (!zipcode) {\n\t\t\treturn false;\n\t\t}\n\n\t\tif (option && option['key'] && !Obj.isArray(option['key'])) {\n\t\t\tfieldName = option['key'];\n\t\t\tzipcode += '-' + Obj.convertZen2Han(form[fieldName].value);\n\t\t}\n\n\t\treturn Obj.is_invalid_zipcode(zipcode);\n\t};\n\n\t/**\n\t * 数字チェック\n\t *\n\t */\n\tObj.is_invalid_numeric = function(str) {\n\t\tif (Obj.is_empty(str) || Obj.convertZen2Han(str+'').match(/^[0-9]+$/)) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t};\n\n\t/**\n\t * 正の整数チェック\n\t *\n\t */\n\tObj.is_invalid_positive_integer = function(str) {\n\t\tif (Obj.is_empty(str) || Obj.convertZen2Han(str+'').match(/^[1-9][0-9]*$/)) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t};\n\n\t/**\n\t * 英字チェック\n\t *\n\t */\n\tObj.is_invalid_alphabet = function(str) {\n\t\tif (Obj.is_empty(str) || Obj.convertZen2Han(str+'').match(/^[a-zA-Z]+$/)) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t};\n\n\t/**\n\t * 英字記号チェック\n\t *\n\t */\n\tObj.is_invalid_alphabet_ext = function(str) {\n\t\tif (Obj.is_empty(str) || Obj.convertZen2Han(str+'').match(/^[a-zA-Z!-~ ]+$/)) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t};\n\n\t/**\n\t * 英数字チェック\n\t *\n\t */\n\tObj.is_invalid_alphanum = function(str) {\n\t\tif (Obj.is_empty(str) || Obj.convertZen2Han(str+'').match(/^[a-zA-Z0-9]+$/)) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t};\n\n\t/**\n\t * 英数字チェック\n\t *\n\t */\n\tObj.is_invalid_alphanum = function(str) {\n\t\tif (Obj.is_empty(str) || Obj.convertZen2Han(str+'').match(/^[a-zA-Z0-9]+$/)) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t};\n\n\t/**\n\t * 英数字記号チェック\n\t *\n\t */\n\tObj.is_invalid_alphanum_ext = function(str) {\n\t\tif (Obj.is_empty(str) || Obj.convertZen2Han(str+'').match(/^[a-zA-Z0-9!-~ ]+$/)) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t};\n\n\t/**\n\t * メールアドレス形式チェック\n\t *\n\t */\n\tObj.is_invalid_mailaddress = function(str) {\n\n\t\tif (Obj.is_empty(str) || Obj.convertZen2Han(str).match(/^[A-Za-z0-9]+[\\w\\.\\+-]+@[\\w\\.-]{2,}\\.\\w{2,}$/)) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t};\n\n\t/**\n\t * 同値チェック\n\t * ※option['key'] = '比較対象のフィールド名'の形式で渡されること\n\t *\n\t */\n\tObj.is_not_equal = function(str, fld_name, option, form) {\n\n\t\tvar fieldName;\n\t\tvar str2 = '';\n\n\t\tif (option && option['key'] && !Obj.isArray(option['key'])) {\n\t\t\tfieldName = option['key'];\n\t\t\tstr2 = form[fieldName].value;\n\t\t}\n\n\t\tif (Obj.convertZen2Han(str) === Obj.convertZen2Han(str2)) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t};\n\n\t/**\n\t * 環境依存文字・旧漢字などJISに変換できない文字チェック\n\t * 機種依存文字を含む場合、falseを返す（エラー）\n\t *\n\t * ※※ !!! 簡易チェックのみ !!! ※※※\n\t *\n\t */\n\tObj.is_invalid_jis = function(str) {\n\n\t\tvar search_txt = \"[①②③④⑤⑥⑦⑧⑨⑩⑪⑫⑬⑭⑮⑯⑰⑱⑲⑳ⅠⅡⅢⅣⅤⅥⅦⅧⅨⅩ㍉㌔㌢㍍㌘㌧㌃㌶㍑㍗㌍㌦㌣㌫㍊㌻㎜㎝㎞㎎㎏㏄㎡㍻〝〟№㏍℡㊤㊥㊦㊧㊨㈱㈲㈹㍾㍽㍼]\";\n\n\t\tif (!Obj.is_empty(str) && str.match(search_txt)) {\n\t\t\treturn true;\n\t\t}\n\n\t\treturn false;\n\t};\n\n\t/**\n\t * YYYY-MM-DD形式かチェック\n\t *\n\t */\n\tObj.is_invalid_ymd = function(str) {\n\n\t\tif (Obj.is_empty(str)) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// 正規表現による書式チェック\n\t\tstr = str.replace(/\\//g, \"-\");\n\t\tmatch = str.match(/^(\\d{4})\\-(\\d{1,2})\\-(\\d{1,2})$/);\n\n\t\tif(!match) {\n\t\t\treturn true;\n\t\t}\n\n\t\tvar year  = parseInt(match[1], 10);\n\t\tvar month = parseInt(match[2], 10) - 1; // Javascriptは、0-11で表現\n\t\tvar day   = parseInt(match[3], 10);\n\n\t\t// 月,日の妥当性チェック\n\t\tif(month >= 0 && month <= 11 && day >= 1 && day <= 31) {\n\t\t\tvar date = new Date(year, month, day);\n\t\t\tif(isNaN(date)) {\n\t\t\t\treturn true;\n\t\t\t} else if( (date.getFullYear() !== year) || (date.getMonth() !== month) || (date.getDate() !== day) ) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t} else {\n\t\t\treturn true;\n\t\t}\n\n\t\treturn false;\n\n\t};\n\n\t/**\n\t * 指定されたフィールド（年・月・日が別のフィールド）が日付形式かのチェック\n\t * ※option['y'] = '年のフィールド名'、option['m'] = '月のフィールド名'、option['d'] = '日のフィールド名'、の形式で渡されることが前提\n\t *\n\t */\n\tObj.is_invalid_ymd_array = function(str, fld_name, option, form) {\n\n\t\tvar fieldNameY, fieldNameM, fieldNameD;\n\n\t\tif (option && option['y'] && option['m'] && option['d'] ) {\n\n\t\t\tfieldNameY = option['y'];\n\t\t\tfieldNameM = option['m'];\n\t\t\tfieldNameD = option['d'];\n// console.log(form[fieldNameY].value + '-' + form[fieldNameM].value + '-' + form[fieldNameD].value);\n\t\t\tif (Obj.is_empty(form[fieldNameY].value) || Obj.is_empty(form[fieldNameM].value) || Obj.is_empty(form[fieldNameD].value)) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\treturn Obj.is_invalid_ymd(form[fieldNameY].value + '-' + form[fieldNameM].value + '-' + form[fieldNameD].value);\n\t\t}\n\n\t\treturn false;\n\n\t};\n\n\t/**\n\t * 指定されたフィールド（年・月が別のフィールド）が年月形式かのチェック\n\t * ※option['y'] = '年のフィールド名'、option['m'] = '月のフィールド名'、の形式で渡されることが前提\n\t *\n\t */\n\tObj.is_invalid_ym = function(str, fld_name, option, form) {\n\n\t\tvar fieldNameY, fieldNameM, fieldNameD;\n\n\t\tif (option && option['y'] && option['m']) {\n\n\t\t\tfieldNameY = option['y'];\n\t\t\tfieldNameM = option['m'];\n\n\t\t\tif (!Obj.is_empty(form[fieldNameY].value + form[fieldNameM].value)) {\n\t\t\t\treturn Obj.is_invalid_ymd(form[fieldNameY].value + '-' + form[fieldNameM].value + '-01');\n\t\t\t} else {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\n\t};\n\n\t/**\n\t * 最大文字数チェック\n\t *\n\t */\n\tObj.is_over_max_length = function(str, fld_name, option) {\n\n\t\tif (!Obj.is_empty(str) && option && option['length'] && !Obj.is_invalid_numeric(option['length'])) {\n\t\t\tvar length = parseInt(option['length'], 10);\n\t\t\tif (str.length > length) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t};\n\n\t/**\n\t * 最大文字数チェック（全角を1文字、半角を0.5文字として換算）\n\t *\n\t */\n/**\n\tObj.is_over_max_length_custom = function(str, option) {\n\t\tif (!Obj.is_empty(str) && option && option['length'] && !Obj.is_invalid_numeric(option['length'])) {\n\n\t\t}\n\n\t\treturn false;\n\t};\n*/\n\n\t/**\n\t * 最大数チェック\n\t *\n\t */\n\tObj.is_over_max_num = function(str, fld_name, option) {\n\t\tif (!Obj.is_empty(str) && option && option['max'] && !Obj.is_invalid_numeric(option['max'])) {\n\t\t\tvar max = parseInt(option['max'], 10);\n\t\t\tvar num = parseInt(str, 10);\n\n\t\t\tif (num > max) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t};\n\n\t/**\n\t * 最大バイト数チェック\n\t *\n\t */\n\tObj.is_over_max_byte = function(str, fld_name, option) {\n\t\tif (!Obj.is_empty(str) && option && option['length'] && !Obj.is_invalid_numeric(option['length'])) {\n\t\t\tvar length = parseInt(option['length'], 10);\n\n\t\t\t// encodeURIでパーセントエンコード\n\t\t\tstr = encodeURI(str);\n\t\t\t// 全角文字が%XXのようになるので、それを半角1文字に変換した文字列を作成し、バイト数を数える\n\t\t\tstr = str.replace(/%[0-9A-F]{2}/g, '*');\n\n\t\t\tif (str.length > length) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t}\n\n\t\treturn false;\n\t};\n\n\t/**\n\t * 文字数チェック\n\t *\n\t */\n\tObj.is_not_equal_length = function(str, fld_name, option) {\n\t\tif (!Obj.is_empty(str) && option && option['length'] && !Obj.is_invalid_numeric(option['length'])) {\n\t\t\tvar length = parseInt(option['length'], 10);\n\t\t\tif (str.length !== length) {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t}\n\n\t\treturn false;\n\t};\n\n\t/**\n\t * 正規表現に合致するかのチェック\n\t *\n\t */\n\tObj.is_invalid_pattern = function(str, fld_name, option) {\n\n\t\tif (Obj.is_empty(str)) {\n\t\t\treturn false;\n\t\t}\n\n\t\tif (option && option['pattern'] && !Obj.isArray(option['pattern'])) {\n\t\t\tvar pattern = option['pattern'];\n\t\t\tvar flg = option['flg']?option['flg']:'';\n\t\t\tvar regexp  = new RegExp(pattern, flg);\n\n\t\t\tif (str.match(regexp)) {\n\t\t\t\treturn false;\n\t\t\t} else {\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t} else {\n\t\t\treturn false;\n\n\t\t}\n\n\t}\n\n\t// 拡張チェック ///////////////////////////////////\n\t/**\n\t * チェックボックスが選択されているかのチェック\n\t *\n\t */\n\tObj.is_not_checked = function(check, fld_name, option) {\n\n\t\tif (Obj.isArray(check)) {\n\t\t\tif (check.length > 0) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t} else if (Obj.trim(check)) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t}\n\n\n\t/* utility関数\n\t --------------------------------------------*/\n\t/**\n\t * trim関数\n\t *\n\t */\n\tObj.trim = function(str) {\n\t\treturn (str + '').replace(/(^[\\s　]+)|([\\s　]+$)/g, \"\");\n\t};\n\n\t/**\n\t * 配列かどうかのチェック\n\t *\n\t */\n\tObj.isArray = function(arr) {\n\t    return Object.prototype.toString.call(arr) === '[object Array]';\n\t};\n\n\t/**\n\t * 半角カタカナを全角カタカナに変換\n\t *\n\t */\n\tObj.convertHankana2Zenkana = function (str) {\n\t\tif (Obj.is_empty(str)) {\n\t\t\treturn str;\n\t\t}\n\n\t\tvar kanaMap = {\n\t\t\t'ｶﾞ': 'ガ', 'ｷﾞ': 'ギ', 'ｸﾞ': 'グ', 'ｹﾞ': 'ゲ', 'ｺﾞ': 'ゴ',\n\t\t\t'ｻﾞ': 'ザ', 'ｼﾞ': 'ジ', 'ｽﾞ': 'ズ', 'ｾﾞ': 'ゼ', 'ｿﾞ': 'ゾ',\n\t\t\t'ﾀﾞ': 'ダ', 'ﾁﾞ': 'ヂ', 'ﾂﾞ': 'ヅ', 'ﾃﾞ': 'デ', 'ﾄﾞ': 'ド',\n\t\t\t'ﾊﾞ': 'バ', 'ﾋﾞ': 'ビ', 'ﾌﾞ': 'ブ', 'ﾍﾞ': 'ベ', 'ﾎﾞ': 'ボ',\n\t\t\t'ﾊﾟ': 'パ', 'ﾋﾟ': 'ピ', 'ﾌﾟ': 'プ', 'ﾍﾟ': 'ペ', 'ﾎﾟ': 'ポ',\n\t\t\t'ｳﾞ': 'ヴ', 'ﾜﾞ': 'ヷ', 'ｦﾞ': 'ヺ',\n\t\t\t'ｱ': 'ア', 'ｲ': 'イ', 'ｳ': 'ウ', 'ｴ': 'エ', 'ｵ': 'オ',\n\t\t\t'ｶ': 'カ', 'ｷ': 'キ', 'ｸ': 'ク', 'ｹ': 'ケ', 'ｺ': 'コ',\n\t\t\t'ｻ': 'サ', 'ｼ': 'シ', 'ｽ': 'ス', 'ｾ': 'セ', 'ｿ': 'ソ',\n\t\t\t'ﾀ': 'タ', 'ﾁ': 'チ', 'ﾂ': 'ツ', 'ﾃ': 'テ', 'ﾄ': 'ト',\n\t\t\t'ﾅ': 'ナ', 'ﾆ': 'ニ', 'ﾇ': 'ヌ', 'ﾈ': 'ネ', 'ﾉ': 'ノ',\n\t\t\t'ﾊ': 'ハ', 'ﾋ': 'ヒ', 'ﾌ': 'フ', 'ﾍ': 'ヘ', 'ﾎ': 'ホ',\n\t\t\t'ﾏ': 'マ', 'ﾐ': 'ミ', 'ﾑ': 'ム', 'ﾒ': 'メ', 'ﾓ': 'モ',\n\t\t\t'ﾔ': 'ヤ', 'ﾕ': 'ユ', 'ﾖ': 'ヨ',\n\t\t\t'ﾗ': 'ラ', 'ﾘ': 'リ', 'ﾙ': 'ル', 'ﾚ': 'レ', 'ﾛ': 'ロ',\n\t\t\t'ﾜ': 'ワ', 'ｦ': 'ヲ', 'ﾝ': 'ン',\n\t\t\t'ｧ': 'ァ', 'ｨ': 'ィ', 'ｩ': 'ゥ', 'ｪ': 'ェ', 'ｫ': 'ォ',\n\t\t\t'ｯ': 'ッ', 'ｬ': 'ャ', 'ｭ': 'ュ', 'ｮ': 'ョ',\n\t\t\t'｡': '。', '､': '、', 'ｰ': 'ー', '｢': '「', '｣': '」', '･': '・'\n\t\t};\n\n\t\tvar reg = new RegExp('(' + Object.keys(kanaMap).join('|') + ')', 'g');\n\n\t\treturn str.replace(reg, function (match) {\n\t\t\t\treturn kanaMap[match];\n\t\t\t})\n\t\t\t.replace(/ﾞ/g, '゛')\n\t\t\t.replace(/ﾟ/g, '゜');\n\t};\n\n\t/**\n\t * 全角から半角に置き換え\n\t *\n\t * 全角チルダ、全角波ダッシュ共に半角チルダに変換\n\t * 全角ハイフン、全角ダッシュ、全角マイナス記号は半角ハイフンに変換\n\t * 長音符は半角ハイフンに含めない（住所の地名等に使用される為）\n\t *\n\t * 今は良いがUnicode 8.0で波ダッシュの形が変わるみたいなので、波ダッシュを変換に\n\t * 含めるべきかどうかは検討が必要\n\t *\n\t */\n\tObj.convertZen2Han = function(str) {\n\t\t// 英字変換\n\t\tstr = str.replace(/[Ａ-Ｚａ-ｚ]/g, function (s) {\n\t\t\t\treturn String.fromCharCode(s.charCodeAt(0) - 65248);\n\t\t\t});\n\n\t\t// 数字変換\n\t\tstr = str.replace(/[０-９]/g, function (s) {\n\t\t\t\treturn String.fromCharCode(s.charCodeAt(0) - 65248);\n\t\t\t});\n\n\t\t// 記号変換\n\t\tvar reg = /[！＂＃＄％＆＇（）＊＋，－．／：；＜＝＞？＠［＼］＾＿｀｛｜｝]/g;\n\t\tstr = str.replace(reg, function (s) {\n\t\t\t\treturn String.fromCharCode(s.charCodeAt(0) - 65248);\n\t\t\t}).replace(/[‐－―−]/g, '-');\n\n\t\t// チルダ変換\n\t\tstr = str.replace(/[～〜]/g, '~');\n\t\t// スペース変換\n\t\tstr = str.replace(/　/g, ' ');\n\n\t\treturn str;\n\t};\n\n\t/**\n\t * 半角カタカナを全角カタカナに変換\n\t *\n\t */\n\tObj.convertJIS = function (str) {\n\t\tif (Obj.is_empty(str)) {\n\t\t\treturn str;\n\t\t}\n\n\t\tvar jisMap = {\n\t\t\t'㈱': '(株)', '㈲': '(有)', '㈹': '(代)'\n\t\t};\n\n\t\tvar reg = new RegExp('(' + Object.keys(jisMap).join('|') + ')', 'g');\n\n\t\treturn str.replace(reg, function (match) {\n\t\t\t\treturn jisMap[match];\n\t\t\t});\n\t};\n\n\tMm.Validation = Obj;\n})();\n\n"], "file": "mm.validation.min.js"}