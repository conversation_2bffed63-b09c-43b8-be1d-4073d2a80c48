<?php
// domain path
$_DOMAIN  = "https://hc.kowa.com";
$_PARSEURL = parse_url($_DOMAIN.$_SERVER["REQUEST_URI"]);
$_URL = $_DOMAIN.$_PARSEURL['path'];

// ページパス
$_PAGEPATH = "";

define('SITE_ROOT', substr( $_SERVER["SCRIPT_FILENAME"], 0, - strlen( $_SERVER["SCRIPT_NAME"] )));
include (SITE_ROOT . '/_include//analytics/analytics_head.php');

?>
<meta charset="UTF-8">
<meta content="IE=edge" http-equiv="X-UA-Compatible">
<meta name="viewport" content="width=device-width,initial-scale=1,viewport-fit=cover">
<title><?php echo $_PAGETITLE; ?></title>
<meta name="description" content="<?php echo $_DESCRIPTION; ?>">
<meta property="og:title" content="<?php echo $_PAGETITLE; ?>">
<meta property="og:description" content="<?php echo $_DESCRIPTION; ?>">
<meta property="og:url" content="<?php echo $_URL;?>">
<meta property="og:image" content="<?php echo $_DOMAIN;?>/assets/images/kowa_ogp.jpg">
<meta property="og:type" content="website">
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:description" content="<?php echo $_DESCRIPTION; ?>">
<meta name="twitter:title" content="<?php echo $_PAGETITLE; ?>">
<meta name="twitter:image" content="<?php echo $_DOMAIN;?>/assets/images/kowa_ogp.jpg">
<meta name="format-detection" content="telephone=no">
<link rel="canonical" href="<?php echo $_URL;?>">
<link rel="stylesheet" href="//fonts.googleapis.com/css?family=Noto+Sans+JP:300,400,700&display=swap&subset=japanese">
<link rel="stylesheet" href="//fonts.googleapis.com/css?family=Noto+Serif+JP:400&display=swap&subset=japanese">
<link rel="stylesheet" href="/assets/css/base.css?v=25">
<!-- favicon wako -->
<link rel="shortcut icon" href="/assets/images/favicon.ico" type="image/vnd.microsoft.icon" />
<meta name="google-site-verification" content="">
<?php if ($_PAGE_ID == 'vantelin') :?>
<link rel="alternate" hreflang="en" href="<?php echo $_DOMAIN;?>/vantelin/">
<link rel="alternate" hreflang="vi" href="<?php echo $_DOMAIN;?>/vnm/vantelin/">
<link rel="alternate" hreflang="en-SG" href="<?php echo $_DOMAIN;?>/sgp/vantelin/">
<link rel="alternate" hreflang="en-MY" href="<?php echo $_DOMAIN;?>/mys/en/vantelin/">
<link rel="alternate" hreflang="id" href="<?php echo $_DOMAIN;?>/idn/vantelin/">
<link rel="alternate" hreflang="x-default" href="<?php echo $_DOMAIN;?>/vantelin/">
<link rel="alternate" hreflang="zh-HK" href="<?php echo $_DOMAIN;?>/hkg/vantelin/">
<link rel="alternate" hreflang="en-PH" href="<?php echo $_DOMAIN;?>/phl/vantelin/">
<?php /*?>
<link rel="alternate" hreflang="ms-MY" href="<?php echo $_DOMAIN;?>/mys/vantelin/">
<link rel="alternate" hreflang="th" href="<?php echo $_DOMAIN;?>/tha/vantelin/"><?php */?>

<?php elseif ($_PAGE_ID == 'dimension_mask') :?>
<link rel="alternate" hreflang="en" href="<?php echo $_DOMAIN;?>/three-dimension-mask/">

<link rel="alternate" hreflang="vi" href="<?php echo $_DOMAIN;?>/vnm/three-dimension-mask/">
<link rel="alternate" hreflang="en-MY" href="<?php echo $_DOMAIN;?>/mys/en/three-dimension-mask/">
<link rel="alternate" hreflang="en-PH" href="<?php echo $_DOMAIN;?>/phl/three-dimension-mask/">
<link rel="alternate" hreflang="x-default" href="<?php echo $_DOMAIN;?>/three-dimension-mask/">
<link rel="alternate" hreflang="zh-HK" href="<?php echo $_DOMAIN;?>/hkg/three-dimension-mask/">
<?php /*?>
<link rel="alternate" hreflang="en-SG" href="<?php echo $_DOMAIN;?>/sgp/three-dimension-mask/">
<link rel="alternate" hreflang="ms-MY" href="<?php echo $_DOMAIN;?>/mys/three-dimension-mask/">
<link rel="alternate" hreflang="id" href="<?php echo $_DOMAIN;?>/idn/three-dimension-mask/">
<link rel="alternate" hreflang="th" href="<?php echo $_DOMAIN;?>/tha/three-dimension-mask/">
<?php */?>


<?php else :?>
<link rel="alternate" hreflang="en" href="<?php echo $_DOMAIN;?>/">
<link rel="alternate" hreflang="en-SG" href="<?php echo $_DOMAIN;?>/sgp/">
<link rel="alternate" hreflang="vi" href="<?php echo $_DOMAIN;?>/vnm/">

<link rel="alternate" hreflang="en-MY" href="<?php echo $_DOMAIN;?>/mys/en/">
<link rel="alternate" hreflang="id" href="<?php echo $_DOMAIN;?>/idn/">
<link rel="alternate" hreflang="en-PH" href="<?php echo $_DOMAIN;?>/phl/">
<link rel="alternate" hreflang="zh-HK" href="<?php echo $_DOMAIN;?>/hkg/">
<link rel="alternate" hreflang="x-default" href="<?php echo $_DOMAIN;?>/">
<?php /*?>
<link rel="alternate" hreflang="ms-MY" href="<?php echo $_DOMAIN;?>/mys/">
<link rel="alternate" hreflang="th" href="<?php echo $_DOMAIN;?>/tha/">
<?php */?>
<?php endif; ?>
<!-- Adobe font -->
<?php /*?>
<script>
  (function(d) {
    var config = {
      kitId: 'zfn7dpd',
      scriptTimeout: 3000,
      async: true
    },
    h=d.documentElement,t=setTimeout(function(){h.className=h.className.replace(/\bwf-loading\b/g,"")+" wf-inactive";},config.scriptTimeout),tk=d.createElement("script"),f=false,s=d.getElementsByTagName("script")[0],a;h.className+=" wf-loading";tk.src='https://use.typekit.net/'+config.kitId+'.js';tk.async=true;tk.onload=tk.onreadystatechange=function(){a=this.readyState;if(f||a&&a!="complete"&&a!="loaded")return;f=true;clearTimeout(t);try{Typekit.load(config)}catch(e){}};s.parentNode.insertBefore(tk,s)
  })(document);
</script>
<?php */?>
