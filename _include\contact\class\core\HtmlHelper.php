<?php
/**
 *
 *
 */

class HtmlHelper {

	/**
	 *
	 *
	 *
	 */
	// public static function


	/**
	 * selectboxの生成
	 *
	 */
	public static function selectbox($field_name, $field_list, $list, $show_default=false, $default_label='-', $attributes=false) {

		$html = '';

		// Attributeの取得
		$code_attr = '';
		if ($attributes && is_array($attributes)) {
			foreach($attributes as $attr_key => $attr_val) {
				$code_attr .= ' ' . $attr_key . '="' . $attr_val . '"';
			}
		}

		if ($list && is_array($list)) {
			$html = sprintf('<select name="%s"%s>', $field_name, $code_attr);

			if ($show_default) {
				$html .= sprintf('<option value="">%s</option>', $default_label);
			}

			foreach($list as $value => $label) {
				$selected = ($value == $field_list[$field_name])?' selected="selected"':'';
				$html .= sprintf('<option value="%s"%s>%s</option>', $value, $selected, $label);
			}
			$html .= '</select>';
		}

		return $html;
	}

	/**
	 * selectboxの生成（$listの1要素が連想配列の場合）
	 *
	 */
	public static function selectbox4associative($field_name, $field_list, $list, $label_id, $show_default=false, $default_label='-', $attributes=false) {

		$html = '';

		// Attributeの取得
		$code_attr = '';
		if ($attributes && is_array($attributes)) {
			foreach($attributes as $attr_key => $attr_val) {
				$code_attr .= ' ' . $attr_key . '="' . $attr_val . '"';
			}
		}

		if ($list && is_array($list)) {
			$html = sprintf('<select name="%s"%s>', $field_name, $code_attr);

			if ($show_default) {
				$html .= sprintf('<option value="">%s</option>', $default_label);
			}

			foreach($list as $value => $data) {
				$selected = ($value == $field_list[$field_name])?' selected="selected"':'';
				if (array_key_exists($label_id, $data)) {
					$html .= sprintf('<option value="%s"%s>%s</option>', $value, $selected, $data[$label_id]);
				}
			}
			$html .= '</select>';
		}

		return $html;
	}

	/**
	 * radioの生成
	 *
	 */
	public static function radiobutton($field_name, $field_list, $list, $wrap=false, $attributes=false) {

		$html = '';

		// wrapするタグの取得
		$wrap_start = '';
		$wrap_end = '';
		if ($wrap) {
			$code_wrap_attr = '';
			if ($attributes && is_array($attributes) && array_key_exists('wrap', $attributes) && is_array($attributes['wrap'])) {
				foreach($attributes['wrap'] as $attr_key => $attr_val) {
					$code_wrap_attr .= ' ' . $attr_key . '="' . $attr_val . '"';
				}
			}

			$wrap_start = '<' . $wrap . $code_wrap_attr . '>';
			$wrap_end   = '</' . $wrap . '>';
		}

		// Attributeの取得
		$code_attr = '';
		if ($attributes && is_array($attributes)) {
			foreach($attributes as $attr_key => $attr_val) {
				if ($attr_key === 'lable') {
					confinue;
				}
				$code_attr .= ' ' . $attr_key . '="' . $attr_val . '"';
			}
		}

		// リスト要素数LOOP
		if ($list && is_array($list)) {
			foreach($list as $value => $label) {
				$checked = ($value == $field_list[$field_name])?' checked="checked"':'';
				$field_id = $field_name . '_' . $value;

				$code_value = htmlspecialchars($value);
				$code_label = htmlspecialchars($label);

				$code_label_attr = '';
				if ($attributes && is_array($attributes) && array_key_exists('label', $attributes) && is_array($attributes['label'])) {
					foreach($attributes['label'] as $attr_key => $attr_val) {
						$code_label_attr .= ' ' . $attr_key . '="' . $attr_val . '"';
					}
				}

				$html .= <<<CODE
${wrap_start}<label for="${field_id}" onclick="" {$code_label_attr}><input type="radio" name="${field_name}" id="${field_id}" value="${code_value}" data-target-id="${field_name}"${code_attr}${checked}><span>${code_label}</span></label>${wrap_end}
CODE;
			}
		}

		return $html;
	}


	/**
	 * checkboxの生成
	 *
	 */
	public static function checkbox($field_name, $field_list, $list, $is_multi=false, $wrap=false, $attributes=false) {

		$html = '';

		// wrapするタグの取得
		$wrap_start = '';
		$wrap_end = '';
		if ($wrap) {
			$wrap_start = '<' . $wrap . '>';
			$wrap_end   = '</' . $wrap . '>';
		}

		// Attributeの取得
		$code_attr = '';
		if ($attributes && is_array($attributes)) {
			foreach($attributes as $attr_key => $attr_val) {
				$code_attr .= ' ' . $attr_key . '="' . $attr_val . '"';
			}
		}

		// 選択されている値の取得
		// $field_value = $field_list[$field_name];

		$field_value = HtmlHelper::getSelectedValues4Multi($field_name, $field_list, $list);


		// リスト要素数LOOP
		if ($list && is_array($list)) {

			foreach($list as $value => $label) {

				// チェックされているかどうかの判断
				$checked = '';
				if ($field_value) {
					if (is_array($field_value)) {
						$checked = array_key_exists($value, $field_value)?' checked="checked"':'';
					} else {
						$checked = ($value == $field_value)?' checked="checked"':'';
					}
				}

				// 選択肢１件分のHTMLコードの取得
				$field_id = $field_name . '_' . $value;
				// $field_name_ext = ($is_multi)?($field_name . '[]'):$field_name.'_'.$value;
				$field_name_ext = ($is_multi)?($field_name . '[]'):$field_name;

				$code_value = htmlspecialchars($value);
				$code_label = htmlspecialchars($label);

				$html .= <<<CODE
${wrap_start}<label for="${field_id}" onclick=""><input type="checkbox" name="${field_name_ext}" id="${field_id}" value="${value}" data-target-id="${field_name}"${code_attr}${checked}><span>${label}</span></label>${wrap_end}
CODE;

			}
		}

		return $html;
	}


	/**
	 * checkboxの生成
	 *
	 */
	public static function checkboxAt($field_name, $field_list, $value, $label, $wrap=false, $attributes=false) {

		$html = '';

		// wrapするタグの取得
		$wrap_start = '';
		$wrap_end = '';
		if ($wrap) {
			$wrap_start = '<' . $wrap . '>';
			$wrap_end   = '</' . $wrap . '>';
		}

		// Attributeの取得
		$code_attr = '';
		if ($attributes && is_array($attributes)) {
			foreach($attributes as $attr_key => $attr_val) {
				if ($attr_key == 'id') {
					continue;
				}
				$code_attr .= ' ' . $attr_key . '="' . $attr_val . '"';
			}
		}

		// 選択されている値の取得
		$field_value = $field_list[$field_name];

		$checked = ($value == $field_value)?' checked="checked"':'';

		// 選択肢１件分のHTMLコードの取得
		$field_id = array_key_exists('id', $attributes)?$attributes['id']:$field_name . '_' . $value;

		$code_value = htmlspecialchars($value);
		$code_label = htmlspecialchars($label);

		$html .= <<<CODE
${wrap_start}<label for="${field_id}" onclick=""><input type="checkbox" name="${field_name}" id="${field_id}" value="${value}" data-target-id="${field_name}"${code_attr}${checked}><span>${label}</span></label>${wrap_end}
CODE;

		return $html;
	}


	/**
	 * 選択されているリストの値を取得（selectbox / radiobutton で使用）
	 *
	 */
	public static function getSelectedValues($field_name, $field_list, $list, $default=false) {

		$field_value = $field_list[$field_name];

		if ($list && is_array($list) && array_key_exists($field_value, $list)) {
			return  $list[$field_value];
		} else {
			if ($default) {
				return $default;
			}
		}

		return '';
	}

	/**
	 * 選択されているリストの値を取得（checkboxで使用）
	 *
	 */
	public static function getSelectedValues4Multi($field_name, $field_list, $list) {

		$field_value = $field_list[$field_name];

		$result = array();
		if ($list && is_array($list)) {
			if (is_array($field_value)) {
				if (count($field_value) > 0) {
					foreach($field_value as $idx) {
						if (array_key_exists($idx, $list)) {
							$result[$idx] = $list[$idx];
						}
					}
				}

			} else if (is_string($field_value)) {
				if (array_key_exists($field_value, $list)) {
					$result[$field_value] = $list[$field_value];
				}
			}
		}

		return $result;
	}


}

