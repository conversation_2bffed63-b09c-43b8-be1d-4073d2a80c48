2022/03/31 12:05:59	[error]	ADMIN MAIL COULD NOT SENT.
2022/03/31 12:05:59	[error]	You must provide at least one recipient email address.
2022/03/31 12:06:01	[error]	ADMIN MAIL COULD NOT SENT.
2022/03/31 12:06:01	[error]	You must provide at least one recipient email address.
2022/03/31 12:11:01	[error]	USER MAIL COULD NOT SENT.
2022/03/31 12:11:01	[error]	SMTP connect() failed. https://github.com/PHPMailer/PHPMailer/wiki/Troubleshooting
2022/03/31 12:11:03	[error]	ADMIN MAIL COULD NOT SENT.
2022/03/31 12:11:03	[error]	SMTP connect() failed. https://github.com/PHPMailer/PHPMailer/wiki/Troubleshooting
2022/03/31 12:12:00	[error]	USER MAIL COULD NOT SENT.
2022/03/31 12:12:00	[error]	SMTP connect() failed. https://github.com/PHPMailer/PHPMailer/wiki/Troubleshooting
2022/03/31 12:12:02	[error]	ADMIN MAIL COULD NOT SENT.
2022/03/31 12:12:02	[error]	SMTP connect() failed. https://github.com/PHPMailer/PHPMailer/wiki/Troubleshooting
2022/03/31 12:13:07	[error]	USER MAIL COULD NOT SENT.
2022/03/31 12:13:07	[error]	SMTP connect() failed. https://github.com/PHPMailer/PHPMailer/wiki/Troubleshooting
2022/03/31 12:13:09	[error]	ADMIN MAIL COULD NOT SENT.
2022/03/31 12:13:09	[error]	SMTP connect() failed. https://github.com/PHPMailer/PHPMailer/wiki/Troubleshooting
2022/03/31 12:15:36	[error]	USER MAIL COULD NOT SENT.
2022/03/31 12:15:36	[error]	SMTP connect() failed. https://github.com/PHPMailer/PHPMailer/wiki/Troubleshooting
2022/03/31 12:15:38	[error]	ADMIN MAIL COULD NOT SENT.
2022/03/31 12:15:38	[error]	SMTP connect() failed. https://github.com/PHPMailer/PHPMailer/wiki/Troubleshooting
2022/03/31 12:24:04	[error]	USER MAIL COULD NOT SENT.
2022/03/31 12:24:04	[error]	SMTP connect() failed. https://github.com/PHPMailer/PHPMailer/wiki/Troubleshooting
2022/03/31 12:24:06	[error]	ADMIN MAIL COULD NOT SENT.
2022/03/31 12:24:06	[error]	SMTP connect() failed. https://github.com/PHPMailer/PHPMailer/wiki/Troubleshooting
2022/03/31 12:25:34	[error]	USER MAIL COULD NOT SENT.
2022/03/31 12:25:34	[error]	SMTP connect() failed. https://github.com/PHPMailer/PHPMailer/wiki/Troubleshooting
2022/03/31 12:25:35	[error]	ADMIN MAIL COULD NOT SENT.
2022/03/31 12:25:35	[error]	SMTP connect() failed. https://github.com/PHPMailer/PHPMailer/wiki/Troubleshooting
2022/03/31 12:59:16	[error]	USER MAIL COULD NOT SENT.
2022/03/31 12:59:16	[error]	Invalid address:  (punyEncode) mail danh cho user
2022/03/31 12:59:16	[error]	ADMIN MAIL COULD NOT SENT.
2022/03/31 12:59:16	[error]	You must provide at least one recipient email address.
