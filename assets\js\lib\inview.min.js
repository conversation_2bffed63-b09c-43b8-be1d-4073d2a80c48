!function(e){"function"==typeof define&&define.amd?define(["jquery"],e):"object"==typeof exports?module.exports=e(require("jquery")):e(jQuery)}(function(f){function t(){if(c.length){var e=0,t=f.map(c,function(e){var t=e.data.selector,n=e.$element;return t?n.find(t):n});for(a=a||function(){var e,t,n={height:u.innerHeight,width:u.innerWidth};return n.height||(!(e=h.compatMode)&&f.support.boxModel||(n={height:(t="CSS1Compat"===e?s:h.body).clientHeight,width:t.clientWidth})),n}(),d=d||{top:u.pageYOffset||s.scrollTop||h.body.scrollTop,left:u.pageXOffset||s.scrollLeft||h.body.scrollLeft};e<c.length;e++)if(f.contains(s,t[e][0])){var n=f(t[e]),i=n[0].offsetHeight,o=n[0].offsetWidth,l=n.offset(),r=n.data("inview");if(!d||!a)return;l.top+i>d.top&&l.top<d.top+a.height&&l.left+o>d.left&&l.left<d.left+a.width?r||n.data("inview",!0).trigger("inview",[!0]):r&&n.data("inview",!1).trigger("inview",[!1])}}}var a,d,i,c=[],h=document,u=window,s=h.documentElement;f.event.special.inview={add:function(e){c.push({data:e,$element:f(this),element:this}),!i&&c.length&&(i=setInterval(t,250))},remove:function(e){for(var t=0;t<c.length;t++){var n=c[t];if(n.element===this&&n.data.guid===e.guid){c.splice(t,1);break}}c.length||(clearInterval(i),i=null)}},f(u).on("scroll resize scrollstop",function(){a=d=null}),!s.addEventListener&&s.attachEvent&&s.attachEvent("onfocusin",function(){d=null})});