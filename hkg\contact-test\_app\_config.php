<?php
/* --------------------------------------------------
各種設定ファイル
----------------------------------------------------- */

/* 実行環境関連
 -------------------------------------------------- */
// 社外プレビュー環境（dev）
if (strpos($_SERVER['SERVER_NAME'], 'mighty2.net') !== false) {
	define('APP_ENV', 'develop');
	define('FORCE_SSL', false);
	define('STATIC_HOME', '/');
	define('SECURE_HOME', '/');

// 社内開発環境
} else if (strpos($_SERVER['SERVER_NAME'], 'mighty2.com') !== false) {
	define('APP_ENV', 'develop-inhouse');
	define('FORCE_SSL', false);
	define('STATIC_HOME', '/');
	define('SECURE_HOME', '/');

// 開発環境（local）
} else if (strpos($_SERVER['SERVER_NAME'], 'local') !== false) {
	define('APP_ENV', 'local');
	define('FORCE_SSL', false);
	define('STATIC_HOME', '/');
	define('SECURE_HOME', '/');

// 本番環境
} else {
	define('APP_ENV', 'publish');
	define('FORCE_SSL', false);
	define('STATIC_HOME', '/');
	define('SECURE_HOME', '/');

}

// ディレクトリの取得
$dir_libs = dirname(dirname(dirname(dirname(__FILE__)))) . '/_include/contact';
$dir_app  = dirname(__FILE__);

/* URL関連
 -------------------------------------------------- */
define('APP_DIR_NAME', 'hkg/contact');
define('APP_URL_ENTRY_INPUT', SECURE_HOME . APP_DIR_NAME . '/');
define('APP_URL_ENTRY_COMFIRM', SECURE_HOME . APP_DIR_NAME . '/confirm.php');
define('APP_URL_ENTRY_COMPLETE', STATIC_HOME . APP_DIR_NAME . '/thanks.php');
define('APP_URL_ENTRY_EXCEPTION', SECURE_HOME . APP_DIR_NAME . '/');

/* テンプレート関連
 -------------------------------------------------- */
// テンプレートファイルパス
define('APP_TMPL_PATH', $dir_app . '/templates');


/* メール設定
 *
 * ■ SMTPサーバを使用する場合
 * 'xxxxx' => array(
 *		'server' => array(
 *			'is_smtp' => true,
 *			'smtp' => array(
 *				'host'     => 'SMTPサーバ名 or IPアドレス',
 *				'port'     => 'ポート番号（587など）',
 *				'secure'   => '通信方法（tls or sslなど）',
 *				'auth'     => true or false,
 *				'username' => 'ユーザーアカウント',
 *				'password' => 'パスワード',
 *			),
 *		),
 * ),
 -------------------------------------------------- */
$_MAIL_CONFIG = array(
	// 本番環境用設定
	'publish' => array(
		'server' => array(
			'is_smtp' => false,
		),
	),
	// dev環境用設定
	'develop' => array(
		'server' => array(
			'is_smtp' => false,
		),
	),
	// 社内環境用設定
	'develop-inhouse' => array(
		'server' => array(
			'is_smtp' => false,
		),
	),
	// ローカル環境
	'local' => array(
		'server' => array(
			// 'is_smtp' => false,
			// 'is_debug' => true,
			'is_smtp' => true,
      'smtp' => array(
        'host'     => 'smtp.gmail.com',
        'port'     => '587',
        'secure'   => 'tls',
        'auth'     => true,
        'username' => '<EMAIL>',
        'password' => 'Nf3JDY2w',
      ),
		),
	),
);


/* お問い合わせ区分によるメール設定
 *
 * ■ 管理者宛のtoアドレスの表示について
 * メールの宛先に、名前 <<EMAIL>> を指定する場合の記述
 * 'admin' => array(
 *  ....
 *     'to' => array(
 *        array('<EMAIL>', '名前')
 *     )
 * )
 *
 -------------------------------------------------- */
$_MAIL_CONFIG_EXTEND = false;
$_MAIL_CONFIG_EXTEND_FIELD = '';


// 管理者宛メール設定
// - メール件名
$_MAIL_SUBJECT_ADMIN = 'We have received your inquiry form.';
// - Fromアドレス
$_MAIL_FROM_ADMIN = '<EMAIL>';
// - From名
$_MAIL_FROM_ADMIN_NAME = 'Kowa Company, Ltd.'; // 不要な場合はnullを指定
// - Toアドレス
$_MAIL_TO_ADMIN = array('<EMAIL>');


// ユーザ宛メール設定
// - メール件名
$_MAIL_SUBJECT_USER = 'Thank you for your inqury.';
// - Fromアドレス
$_MAIL_FROM_USER = '<EMAIL>';


// 環境による設定値が必要な場合の記述
if (APP_ENV !== 'publish') {
	$_MAIL_SUBJECT_ADMIN = '(TEST)' . $_MAIL_SUBJECT_ADMIN;

	$_MAIL_SUBJECT_USER = '(TEST)' . $_MAIL_SUBJECT_USER;

	$_MAIL_FROM_ADMIN = '<EMAIL>';
	$_MAIL_FROM_USER  = '<EMAIL>';

	$_MAIL_TO_ADMIN = array('<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>');
}


$_MAIL_CONTENT = array(
	'admin' => array(
		'subject'   => $_MAIL_SUBJECT_ADMIN,
		'from'      => $_MAIL_FROM_ADMIN,
		'from_name' => $_MAIL_FROM_ADMIN_NAME,
		'to'        => $_MAIL_TO_ADMIN,
	),
	'user' => array(
		'subject'   => $_MAIL_SUBJECT_USER,
		'from'      => $_MAIL_FROM_USER,
		'from_name' => $_MAIL_FROM_ADMIN_NAME,
	),
);


/* 定数関連
 -------------------------------------------------- */
$_MAIL_VARS = array();



/* Session関連
 -------------------------------------------------- */
// Session名
define('APP_SESSION_NAME', 'general-form-data');
// Sessionパス
define('APP_SESSION_PATH', '/contact/');
// Sessionパス
define('APP_SESSION_KEY', 'app_data');

// Session定義の変更
ini_set('session.cookie_httponly', '1');


/* ログ関連
 -------------------------------------------------- */
// ログファイルパス
define('LOG_PATH', $dir_app . '/logs');
// ログレベル出力するログレベル
// 設定したレベル以下のログが表示されるように設定できます。
// 1:FATAL, 2:ERROR, 3:WARNING, 4:NOTICE, 5:INFO, 6:TRACE, 7:DEBUG
define('LOG_LEVEL', 7);
// ログファイルをレベルごとに分けるかの設置（0:日付での分割のみ、1:日付+レベルでの分割）
define('LOG_FILE_SEPARATE', 0);


/* 各ファイルの読み込み
 -------------------------------------------------- */
require_once($dir_libs . '/class/core/Common.php');
require_once($dir_libs . '/class/core/Log.php');
require_once($dir_libs . '/class/core/Session.php');
require_once($dir_libs . '/class/core/Token.php');
require_once($dir_libs . '/class/core/HtmlHelper.php');
require_once($dir_libs . '/class/core/MailSender.php');
require_once($dir_libs . '/class/core/Validate.php');
require_once($dir_app . '/class/Entry.php');
