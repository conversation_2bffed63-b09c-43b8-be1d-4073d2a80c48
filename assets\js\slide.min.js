
$(window).on('load resize orientationchange', function() {
	slideSet();
	singleSlideSet();
});

// top slide
function slideSet() {
	$('.js-topSlide').not('.slick-initialized').slick({
		arrows: false,
		autoplay: true,
		infinite: true,
		touchThreshold: 15,
		slidesToShow: 1,
		centerMode: true,
		autoplaySpeed: 6000,
		speed: 800,
		dots: true,
		pauseOnFocus: false, //スライダーを止めない
		pauseOnHover: false, //スライダーを止めない
		pauseOnDotsHover: false,
		centerPadding: '0',
		fade: true,
		customPaging: function(slick, index){
			var num = slick.$slides.eq(index).html();
			return '<div class="slick-dots__dot">' + '</div>';
		},
		
	})
	.on({
		
		beforeChange: function(event, slick, currentSlide, nextSlide) {
			$(".slick-slide", this).eq(currentSlide).addClass("preve-slide");
			$(".slick-slide", this).eq(nextSlide).addClass("slide-animation");
		},
		afterChange: function() {
			$(".preve-slide", this).removeClass("preve-slide slide-animation");
		},
		touchmove: function(event, slick, currentSlide, nextSlide){
			$('.js-topSlide').slick('slickPlay');
		}
	});
	$('.js-topSlide').find(".slick-current").eq(0).addClass("slide-animation");
	
}

// single slide
function singleSlideSet() {
	$('.js-slide').not('.slick-initialized').slick({
		arrows: true,
		autoplay: true,
		infinite: true,
		touchThreshold: 15,
		slidesToShow: 1,
		centerMode: true,
		autoplaySpeed: 2500,
		pauseOnFocus: false,
		pauseOnHover: false,
		centerPadding: '0',
		speed: 800,
		dots: true,
		prevArrow: '<button class="m_slick__arrow m_slick__arrow_prev"></button>',
    nextArrow: '<button class="m_slick__arrow m_slick__arrow_next"></button>',
		responsive: [
			{
				breakpoint: 750,
				settings: {
					slidesToShow: 1,
					centerPadding: '0'
				}
			}
		],
		customPaging: function(slick, index){
			var num = slick.$slides.eq(index).html();
			return '<div class="slick-dots__dot m_slick__dot">' + '</div>';
		}
	});
};
//# sourceMappingURL=slide.min.js.map
