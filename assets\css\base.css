html,
body,
div,
span,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
abbr,
address,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
samp,
small,
strong,
sub,
sup,
var,
b,
i,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  font-size: 100%;
  vertical-align: baseline;
  background: transparent;
}

body {
  line-height: 1;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section,
main {
  display: block;
}

ul,
ol {
  list-style: none;
}

blockquote,
q {
  quotes: none;
}

blockquote:before,
blockquote:after,
q:before,
q:after {
  content: "";
  content: none;
}

a {
  margin: 0;
  padding: 0;
  font-size: 100%;
  vertical-align: baseline;
  background: transparent;
}

ins {
  color: #000;
  text-decoration: none;
}

mark {
  background-color: #ff9;
  color: #000;
  font-style: italic;
  font-weight: bold;
}

del {
  text-decoration: line-through;
}

abbr[title],
dfn[title] {
  border-bottom: 1px dotted;
  cursor: help;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

hr {
  display: block;
  height: 0;
  border: 0;
  border-top: 1px solid #cccccc;
  margin: 0;
  padding: 0;
}

input,
select {
  vertical-align: middle;
}
.u-text-left{
  text-align: left !important;
}
@media (min-width:751px){
  .u-text-left-pc{
    text-align: left !important;
  }
}
@media (max-width:750px){
  .u-text-left-sp{
    text-align: left !important;
  }
}
@font-face {
  font-family: "GothamBold";
  src: url("../fonts/GOTHAM-BOLD.TTF") format("truetype");
}

@font-face {
  font-family: "NotoSansCjkJPDemiLight";
  src: url("../fonts/NotoSansCJKjp-DemiLight.otf") format("opentype"), url("../fonts/NotoSansCJKjp-DemiLight.woff") format("woff");
}

@font-face {
  font-family: "NotoSansCjkJPLight";
  src: url("../fonts/NotoSansCJKjp-Light.otf") format("opentype");
}

html {
  font-family: "noto-sans-cjk-jp", "Noto Sans JP", "游ゴシック体", "Yu Gothic", YuGothic, sans-serif;
  color: #333;
  font-weight: 400;
  word-wrap: break-word;
  font-size: 16px;
}

@media print,
screen and (max-width: 750px) {
  html {
    font-size: 14px;
    font-size: 3.73333vw;
  }
}

body {
  width: 100%;
  height: 100%;
  background-color: #fff9ed;
  -webkit-text-size-adjust: 100%;
}

html.is-fixed,
body.is-fixed {
  overflow: hidden;
  height: 100%;
}

html.js-fixed,
body.js-fixed {
  position: fixed;
  width: 100%;
  height: 100%;
}

a {
  text-decoration: none;
  color: #333;
}

button {
  border: none;
  cursor: pointer;
  outline: none;
  padding: 0;
  appearance: none;
  font: initial;
}

@media print,
screen and (min-width: 1025px) {
  a:hover {
    text-decoration: none;
  }
}

*:focus {
  outline: none;
}

img {
  vertical-align: bottom;
  max-width: 100%;
  max-height: 100%;
}

@media print,
screen and (max-width: 750px) {
  img {
    width: 100%;
  }
}

strong {
  font-weight: 700;
}

em {
  font-style: italic;
}

picture {
  display: block;
}

input {
  padding: 0;
  border: none;
  background: none;
}

* {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

input[type="text"],
input[type="tel"],
input[type="password"],
input[type="email"],
input[type="search"],
input[type="url"],
input[type="datetime"],
input[type="date"],
input[type="month"],
input[type="week"],
input[type="time"],
input[type="datetime-local"],
input[type="number"],
select,
textarea {
  max-width: 100%;
  padding: 0.5rem 0.75rem;
  font: inherit;
  background-color: #fff;
  border: none;
  vertical-align: bottom;
}

input[type="text"]::-webkit-input-placeholder,
input[type="tel"]::-webkit-input-placeholder,
input[type="password"]::-webkit-input-placeholder,
input[type="email"]::-webkit-input-placeholder,
input[type="search"]::-webkit-input-placeholder,
input[type="url"]::-webkit-input-placeholder,
input[type="datetime"]::-webkit-input-placeholder,
input[type="date"]::-webkit-input-placeholder,
input[type="month"]::-webkit-input-placeholder,
input[type="week"]::-webkit-input-placeholder,
input[type="time"]::-webkit-input-placeholder,
input[type="datetime-local"]::-webkit-input-placeholder,
input[type="number"]::-webkit-input-placeholder,
select::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #bebebe;
}

input[type="text"]:-ms-input-placeholder,
input[type="tel"]:-ms-input-placeholder,
input[type="password"]:-ms-input-placeholder,
input[type="email"]:-ms-input-placeholder,
input[type="search"]:-ms-input-placeholder,
input[type="url"]:-ms-input-placeholder,
input[type="datetime"]:-ms-input-placeholder,
input[type="date"]:-ms-input-placeholder,
input[type="month"]:-ms-input-placeholder,
input[type="week"]:-ms-input-placeholder,
input[type="time"]:-ms-input-placeholder,
input[type="datetime-local"]:-ms-input-placeholder,
input[type="number"]:-ms-input-placeholder,
select:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  color: #bebebe;
}

input[type="text"]::-moz-placeholder,
input[type="tel"]::-moz-placeholder,
input[type="password"]::-moz-placeholder,
input[type="email"]::-moz-placeholder,
input[type="search"]::-moz-placeholder,
input[type="url"]::-moz-placeholder,
input[type="datetime"]::-moz-placeholder,
input[type="date"]::-moz-placeholder,
input[type="month"]::-moz-placeholder,
input[type="week"]::-moz-placeholder,
input[type="time"]::-moz-placeholder,
input[type="datetime-local"]::-moz-placeholder,
input[type="number"]::-moz-placeholder,
select::-moz-placeholder,
textarea::-moz-placeholder {
  color: #bebebe;
  opacity: 1;
}

input[type="text"]:focus,
input[type="tel"]:focus,
input[type="password"]:focus,
input[type="email"]:focus,
input[type="search"]:focus,
input[type="url"]:focus,
input[type="datetime"]:focus,
input[type="date"]:focus,
input[type="month"]:focus,
input[type="week"]:focus,
input[type="time"]:focus,
input[type="datetime-local"]:focus,
input[type="number"]:focus,
select:focus,
textarea:focus {
  -webkit-box-shadow: 0 0 4px #cccdce;
  box-shadow: 0 0 4px #cccdce;
}

input[type="radio"],
input[type="checkbox"] {
  margin: 0;
  vertical-align: -1px;
}

input[type="button"],
input[type="submit"] {
  -webkit-appearance: none;
  cursor: pointer;
  font: inherit;
  vertical-align: bottom;
}

textarea {
  resize: vertical;
  vertical-align: bottom;
}

sup {
  font-size: 85%;
  line-height: 1;
  vertical-align: super;
}

sub {
  vertical-align: sub;
  line-height: 1;
}

blockquote {
  background-color: #f5f5f5;
  padding: 1em 1em 1em 3em;
  position: relative;
  border-left: 3px solid #666;
}

blockquote:before {
  content: "“";
  font-size: 4em;
  line-height: 1;
  color: #999;
  position: absolute;
  left: 0.15em;
  top: 0.15em;
}

::-moz-selection {
  color: #fff;
  background-color: #e50113;
}

::selection {
  color: #fff;
  background-color: #e50113;
}

::-moz-selection {
  color: #fff;
  background-color: #e50113;
}

.clr-bdr::after {
  border-bottom: 5px solid #e50113 !important;
}

.clr-bdr01 {
  color: #e50113 !important;
  border: 1px solid #e50113 !important;
}

.clr-bdr01:hover {
  color: #fff !important;
}

.clr-bdr01:hover::after,
.clr-bdr01:hover::before {
  background-color: #fff !important;
}

.btn-hover:hover p>a,
.clr-bdr01:hover,
.clr-bdr01::before,
.clr-bdr01::after {
  background-color: #e50113 !important;
}

.clr-hover:hover,
.clr01 {
  color: #e50113 !important;
}

.clr02 {
  color: #333 !important;
}

.clr03 {
  color: #fbff0a !important;
}

.mr-0 {
  margin-right: 0 !important;
}

.bg-clr,
.lineClr::after {
  background-color: #e50113 !important;
}

.lineClr01::after {
  background-color: #333 !important;
}

.bg-img {
  background-image: url(../images/common/icon_bank_red.svg) !important;
}

.line-height {
  line-height: 1.5 !important;
}

.l_wrap .is-pc {
  display: block;
}

@media print,
screen and (max-width: 1024px) {
  .l_wrap .is-pc {
    display: none;
  }
}

.l_wrap .is-pctbl {
  display: block;
}

@media print,
screen and (max-width: 750px) {
  .l_wrap .is-pctbl {
    display: none;
  }
}

.l_wrap .is-pcsp {
  display: block;
}

@media print,
screen and (min-width: 751px) and (max-width: 1024px) {
  .l_wrap .is-pcsp {
    display: none;
  }
}

.l_wrap .is-tbl {
  display: none;
}

@media print,
screen and (min-width: 751px) and (max-width: 1024px) {
  .l_wrap .is-tbl {
    display: block;
  }
}

.l_wrap .is-tblsp {
  display: none;
}

@media print,
screen and (max-width: 1024px) {
  .l_wrap .is-tblsp {
    display: block;
  }
}

.l_wrap .is-sp {
  display: none;
}

@media print,
screen and (max-width: 750px) {
  .l_wrap .is-sp {
    display: block;
  }
}

.l_wrap .is-sp_space {
  margin-left: 1rem;
}

@media print,
screen and (max-width: 750px) {
  .l_wrap .is-sp_space {
    display: block;
    margin-left: 0;
  }
}

.l_wrap .is-pcI {
  display: inline;
}

@media print,
screen and (max-width: 1024px) {
  .l_wrap .is-pcI {
    display: none;
  }
}

.l_wrap .is-pctblI {
  display: inline;
}

@media print,
screen and (max-width: 750px) {
  .l_wrap .is-pctblI {
    display: none;
  }
}

.l_wrap .is-tblI {
  display: none;
}

@media print,
screen and (min-width: 751px) and (max-width: 1024px) {
  .l_wrap .is-tblI {
    display: inline;
  }
}

.l_wrap .is-tblspI {
  display: inline;
}

@media print,
screen and (min-width: 1025px) {
  .l_wrap .is-tblspI {
    display: none;
  }
}

.l_wrap .is-spI {
  display: none;
}

@media print,
screen and (max-width: 750px) {
  .l_wrap .is-spI {
    display: inline;
  }
}

.l_wrap .is-custamYblI {
  display: inline;
}

@media (max-width: 768px) {
  .l_wrap .is-custamYblI {
    display: none;
  }
}

.is-mt0 {
  margin-top: 0 !important;
}

.is-mb0 {
  margin-bottom: 0 !important;
}

.is-mtXXL {
  margin-top: 7em !important;
}

@media print,
screen and (max-width: 750px) {
  .is-mtXXL {
    margin-top: 4em !important;
  }
}

.is-mtXL {
  margin-top: 5.5em !important;
}

@media print,
screen and (max-width: 750px) {
  .is-mtXL {
    margin-top: 3em !important;
  }
}

.is-mtL {
  margin-top: 4em !important;
}

@media print,
screen and (max-width: 750px) {
  .is-mtL {
    margin-top: 2em !important;
  }
}

.is-mtM {
  margin-top: 2.5em !important;
}

@media print,
screen and (max-width: 750px) {
  .is-mtM {
    margin-top: 1.75em !important;
  }
}

.is-mtS {
  margin-top: 1.25em !important;
}

.is-mtXS {
  margin-top: 0.75em !important;
}

.is-taL {
  text-align: left !important;
}

.is-taC {
  text-align: center !important;
}

.is-taR {
  text-align: right !important;
}

.is-fwB {
  font-weight: bold !important;
}

.is-tdUL {
  text-decoration: underline !important;
}

.is-colorEm {
  color: red !important;
}

.is-colorG {
  color: #95989a !important;
}

.is-link a {
  font-weight: bold;
}

@media print,
screen and (min-width: 1025px) {
  .is-link a {
    -webkit-transition: 0.3s;
    transition: 0.3s;
  }

  .is-link a:hover {
    color: #e50113;
  }
}

.is-link a[target*="_blank"]::after {
  content: "";
  display: inline-block;
  width: 0.95em;
  height: 0.8em;
  margin-left: 0.5em;
  background: url(../images/icon/blankBk.svg) no-repeat center;
  background-size: contain;
}

.l_wrap .is-fsXXL {
  font-size: 24px;
  font-size: 1.5rem;
}

@media print,
screen and (max-width: 750px) {
  .l_wrap .is-fsXXL {
    font-size: 18px;
    font-size: 4.8vw;
  }
}

.l_wrap .is-fsXL {
  font-size: 20px;
  font-size: 1.25rem;
}

@media print,
screen and (max-width: 750px) {
  .l_wrap .is-fsXL {
    font-size: 16px;
    font-size: 4.26667vw;
  }
}

.l_wrap .is-fsL {
  font-size: 18px;
  font-size: 1.125rem;
}

@media print,
screen and (max-width: 750px) {
  .l_wrap .is-fsL {
    font-size: 15px;
    font-size: 4vw;
  }
}

.l_wrap .is-fsM {
  font-size: 16px;
  font-size: 1rem;
}

@media print,
screen and (max-width: 750px) {
  .l_wrap .is-fsM {
    font-size: 14px;
    font-size: 3.73333vw;
  }
}

.l_wrap .is-fsS {
  font-size: 14px;
  font-size: 0.875rem;
}

@media print,
screen and (max-width: 750px) {
  .l_wrap .is-fsS {
    font-size: 12px;
    font-size: 3.2vw;
  }
}

.l_wrap .is-fsXS {
  font-size: 12px;
  font-size: 0.75rem;
}

@media print,
screen and (max-width: 750px) {
  .l_wrap .is-fsXS {
    font-size: 11px;
    font-size: 2.93333vw;
  }
}

.slick-slider {
  position: relative;
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -ms-touch-action: pan-y;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
}

.slick-list {
  position: relative;
  overflow: hidden;
  display: block;
  margin: 0;
  padding: 0;
}

.is-top .slick-list {
  pointer-events: none;
}

.slick-list:focus {
  outline: none;
}

.slick-list.dragging {
  cursor: pointer;
  cursor: hand;
}

.slick-slider .slick-track,
.slick-slider .slick-list {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

.slick-track {
  position: relative;
  left: 0;
  top: 0;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

.slick-track:before,
.slick-track:after {
  content: "";
  display: table;
}

.slick-track:after {
  clear: both;
}

.slick-loading .slick-track {
  visibility: hidden;
}

.slick-slide {
  float: left;
  height: 100%;
  min-height: 1px;
  display: none;
}

[dir="rtl"] .slick-slide {
  float: right;
}

.slick-slide img {
  display: block;
}

.slick-slide.slick-loading img {
  display: none;
}

.slick-slide.dragging img {
  pointer-events: none;
}

.slick-initialized .slick-slide {
  display: block;
}

.slick-loading .slick-slide {
  visibility: hidden;
}

.slick-vertical .slick-slide {
  display: block;
  height: auto;
  border: 1px solid transparent;
}

.slick-arrow.slick-hidden {
  display: none;
}

.l_header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: transparent;
  z-index: 11;
  -webkit-transition: background-color 0.35s;
  transition: background-color 0.35s;
  background-color: #fff;
}

.l_header.is-none {
  display: none;
}

@media print,
screen and (min-width: 1025px) {
  body.is-home .l_header {
    opacity: 0;
    -webkit-animation: topHeader 1s 2s forwards;
    animation: topHeader 1s 2s forwards;
  }
}

@media print,
screen and (min-width: 751px) and (max-width: 1024px) {
  body.is-home .l_header {
    opacity: 0;
    -webkit-animation: topHeader 1s 2s forwards;
    animation: topHeader 1s 2s forwards;
  }
}

@media print,
screen and (max-width: 750px) {
  body.is-home .l_header {
    opacity: 1;
    z-index: 10;
    background-color: #fff;
  }
}

body.is-home .l_header.is-showAll {
  opacity: 1;
  z-index: 15;
  -webkit-transition: 0.35s;
  transition: 0.35s;
}

.l_header.is-bgColor {
  background-color: rgba(255, 255, 255, 0.95);
  -webkit-box-shadow: 0px 3px 6px 0px rgba(51, 51, 51, 0.2);
  box-shadow: 0px 3px 6px 0px rgba(51, 51, 51, 0.2);
}

@media print,
screen and (min-width: 1025px) {
  .is-navOpen .l_header {
    background-color: transparent;
  }
}

.is-navOpen .l_header.is-bgColor {
  -webkit-box-shadow: none;
  box-shadow: none;
}

.l_header .l_inner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  width: 100%;
  max-width: none;
  padding-top: 0.85rem;
  padding-bottom: 0.85rem;
  padding-left: 33px;
  padding-right: 33px;
}

@media print,
screen and (min-width: 751px) {
  .l_header .l_inner {
    height: 80px;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_header .l_inner {
    padding: 0.7rem 0.7rem;
    padding-right: 0 !important;
  }
}

@-webkit-keyframes topHeader {
  0% {
    opacity: 0;
    z-index: -10;
  }

  100% {
    pointer-events: auto;
    opacity: 1;
    z-index: 10;
  }
}

@keyframes topHeader {
  0% {
    opacity: 0;
    z-index: -10;
  }

  100% {
    pointer-events: auto;
    opacity: 1;
    z-index: 10;
  }
}

.l_header-logo {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.l_header-logo__kowa {
  max-width: 93.81px;
}

@media print,
screen and (max-width: 750px) {
  .l_header-logo__kowa {
    width: 4.5rem;
  }
}

.l_header-logo__kowa--copy {
  font-size: 18px;
  font-size: 1.125rem;
}

@media print,
screen and (max-width: 750px) {
  .l_header-logo__kowa--copy {
    font-size: 14px;
    font-size: 3.73333vw;
  }

  .is-vnm .l_header-logo__kowa--copy {
    font-size: 10px;
    font-size: 3.66667vw;
  }

  .is-mys .l_header-logo__kowa--copy {
    font-size: 13px;
    font-size: 3.46667vw;
  }
}

.l_header-logo span,
.l_header-logo h1 {
  font-weight: 500;
  margin-left: 22px;
}

@media print,
screen and (max-width: 750px) {
  .l_header-logo {
    padding-right: 10px;
  }

  .l_header-logo span,
  .l_header-logo h1 {
    margin-left: 1rem;
  }

  .l_header-logo h1 {
    flex: 1 0 auto;
  }
}

.is-bgColor .l_header-logo svg path {
  fill: #bd0f72;
}

@media print,
screen and (min-width: 1025px) {
  .is-navOpen .l_header-logo svg path {
    fill: #bd0f72;
  }
}

@media print,
screen and (min-width: 751px) {
  .is-headerUp {
    -webkit-transform: translateY(-83px);
    transform: translateY(-83px);
  }
}

@media print,
screen and (max-width: 750px) {
  .is-headerUp {
    -webkit-transform: translateY(-3.75rem);
    transform: translateY(-3.75rem);
  }
}

.l_header-main {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  z-index: 15;
}

.l_header-main-nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
  width: 100%;
}

@media (max-width: 1024px) {
  .l_header-main-nav>ul li .is-global {
    display: none;
  }
}

.l_header-main-nav>ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.l_header-main-nav>ul li .is-global {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-transition: 0.35s;
  transition: 0.35s;
  padding: 1.65rem 0;
  padding-right: 46px;
  background-image: url(../images/common/icon_arrow_down.svg);
  background-size: auto 7px;
  background-repeat: no-repeat;
  background-position: 85% center;
}

.is-tha .l_header-main-nav>ul li .is-global {
  background-position: 90% center;
}

@media print,
screen and (min-width: 1025px) {
  .l_header-main-nav>ul li .is-global {
    position: relative;
  }

  .l_header-main-nav>ul li .is-global:hover {
    background-image: url(../images/common/icon_arrow_down_red.svg);
    color: #e50113;
    cursor: pointer;
  }

  .l_header-main-nav>ul li .is-global:hover .icon-global {
    background: url(../images/common/icon_global_red.svg) no-repeat center left;
  }
}

@media print,
screen and (max-width: 750px) {

  .l_header-main-nav>ul li:nth-child(2),
  .l_header-main-nav>ul li .is-global {
    display: none;
  }

  .l_header-main-nav {
    flex: 0;
  }
}

.l_header-main-nav>ul li {
  display: flex;
  align-items: center;
}

.l_header-main-nav>ul li>.text-nation {
  padding: 7px 8px 8px 8px;
  margin-right: 20px;
  font-size: 16px;
  color: #333;
  border: 1px solid #707070;
  display: none;
}

.l_header-main-nav>ul li .is-global .icon-global {
  display: inline-block;
  background: url(../images/common/icon_global.svg) no-repeat center left;
  background-size: 24px;
  -webkit-transition: 0.35s;
  transition: 0.35s;
  width: 24px;
  height: 24px;
  margin-bottom: 2px;
  margin-right: 14px;
  margin-top: 3px;
}

.l_header-nav_global-wrap>.l_header-nav_global-list>.l_header-nav_global-list__item>a:active {
  color: #e50113;
}

.l_header-main-nav>ul li .l_heade-btn {
  width: 100%;
}

@media print,
screen and (min-width: 751px) {
  .l_header-main-nav>ul li .l_heade-btn {
    display: table;
    border-collapse: separate;
    margin: 0 auto;
    position: relative;
  }
}

@media print,
screen and (min-width: 751px) {
  .l_header-main-nav>ul li .l_heade-btn a {
    background: transparent;
    display: table-cell;
    text-align: center;
    vertical-align: middle;
    width: 100%;
    height: 45px;
    font-size: 15px;
    font-size: 0.9375rem;
    letter-spacing: 0.28px;
    padding: 0.25rem 1.82rem 0.25rem 3rem;
    -webkit-transition: 0.35s;
    transition: 0.35s;
    padding-top: 2.031rem;
    padding-bottom: 2.031rem;
  }

  .l_header-main-nav>ul li .l_heade-btn a span {
    display: inline-block;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_header-main-nav>ul li>.text-nation {
    display: block;
    margin-right: 0;
    padding: 5px 6px 6px 6px;
    font-size: 14px;
  }

  .l_header-main-nav>ul li .l_heade-btn a {
    display: block;
    background: url(../images/common/icon_mail_sp.svg) no-repeat center center;
    background-size: cover;
    width: 3.7rem;
    height: 3.43rem;
  }

  .l_header-main-nav>ul li .l_heade-btn a span {
    display: none;
  }

  .l_header-main-nav>ul li .l_heade-btn a::before {
    content: none !important;
  }
}

.l_header-main-nav>ul li .l_heade-btn.is-mail a {
  position: relative;
  -webkit-transition: none;
  transition: none;
  background-image: url(../images/common/icon_mail.svg);
  background-size: auto;
  background-repeat: no-repeat;
  background-position: 13% center;
}

@media print,
screen and (min-width: 1025px) {
  .l_header-main-nav>ul li .l_heade-btn.is-mail a:hover {
    color: #e50113;
    background-image: url(../images/common/icon_mail_red.svg);
  }
}

.l_header-nav-navtrigger {
  z-index: 15;
}

@media print,
screen and (min-width: 751px) {
  .l_header-nav-navtrigger {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding-left: 35px;
    position: relative;
  }

  .l_header-nav-navtrigger::before {
    content: "";
    position: absolute;
    width: 1px;
    height: 43px;
    left: 0;
    top: 5px;
    background-color: #707070;
  }

  .l_header-nav-navtrigger p {
    padding-bottom: 4px;
    white-space: nowrap;
  }
}

@media print,
screen and (min-width: 1025px) {
  .l_header-nav-navtrigger {
    -webkit-transition: 0.35s;
    transition: 0.35s;
    cursor: pointer;
  }

  .l_header-nav-navtrigger:hover {
    color: #e50113;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_header-nav-navtrigger {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-flow: column;
    flex-flow: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    font-size: 12px;
    font-size: 3.2vw;
  }

  .l_header-nav-navtrigger p {
    margin-bottom: 0.1rem;
  }
}

.l_header-nav-navtrigger_btn {
  width: 55px;
  height: 55px;
  margin-left: 9px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  z-index: 15;
}

@media print,
screen and (min-width: 1025px) {
  .l_header-nav-navtrigger_btn {
    cursor: pointer;
  }

  .l_header-nav-navtrigger_btn:hover span {
    background: #e50113 !important;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_header-nav-navtrigger_btn {
    width: 4.342rem;
    height: 1rem;
    margin-left: 0;
  }
}

.l_header-nav-navtrigger_btn div {
  width: 44px;
  height: 9px;
  position: relative;
}

@media print,
screen and (max-width: 750px) {
  .l_header-nav-navtrigger_btn div {
    width: 1.429rem;
    height: 0.5rem;
  }
}

.l_header-nav-navtrigger_btn div span {
  width: 100%;
  height: 1px;
  display: block;
  background: #e50113;
  position: absolute;
  left: 0px;
}

.l_header-nav-navtrigger_btn div span:nth-child(1) {
  top: 0px;
}

.is-navOpen .l_header-nav-navtrigger_btn div span:nth-child(1) {
  -webkit-transform: translate3d(0px, 4px, 0px) rotate(45deg);
  transform: translate3d(0px, 4px, 0px) rotate(45deg);
}

@media print,
screen and (max-width: 750px) {
  .is-navOpen .l_header-nav-navtrigger_btn div span:nth-child(1) {
    -webkit-transform: translate3d(0px, 0.3rem, 0px) rotate(25deg);
    transform: translate3d(0px, 0.3rem, 0px) rotate(25deg);
  }
}

.l_header-nav-navtrigger_btn div span:nth-child(2) {
  bottom: 0px;
}

.is-navOpen .l_header-nav-navtrigger_btn div span:nth-child(2) {
  -webkit-transform: translate3d(0px, -5px, 0px) rotate(-45deg);
  transform: translate3d(0px, -5px, 0px) rotate(-45deg);
}

@media print,
screen and (max-width: 750px) {
  .is-navOpen .l_header-nav-navtrigger_btn div span:nth-child(2) {
    -webkit-transform: translate3d(0px, -0.15rem, 0px) rotate(-25deg);
    transform: translate3d(0px, -0.15rem, 0px) rotate(-25deg);
  }
}

.l_header-nav-navtrigger_btn span {
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
}

.l_header-nav-wrap {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  height: auto;
  min-height: 30vh;
  max-height: 100vh;
  background-color: #fff;
  overflow: auto;
  z-index: 11;
  padding-top: 82px;
  padding-bottom: 40px;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media print,
screen and (max-width: 750px) {
  .l_header-nav-wrap {
    padding: 0;
    padding-top: 3.71rem;
  }
}

.l_header-nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
}

@media print,
screen and (max-width: 1024px) {
  .l_header-nav {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-flow: column;
    flex-flow: column;
  }
}

@media print,
screen and (min-width: 1025px) {
  .l_header-nav {
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }

  .is-hkg .l_header-nav {
    padding: 0;
    max-width: 90%;
    width: 1350px;
  }

  .is-hkg .l_header-nav__item {
    margin: 0 !important;
    width: 25%;
  }
}

@media print,
screen and (min-width: 751px) and (max-width: 1024px) {
  .l_header-nav {
    padding-top: 5vw;
    padding-left: 35px;
    padding-right: 35px;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_header-nav {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-flow: column;
    flex-flow: column;
    padding: 5vw;
    padding-bottom: 15vw;
  }
}

.l_header-nav__item {
  -webkit-transition: color 0.35s;
  transition: color 0.35s;
  white-space: nowrap;
  cursor: pointer;
}

@media print,
screen and (min-width: 1025px) {
  .l_header-nav__item:first-child {
    margin-right: 35%;
  }

  .l_header-nav__item:nth-child(3) {
    margin-left: 35%;
  }
}

@media print,
screen and (max-width: 1024px) {
  .l_header-nav__item {
    position: relative;
  }

  .l_header-nav__item a {
    display: block;
  }
}

.l_header-nav__itemParent {
  font-family: "Noto Sans CJK JP", "Noto Sans JP", "游明朝体", "Yu Mincho", YuMincho, serif;
}

@media print,
screen and (min-width: 1025px) {
  .l_header-nav__itemParent {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-flow: column;
    flex-flow: column;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    height: 100%;
    padding: 0 1rem;
    position: relative;
    -webkit-transition: 0.35s;
    transition: 0.35s;
  }

  .l_header-nav__itemParent:hover {
    color: #e50113;
  }

  .l_header-nav__itemParent:hover .__link::after {
    right: -1.8rem;
    background: url(../images/common/icon_arrow_red.svg) no-repeat center right;
  }

  .l_header-nav__itemParent .__link {
    font-size: 20px;
    font-size: 1.25rem;
    position: relative;
  }

  .l_header-nav__itemParent .__link::after {
    content: "";
    position: absolute;
    bottom: 0;
    right: -1.5rem;
    display: inline-block;
    width: 1rem;
    height: 1rem;
    background: url(../images/common/icon_arrow_right.svg) no-repeat center right;
    background-size: 1rem;
    -webkit-transition: 0.35s;
    transition: 0.35s;
  }
}

@media (max-width: 1200px) {
  .l_header-nav__itemParent {
    width: 100%;
    padding: 1.66vw 0;
    padding: 2.5vw 0;
  }

  .l_header-nav__itemParent .__link {
    font-size: 1.75vw;
  }
}

@media print,
screen and (max-width: 1024px) {
  .l_header-nav__itemParent {
    font-weight: 300;
    position: relative;
  }

  .l_header-nav__itemParent::after {
    content: "";
    position: absolute;
    top: 35%;
    right: 6vw;
    display: inline-block;
    width: 1rem;
    height: 1rem;
    background: url(../images/common/icon_arrow_right.svg) no-repeat center right;
    background-size: 1rem;
  }

  .l_header-nav__itemParent .__link {
    font-weight: bold;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_header-nav__itemParent {
    width: 100%;
    font-size: 11px;
    font-size: 2.93333vw;
    padding: 2.5vw 0;
  }

  .l_header-nav__itemParent .__link {
    font-size: 16px;
    font-size: 4.26667vw;
    font-weight: bold;
  }
}

@media print,
screen and (min-width: 1025px) {
  .l_header-nav__itemParent>p {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: end;
    -ms-flex-align: end;
    align-items: flex-end;
  }

  .l_header-nav__itemParent>p.is-vantelin {
    height: 181px;
    width: 268.44px;
  }

  .l_header-nav__itemParent>p.is-mask {
    height: 181.25px;
    width: 206.2px;
  }

  .l_header-nav__itemParent>p>img {
    -o-object-fit: contain;
    object-fit: contain;
    font-family: "object-fit: cover;";
    width: 100%;
  }
}

@media print,
screen and (max-width: 1024px) {
  .l_header-nav__itemParent>p {
    display: none;
  }
}

.l_header-nav__item--bottom {
  margin-top: 1rem;
}

.l_header-nav__item--bottom>ul>li {
  border-bottom: 1px solid #707070;
}

.l_header-nav__item--bottom>ul>li:first-child {
  border-top: 1px solid #707070;
}

.l_header-nav__item--bottom>ul>li .icon-global {
  display: inline-block;
  background: url(../images/common/icon_global.svg) no-repeat center left;
  background-size: 1.5rem;
  width: 1.5rem;
  height: 1.5rem;
  margin-right: 0.7rem;
}

.l_header-nav__item--bottom>ul>li .is-mail {
  display: inline-block;
  background: url(../images/common/icon_mail.svg) no-repeat center left;
  background-size: 1.5rem;
  width: 1.5rem;
  height: 1.5rem;
  margin-right: 0.7rem;
}

.l_header-nav__item--bottom>ul>li>a {
  position: relative;
}

.l_header-nav__item--bottom>ul>li>a::after {
  content: "";
  position: absolute;
  top: 35%;
  right: 6vw;
  display: inline-block;
  width: 1rem;
  height: 1rem;
  background: url(../images/common/icon_arrow_right.svg) no-repeat center right;
  background-size: 1rem;
}

.l_header-nav__item--bottom--child {
  position: relative;
}

.l_header-nav__item--bottom--ttl {
  padding: 2.5vw 5vw;
  display: -webkit-box !important;
  display: -ms-flexbox !important;
  display: flex !important;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.l_header-nav__item--bottom--ttl p {
  font-weight: 400;
}

@media (max-width: 1200px) {
  .l_header-nav__item--bottom--ttl p {
    font-size: 1.5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_header-nav__item--bottom--ttl p {
    font-size: 16px;
    font-size: 4.26667vw;
  }
}

.l_header-nav__item--bottom--spAccoBtn {
  position: absolute;
  display: block;
}

@media (max-width: 1200px) {
  .l_header-nav__item--bottom--spAccoBtn {
    top: 0;
    right: 0;
    width: 3rem;
    height: 3.8rem;
    padding: 0.3rem;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_header-nav__item--bottom--spAccoBtn {
    top: 0;
    right: 0.35rem;
    width: 3rem;
    height: 2.8rem;
    padding: 0.3rem;
  }
}

.l_header-nav__item--bottom--spAccoBtn::before,
.l_header-nav__item--bottom--spAccoBtn::after {
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  content: "";
  display: inline-block;
  vertical-align: middle;
  background-color: #333;
  bottom: 0;
  height: 1px;
  margin: auto;
  position: absolute;
  width: 1rem;
  -webkit-transition: -webkit-transform 200ms;
  transition: -webkit-transform 200ms;
  transition: transform 200ms;
  transition: transform 200ms, -webkit-transform 200ms;
  top: 5%;
}

@media (max-width: 1200px) {

  .l_header-nav__item--bottom--spAccoBtn::before,
  .l_header-nav__item--bottom--spAccoBtn::after {
    right: 6vw;
  }
}

@media print,
screen and (max-width: 750px) {

  .l_header-nav__item--bottom--spAccoBtn::before,
  .l_header-nav__item--bottom--spAccoBtn::after {
    right: 5vw;
  }
}

.l_header-nav__item--bottom--spAccoBtn::after {
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
}

.l_header-nav__item--bottom--spAccoBtn.is-accoOpen::before,
.l_header-nav__item--bottom--spAccoBtn.is-accoOpen::after {
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}

.l_header-nav__item--bottom--spAccoBtn.is-accoOpen::after {
  -webkit-transform: rotate(-180deg);
  transform: rotate(-180deg);
}

.l_header-nav__item--bottom_global-wrap {
  display: none;
  padding-left: 5vw;
}

.l_header-nav__item--bottom_global-list>li {
  border-top: 1px solid #707070;
}

.l_header-nav__item--bottom_global-list>li:first-child {
  border-top: 1px solid #707070;
}

.l_header-nav__item--bottom_global-list>li>a {
  padding: 2.5vw 5vw;
  position: relative;
}

.l_header-nav__item--bottom_global-list>li>a::after {
  content: "";
  position: absolute;
  top: 35%;
  right: 6vw;
  display: inline-block;
  width: 1rem;
  height: 1rem;
  background: url(../images/common/icon_arrow_right.svg) no-repeat center right;
  background-size: 1rem;
}

.l_header-nav_global-wrap {
  position: absolute;
  background: #fff;
  top: 71px;
  right: 15px;
}

.l_header-nav_global-wrap.is-navOpen .l_header-nav_global-list {
  display: block;
  -webkit-animation: fadeInMenu 0.35s 0s ease-in-out forwards;
  animation: fadeInMenu 0.35s 0s ease-in-out forwards;
}

.l_header-nav_global-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-flow: column;
  flex-flow: column;
  height: 100%;
  display: none;
  padding-top: 10px;
}

@media print,
screen and (min-width: 1025px) {
  .l_header-nav_global-list {
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
}

@media print,
screen and (min-width: 751px) and (max-width: 1024px) {
  .l_header-nav_global-list {
    padding-top: 5vw;
    padding-left: 35px;
    padding-right: 35px;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_header-nav_global-list {
    padding-top: 15.6vw;
    padding-left: 4vw;
    padding-right: 4vw;
  }
}

.l_header-nav_global-list__item {
  -webkit-transition: color 0.35s;
  transition: color 0.35s;
  white-space: nowrap;
  width: 100%;
  text-align: center;
  border-bottom: 1px solid #dbdbdb;
}

.l_header-nav_global-list__item:first-child {
  border-top: 1px solid #dbdbdb;
}

.l_header-nav_global-list__item:last-child {
  border-bottom: none;
}

.l_header-nav_global-list__item>a {
  padding: 1rem 3rem;
  padding-left: 1rem;
  display: block;
  -webkit-transition: 0.35s;
  transition: 0.35s;
  width: 100%;
  text-align: left;
}

.l_header-nav_global-list__item>a:hover {
  background: #dbdbdb;
}

.l_header-nav_global-list__item--sub {
  padding: 0.5rem 0 0.5rem;
}

.l_header-nav_global-list__item--sub>p {
  color: #333;
  margin-bottom: 0.5rem;
  padding: 0 1rem 0;
  text-align: left;
}

.l_header-nav_global-list__item--sub__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: space-evenly;
  -ms-flex-pack: space-evenly;
  justify-content: space-evenly;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 0 1rem;
}

.l_header-nav_global-list__item--sub__list>a {
  display: block;
  -webkit-transition: 0.35s;
  transition: 0.35s;
  width: 100%;
  padding: 0.5rem 0 0.5rem;
  font-size: 12px;
  font-size: 0.75rem;
  position: relative;
}

.l_header-nav_global-list__item--sub__list>a::before {
  content: "";
  position: absolute;
  width: 1px;
  height: 20px;
  left: 0;
  top: 5px;
  background-color: #dbdbdb;
}

.l_header-nav_global-list__item--sub__list>a:first-child:before {
  content: none;
}

.l_header-nav_global-list__item--sub__list>a:hover {
  background: #dbdbdb;
}

.l_footer {
  line-height: 1.45;
  background-color: #8b8b8b;
  color: #fff;
}

@media print,
screen and (min-width: 751px) {
  .l_footer-nav-wrap {
    padding: 38px 0;
  }
}

@media (max-width: 1200px) {
  .l_footer-nav-wrap {
    padding: 3.16vw 0;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_footer-nav-wrap {
    padding: 1.5rem 0;
  }
}

@media print,
screen and (min-width: 751px) {
  .l_footer-nav {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    max-width: 555px;
    margin: 0 auto;
  }

  .l_footer-nav.is-custom {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
  }
}

@media (max-width: 1200px) {
  .l_footer-nav {
    max-width: 46.25vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_footer-nav {
    max-width: 100%;
  }
}

@media print,
screen and (min-width: 751px) {
  .l_footer-nav__item {
    width: 37.83%;
  }

  .l_footer-nav__item:nth-child(3) {
    width: 25.4%;
    text-align: right;
  }
}

.l_footer-nav__item--parent a,
.l_footer-nav__item--parent span {
  color: #fff;
  font-weight: bold;
  -webkit-transition: 0.35s;
  transition: 0.35s;
  display: inline-block;
  font-size: 14px;
  font-size: 0.875rem;
}

@media (max-width: 1200px) {

  .l_footer-nav__item--parent a,
  .l_footer-nav__item--parent span {
    font-size: 1.16vw;
  }
}

@media print,
screen and (max-width: 750px) {

  .l_footer-nav__item--parent a,
  .l_footer-nav__item--parent span {
    font-size: 14px;
    font-size: 3.73333vw;
    padding: 0.5rem 0;
  }
}

@media print,
screen and (min-width: 1025px) {
  .l_footer-nav__item--parent a:hover {
    opacity: 0.7;
  }
}

.l_footer-nav__item--parent__pages {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-flow: column;
  flex-flow: column;
}

@media print,
screen and (min-width: 751px) {
  .l_footer-nav__item--parent__pages {
    margin-top: 5px;
  }
}

.l_footer-nav__item--parent__pages a {
  color: #fff;
  font-weight: 400;
  font-size: 14px;
  font-size: 0.875rem;
}

@media print,
screen and (min-width: 1025px) {
  .l_footer-nav__item--parent__pages a:hover {
    opacity: 0.7;
  }
}

@media print,
screen and (min-width: 751px) {
  .l_footer-nav__item--parent__pages a {
    padding: 5px 0 5px 22px;
    position: relative;
    -webkit-transition: 0.35s;
    transition: 0.35s;
  }

  .l_footer-nav__item--parent__pages a::before {
    content: "";
    position: absolute;
    top: 15px;
    left: 0;
    width: 10px;
    height: 1px;
    background-color: #fff;
  }
}

@media (max-width: 1200px) {
  .l_footer-nav__item--parent__pages a {
    font-size: 1.16vw;
    padding: 0.41vw 1.83vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_footer-nav__item--parent__pages a {
    font-size: 14px;
    font-size: 3.73333vw;
    padding: 0.5rem 0;
  }
}

.l_footer_bottom {
  background-color: #fff;
}

.l_footer_bottom-nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

@media print,
screen and (min-width: 751px) {
  .l_footer_bottom-nav {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_footer_bottom-nav {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-flow: column;
    flex-flow: column;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_footer_bottom-nav-wrap {
    padding: 3rem 0 1rem;
  }
}

.l_footer_bottom-nav>li {
  display: inline-block;
  padding: 20px 0;
}

@media (max-width: 1200px) {
  .l_footer_bottom-nav>li {
    padding: 1.66vw 0;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_footer_bottom-nav>li {
    font-size: 14px;
    font-size: 3.73333vw;
    padding: 0.2rem 0;
  }
}

.l_footer_bottom-nav>li>a {
  background-image: url(../images/common/icon_blank_black.svg);
  background-size: auto 11px;
  background-repeat: no-repeat;
  background-position: 94% center;
  display: inline-block;
  padding: 0 30px;
  line-height: 1.7;
  -webkit-transition: 0.35s;
  transition: 0.35s;
  font-size: 14px;
  font-size: 0.875rem;
}

@media print,
screen and (min-width: 1025px) {
  .l_footer_bottom-nav>li>a:hover {
    opacity: 0.7;
  }
}

@media (max-width: 1200px) {
  .l_footer_bottom-nav>li>a {
    font-size: 1.16vw;
    padding: 0 2.5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_footer_bottom-nav>li>a {
    background-position: 100% center;
    font-size: 14px;
    font-size: 3.73333vw;
    padding: 0;
    padding-right: 1.5rem;
  }
}

.l_footer-copy {
  font-size: 11px;
  font-size: 0.6875rem;
  color: #333;
  text-align: center;
  padding-top: 24px;
  padding-bottom: 30px;
  letter-spacing: 0;
}

@media (max-width: 768px) {
  .l_footer-copy {
    letter-spacing: 1px;
    padding-top: 2vw;
    padding-bottom: 2.5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_footer-copy {
    font-size: 10px;
    font-size: 2.66667vw;
    letter-spacing: 0;
    margin-top: 0.2rem;
  }
}

body {
  position: relative;
}

body.is-navOpen::after {
  content: "";
  background-color: #000;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0.6;
  width: 100%;
  z-index: 1;
}

@media print,
screen and (min-width: 1025px) {
  .l_wrap::after {
    content: "";
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 0;
    -webkit-box-shadow: 0px 3px 6px 0px rgba(51, 51, 51, 0.2);
    box-shadow: 0px 3px 6px 0px rgba(51, 51, 51, 0.2);
    background-color: #fff;
    z-index: -1;
    -webkit-transition: 0.3s;
    transition: 0.3s;
  }

  .l_wrap.is-navOpen::after {
    height: 349px;
    z-index: 6;
  }

  .l_wrap.is-navlongOpen::after {
    height: 527px;
    z-index: 6;
  }
}

.l_main {
  position: relative;
  overflow: hidden;
}

.l_main.is-navOpen::after {
  content: "";
  background-color: #000;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0.6;
  width: 100%;
  z-index: 1;
}

.l_section {
  background-color: #fff9ed;
}

@media print,
screen and (min-width: 1025px) {
  .l_section {
    padding: 5rem 0 6rem;
  }
}

@media print,
screen and (min-width: 751px) and (max-width: 1024px) {
  .l_section {
    padding: 4.25rem 0 5rem;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_section {
    padding: 2.5rem 0;
    padding-top: 1rem;
  }
}

.l_inner {
  width: 100%;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 50px;
  padding-right: 50px;
  position: relative;
  z-index: 1;
}

@media print,
screen and (min-width: 751px) and (max-width: 1024px) {
  .l_inner {
    padding-left: 35px;
    padding-right: 35px;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_inner {
    padding-left: 4vw;
    padding-right: 4vw;
  }
}

.l_page-mv {
  color: #fff;
  position: relative;
}

@media print,
screen and (min-width: 1025px) {
  .l_page-mv {
    height: 480px;
  }
}

@media print,
screen and (min-width: 751px) and (max-width: 1024px) {
  .l_page-mv {
    height: 300px;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_page-mv {
    height: 84vw;
  }
}

.l_page-mv .l_inner {
  height: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.l_page-mv__title {
  display: block;
  font-size: 54px;
  font-size: 3.375rem;
  text-align: center;
  letter-spacing: 5.6px;
  font-weight: 300;
  line-height: 1.2;
}

@media print,
screen and (min-width: 1025px) {
  .l_page-mv__title.is-recruit {
    font-size: 36px;
    font-size: 2.25rem;
  }
}

@media print,
screen and (min-width: 751px) and (max-width: 1024px) {
  .l_page-mv__title.is-recruit {
    font-size: 28px;
    font-size: 1.75rem;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_page-mv__title.is-recruit {
    font-size: 20px;
    font-size: 5.33333vw;
    padding-top: 1.8rem;
  }
}

@media print,
screen and (min-width: 751px) and (max-width: 1024px) {
  .l_page-mv__title {
    font-size: 42px;
    font-size: 2.625rem;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_page-mv__title {
    font-size: 40px;
    font-size: 10.66667vw;
    padding-top: 1.8rem;
  }
}

.l_page-mv__title span {
  display: block;
  letter-spacing: 4.8px;
  padding-top: 1rem;
  font-size: 24px;
  font-size: 1.5rem;
}

@media print,
screen and (min-width: 751px) and (max-width: 1024px) {
  .l_page-mv__title span {
    font-size: 20px;
    font-size: 1.25rem;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_page-mv__title span {
    font-size: 15px;
    font-size: 4vw;
  }
}

@media all and (-ms-high-contrast: none) {
  .l_page-mv {
    overflow: hidden;
  }

  .l_page-mv__image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: auto;
    max-height: none;
  }
}

@media (min-width: 751px) and (max-width: 1450px) and (-ms-high-contrast: none) {
  .l_page-mv {
    height: 30vw;
  }
}

@media print,
screen and (min-width: 1025px) {
  .l_youtube {
    margin-top: 120px;
    padding-top: 40px;
  }
}

@media (max-width: 1200px) {
  .l_youtube {
    margin-top: 10vw;
    padding-top: 3.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_youtube {
    margin-top: 4rem;
    padding-top: 0;
  }
}

.l_youtube-bg {
  background-color: #fff;
  width: 100%;
  height: 100%;
}

.l_youtube--list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  max-width: 184px;
  margin: 0 auto;
}

.l_youtube-link {
  background-color: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

@media print,
screen and (min-width: 1025px) {
  .l_youtube-link {
    height: 104px;
  }
}

@media (max-width: 1200px) {
  .l_youtube-link {
    height: 8.66vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_youtube-link {
    height: 100%;
    padding: 3rem 0;
  }
}

@media print,
screen and (min-width: 1025px) {
  .l_youtube-link {
    -webkit-transition: 0.35s;
    transition: 0.35s;
  }

  .l_youtube-link:hover {
    opacity: 0.6;
  }
}

.l_youtube-img {
  max-width: 107px;
  width: 107px;
}

@media print,
screen and (max-width: 750px) {
  .l_youtube-img {
    max-width: 7.3rem;
    width: 7.3rem;
  }
}

.l_facebook-link {
  background-color: #fff;
  margin-right: 40px;
}

@media print,
screen and (min-width: 1025px) {
  .l_facebook-link {
    -webkit-transition: 0.35s;
    transition: 0.35s;
  }

  .l_facebook-link:hover {
    opacity: 0.6;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_facebook-link {
    margin-right: 2.7rem;
  }
}

.l_facebook-img {
  max-width: 35.98px;
  width: 35.98px;
}

@media print,
screen and (max-width: 750px) {
  .l_facebook-img {
    max-width: 2.57rem;
    width: 2.57rem;
  }
}

.l_contact-bg {
  background-color: #e50113;
  width: 100%;
  height: 100%;
}

.l_contact__inner {
  padding-top: 20px;
  padding-bottom: 40px;
}

@media (max-width: 1200px) {
  .l_contact__inner {
    padding-top: 1.66vw;
    padding-bottom: 3.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_contact__inner {
    padding-top: 2.4rem;
    padding-bottom: 3.2rem;
  }
}

.l_contact_list>ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

@media print,
screen and (max-width: 750px) {
  .l_contact_list>ul {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-flow: column;
    flex-flow: column;
  }
}

.l_contact_list>ul>li {
  width: 400px;
  padding: 10px 50px;
  border-left: 1px solid #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.l_contact__inner.chile .l_contact--title {
  margin-bottom: 5px;
}

.l_contact__inner.chile .l_contact--title p {
  font-size: 1.85rem;
}

.l_contact__inner.chile .l_contact--lead_title {
  margin-bottom: 0;
}

.l_contact__inner.chile .l_contact_list>ul>li {
  padding: 10px;
}

.l_contact__inner.chile .l_contact_list>ul>li .l_contact--lead_title p,
.l_contact__inner.chile .l_contact_list>ul>li .l_contact--lead_txt p {
  letter-spacing: 0.5px;
  line-height: 1.5;
  font-size: 0.85rem;
}

.l_contact_list>ul>li:first-child {
  border-left: none;
}

.l_contact_list>ul>li:nth-child(2) {
  padding-left: 0;
}

@media (max-width: 1200px) {
  .l_contact_list>ul>li {
    width: 33.33vw;
    padding-right: 4.16vw;
    padding-left: 4.16vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_contact_list>ul>li {
    margin-top: 2rem;
    width: 100%;
    padding: 0;
    border-left: none;
  }

  .l_contact_list>ul>li:first-child {
    margin-top: 0;
  }
}

.l_contact--lead_title p,
.l_contact--lead_title a,
.l_contact--lead_txt p,
.l_contact--lead_txt a,
.l_contact--lead_link p,
.l_contact--lead_link a {
  color: #fff;
  text-align: center;
  line-height: 1.75;
  font-size: 14px;
  font-size: 0.875rem;
}

@media (max-width: 1200px) {

  .l_contact--lead_title p,
  .l_contact--lead_title a,
  .l_contact--lead_txt p,
  .l_contact--lead_txt a,
  .l_contact--lead_link p,
  .l_contact--lead_link a {
    font-size: 1.16vw;
  }
}

@media print,
screen and (max-width: 750px) {

  .l_contact--lead_title p,
  .l_contact--lead_title a,
  .l_contact--lead_txt p,
  .l_contact--lead_txt a,
  .l_contact--lead_link p,
  .l_contact--lead_link a {
    font-size: 14px;
    font-size: 3.73333vw;
  }
}

.l_contact--lead_title {
  font-weight: bold;
  margin-bottom: 0.4rem;
}


/* .is-hkg .l_contact--lead_txt {
  margin-bottom: 30px;
}

@media (max-width: 1200px) {
  .is-hkg .l_contact--lead_txt {
    margin-bottom: 30px;
  }
}
*/

@media print,
screen and (max-width: 750px) {
  .is-hkg .l_contact--lead_txt {
    margin-bottom: 1rem;
  }
}

.l_contact--lead_txt a {
  display: inline-block;
  -webkit-transition: 0.35s;
  transition: 0.35s;
}

@media print,
screen and (min-width: 1025px) {
  .l_contact--lead_txt a:hover {
    opacity: 0.7;
  }
}

.l_contact--lead_link a {
  display: block;
  -webkit-text-decoration: underline #fff;
  text-decoration: underline #fff;
  -webkit-transition: 0.35s;
  transition: 0.35s;
}

@media print,
screen and (min-width: 1025px) {
  .l_contact--lead_link a:hover {
    opacity: 0.7;
  }
}

.l_contact--title {
  margin-bottom: 25px;
}

@media (max-width: 1200px) {
  .l_contact--title {
    margin-bottom: 2.08vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_contact--title {
    margin-bottom: 1rem;
  }
}

.l_contact--title P {
  color: #fff;
  text-align: center;
  line-height: 1.75;
  font-weight: bold;
  font-size: 36px;
  font-size: 2.25rem;
}

@media (max-width: 1200px) {
  .l_contact--title P {
    font-size: 3vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_contact--title P {
    font-size: 26px;
    font-size: 6.93333vw;
  }
}

.l_contact--btn p {
  width: 293px;
  height: 48px;
}

@media (max-width: 1200px) {
  .l_contact--btn p {
    width: 24.41vw;
    height: 4vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_contact--btn p {
    width: 21.5rem;
    height: 3.5rem;
  }
}

.l_contact--btn p a {
  border-radius: 24px;
  font-size: 16px;
  font-size: 1rem;
}

@media (max-width: 1200px) {
  .l_contact--btn p a {
    font-size: 1.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .l_contact--btn p a {
    border-radius: 2rem;
    font-size: 14px;
    font-size: 3.73333vw;
  }
}

.l_contact--btn p a:hover {
  color: #e50113;
  background-color: #fff;
  background-image: url(../images/common/icon_arrow_red.svg);
}

.js-slideIn {
  -webkit-transform: translateY(3.5rem);
  transform: translateY(3.5rem);
  opacity: 0;
  -webkit-transition: opacity 0.4s ease, -webkit-transform 0.95s ease;
  transition: opacity 0.4s ease, -webkit-transform 0.95s ease;
  transition: opacity 0.4s ease, transform 0.95s ease;
  transition: opacity 0.4s ease, transform 0.95s ease, -webkit-transform 0.95s ease;
}

.js-slideIn.is-show {
  -webkit-transform: translateY(0);
  transform: translateY(0);
  opacity: 1;
}

.js-slideIn__item {
  -webkit-transform: translateY(3.5rem);
  transform: translateY(3.5rem);
  opacity: 0;
  -webkit-transition: opacity 0.4s ease, -webkit-transform 0.95s ease;
  transition: opacity 0.4s ease, -webkit-transform 0.95s ease;
  transition: opacity 0.4s ease, transform 0.95s ease;
  transition: opacity 0.4s ease, transform 0.95s ease, -webkit-transform 0.95s ease;
}

.js-slideIn__item.is-show {
  -webkit-transform: translateY(0);
  transform: translateY(0);
  opacity: 1;
}

.js-orderIn>* {
  -webkit-transform: translateY(2.5rem);
  transform: translateY(2.5rem);
  opacity: 0;
  -webkit-transition: opacity 0.7s ease-out, -webkit-transform 0.6s ease-out;
  transition: opacity 0.7s ease-out, -webkit-transform 0.6s ease-out;
  transition: opacity 0.7s ease-out, transform 0.6s ease-out;
  transition: opacity 0.7s ease-out, transform 0.6s ease-out, -webkit-transform 0.6s ease-out;
}

.js-orderIn>*.is-show {
  -webkit-transform: translateY(0);
  transform: translateY(0);
  opacity: 1;
}

.js-headerIn {
  -webkit-transform: translateY(1.5rem);
  transform: translateY(1.5rem);
  opacity: 0;
  -webkit-transition: opacity 0.8s ease, -webkit-transform 0.75s ease;
  transition: opacity 0.8s ease, -webkit-transform 0.75s ease;
  transition: opacity 0.8s ease, transform 0.75s ease;
  transition: opacity 0.8s ease, transform 0.75s ease, -webkit-transform 0.75s ease;
}

.js-headerIn.is-show {
  -webkit-transform: translateY(0);
  transform: translateY(0);
  opacity: 1;
}

.js-headerIn__item {
  -webkit-transform: translateY(1.5rem);
  transform: translateY(1.5rem);
  opacity: 0;
  -webkit-transition: opacity 0.8s ease, -webkit-transform 0.55s ease;
  transition: opacity 0.8s ease, -webkit-transform 0.55s ease;
  transition: opacity 0.8s ease, transform 0.55s ease;
  transition: opacity 0.8s ease, transform 0.55s ease, -webkit-transform 0.55s ease;
}

.js-headerIn__item.is-show {
  -webkit-transform: translateY(0);
  transform: translateY(0);
  opacity: 1;
}

.is-anime__Upin {
  opacity: 0;
  -webkit-transform: translateY(-1.5rem);
  transform: translateY(-1.5rem);
  -webkit-transition: opacity 0.4s ease, -webkit-transform 0.95s ease;
  transition: opacity 0.4s ease, -webkit-transform 0.95s ease;
  transition: opacity 0.4s ease, transform 0.95s ease;
  transition: opacity 0.4s ease, transform 0.95s ease, -webkit-transform 0.95s ease;
}

.is-anime__Upin.is-show {
  opacity: 1;
  -webkit-transform: translateY(0);
  transform: translateY(0);
}

.is-anime__Downin {
  opacity: 0;
  -webkit-transform: translateY(1.5rem);
  transform: translateY(1.5rem);
  -webkit-transition: opacity 0.4s ease, -webkit-transform 0.95s ease;
  transition: opacity 0.4s ease, -webkit-transform 0.95s ease;
  transition: opacity 0.4s ease, transform 0.95s ease;
  transition: opacity 0.4s ease, transform 0.95s ease, -webkit-transform 0.95s ease;
}

.is-anime__Downin.is-show {
  opacity: 1;
  -webkit-transform: translateY(0);
  transform: translateY(0);
}

.is-anime__Rightin {
  opacity: 0;
  -webkit-transform: translateX(-2.5rem);
  transform: translateX(-2.5rem);
  -webkit-transition: opacity 0.4s ease, -webkit-transform 0.95s ease;
  transition: opacity 0.4s ease, -webkit-transform 0.95s ease;
  transition: opacity 0.4s ease, transform 0.95s ease;
  transition: opacity 0.4s ease, transform 0.95s ease, -webkit-transform 0.95s ease;
}

.is-anime__Rightin.is-show {
  opacity: 1;
  -webkit-transform: translateX(0);
  transform: translateX(0);
}

.uq_top__lead--banner {
  margin: 0 2rem;
  width: 30%;
}

.uq_top__lead--banner--sgp {
  margin: 30px 2rem 0;
  width: 100%;
}

.uq_top__lead--banner--sgp-sp {
  display: none;
}

@media print,
screen and (max-width: 750px) {
  .uq_top__lead--banner {
    display: none;
  }

  .uq_top__lead--banner--sgp {
    display: none;
  }

  .uq_top__lead--banner--sgp-sp {
    display: block;
    margin: 0 0rem 15px;
    width: 100%;
  }
}

.is-anime__Leftin {
  opacity: 0;
  -webkit-transform: translateX(2.5rem);
  transform: translateX(2.5rem);
  -webkit-transition: opacity 0.4s ease, -webkit-transform 0.95s ease;
  transition: opacity 0.4s ease, -webkit-transform 0.95s ease;
  transition: opacity 0.4s ease, transform 0.95s ease;
  transition: opacity 0.4s ease, transform 0.95s ease, -webkit-transform 0.95s ease;
  display: flex;
  align-items: center;
}

.is-anime__Leftin.is-show {
  opacity: 1;
  -webkit-transform: translateX(0);
  transform: translateX(0);
}

.is-anime__item {
  -webkit-transform: translateY(2.5rem);
  transform: translateY(2.5rem);
  opacity: 0;
  -webkit-transition: opacity 0.4s ease, -webkit-transform 0.95s ease;
  transition: opacity 0.4s ease, -webkit-transform 0.95s ease;
  transition: opacity 0.4s ease, transform 0.95s ease;
  transition: opacity 0.4s ease, transform 0.95s ease, -webkit-transform 0.95s ease;
}

.is-anime__item.is-show {
  -webkit-transform: translateY(0);
  transform: translateY(0);
  opacity: 1;
}

.uq_top__lead--banner-sp {
  margin: 0 2.13rem;
  margin-top: 0.5rem;
}

.m_btn {
  display: table;
  border-collapse: separate;
  margin: 0 auto;
  position: relative;
}

.m_btn>a,
.m_btn>span,
.m_btn>input[type="submit"] {
  border-radius: 20px;
  border: 1px solid #e50113;
  font: inherit;
  font-family: "noto-sans-cjk-jp", "Noto Sans JP", "游ゴシック体", "Yu Gothic", YuGothic, sans-serif;
  font-weight: 500;
  letter-spacing: 0;
  font-size: 12px;
  font-size: 0.75rem;
  display: table-cell;
  text-align: center;
  vertical-align: middle;
  width: 100%;
  height: 2.5rem;
  background-color: #e50113;
  line-height: 1.45;
  text-decoration: none !important;
  padding: 0.25rem 2.5rem 0.25rem 1.5rem;
  border: 1px solid #e50113;
  background-color: #fff;
  color: #e50113;
  cursor: pointer;
  white-space: nowrap;
  background-image: url(../images/common/icon_arrow_red.svg);
  background-size: auto 9px;
  background-repeat: no-repeat;
  background-position: 93% center;
}

@media (max-width: 1200px) {

  .m_btn>a,
  .m_btn>span,
  .m_btn>input[type="submit"] {
    background-image: url(../images/common/icon_arrow_red.svg);
    background-size: auto 0.75vw;
  }
}

@media print,
screen and (max-width: 750px) {

  .m_btn>a,
  .m_btn>span,
  .m_btn>input[type="submit"] {
    font-size: 12px;
    font-size: 3.2vw;
    height: 3rem;
    border-radius: 2.3rem;
    background-size: auto 0.6rem;
  }
}

@media print,
screen and (min-width: 1025px) {

  .m_btn>a,
  .m_btn>input[type="submit"] {
    -webkit-transition: 0.35s;
    transition: 0.35s;
  }

  .m_btn>a:hover,
  .m_btn>input[type="submit"]:hover {
    color: #fff;
    background-color: #e50113;
    border-color: #e50113;
    background-image: url(../images/common/icon_arrow_white.svg);
    background-position: 95% center;
  }
}

.m_btn.is-em>a,
.m_btn.is-em>span {
  color: #fff;
  background-color: #f00;
  border-color: #f00;
}

.m_btn.is-em>a::after,
.m_btn.is-em>span::after {
  background-image: url(../images/common/icon_arrowW.png);
}

@-webkit-keyframes mack_fadein {
  0% {
    left: -100%;
  }

  100% {
    left: 0;
  }
}

@keyframes mack_fadein {
  0% {
    left: -100%;
  }

  100% {
    left: 0;
  }
}

@-webkit-keyframes mack_bgfadein {
  0% {
    /* left: 0; */
    width: 100%;
  }

  100% {
    /* left: 100%; */
    width: 0;
  }
}

@keyframes mack_bgfadein {
  0% {
    /* left: 0; */
    width: 100%;
  }

  100% {
    /* left: 100%; */
    width: 0;
  }
}

.new-release {
  color: #e50113;
  font-weight: bold;
  font-size: 42px;
  font-size: 2.625rem;
  text-align: center;
  padding: 10px 0;
  transform: translateY(-30px);
}

.m_mask .new-release {
  margin-top: 20px;
  padding: 0 0 0 6px;
  font-size: 20px;
  transform: translateY(0px);
}

@media (max-width: 750px) {
  .new-release {
    font-size: 2.9vw;
    transform: translateY(0px);
  }
}

.m_mask-wrap {
  display: table;
  overflow: hidden;
  margin: 0 auto;
  position: relative;
}

.m_mask-wrap .m_mask {
  display: table;
  position: relative;
  margin-bottom: 0.15em;
  left: -100%;
  overflow: hidden;
}

.m_mask-wrap .m_mask.is-show {
  -webkit-animation: mack_fadein 0.3s 0s ease-in-out forwards;
  animation: mack_fadein 0.3s 0s ease-in-out forwards;
}

.m_mask-wrap .m_mask-bg {
  display: block;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #fff;
}

.m_mask-wrap .m_mask-bg.is-show {
  -webkit-animation: mack_bgfadein 0.3s 0.3s ease-in-out forwards;
  animation: mack_bgfadein 0.3s 0.3s ease-in-out forwards;
}

.is-bg_grad .m_mask-bg {
  background: transparent -webkit-gradient(linear, left top, right top, from(#f5bd41), color-stop(66%, #ef7c31), to(#e50012)) 0% 0% no-repeat padding-box;
  background: transparent linear-gradient(90deg, #f5bd41 0%, #ef7c31 66%, #e50012 100%) 0% 0% no-repeat padding-box;
}

.is-bg_red .m_mask-bg {
  background: #e50113;
}

@media print,
screen and (min-width: 751px) {
  .m_slide-single {
    width: 580px;
    margin: 0 auto 6.5rem;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_slide-single {
    margin-bottom: 5.5rem;
  }
}

@media print,
screen and (min-width: 751px) {
  .m_slide-single .slick-dots {
    -webkit-transform: translate(-50%, 500%);
    transform: translate(-50%, 500%);
  }
}

@media print,
screen and (max-width: 750px) {
  .m_slide-single .slick-dots {
    -webkit-transform: translate(-50%, 300%);
    transform: translate(-50%, 300%);
  }
}

.m_slide-single .slick-dots__dot {
  width: 10px;
  height: 10px;
}

@media print,
screen and (max-width: 750px) {
  .m_slide-single .slick-dots__dot {
    width: 14px;
    height: 14px;
  }
}

.m_pagetop {
  position: fixed;
  right: 1rem;
  bottom: 1rem;
  z-index: 12;
  opacity: 0;
  -webkit-transition: 0.35s;
  transition: 0.35s;
}

.is-navOpen .m_pagetop {
  z-index: 1;
}

.m_pagetop.is-fadeIn {
  opacity: 1;
}

.m_pagetop a {
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  background: #ffffff;
  border: 1px solid #707070;
  border-radius: 50%;
}

@media print,
screen and (min-width: 751px) {
  .m_pagetop a {
    width: 60px;
    height: 60px;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_pagetop a {
    width: 60px;
    height: 60px;
  }
}

.m_pagetop a::before {
  content: "";
  background: url(../images/common/icon_pagetop.svg) no-repeat;
  -webkit-transform: translateY(50%) translateX(50%);
  transform: translateY(50%) translateX(50%);
  left: 50%;
  top: 50%;
  position: absolute;
  transform: translate(-50%, -50%);
}

@media print,
screen and (min-width: 1025px) {
  .m_pagetop a::before {
    -webkit-transition: 0.35s;
    transition: 0.35s;
  }
}

@media print,
screen and (min-width: 751px) {
  .m_pagetop a::before {
    width: 15px;
    height: 15px;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_pagetop a::before {
    width: 15px;
    height: 15px;
  }
}

@media print,
screen and (min-width: 1025px) {
  .m_pagetop>a:hover::before {
    margin: -1.3rem 0 1rem;
  }
}

.l-totop {
  text-align: center;
  padding: 1rem 0 1.75rem;
  position: relative;
  background-color: #fff;
  margin-top: -1rem;
}

@media print,
screen and (min-width: 751px) {
  .l-totop {
    display: none;
  }
}

.l-totop a {
  display: inline-block;
  font-size: 12px;
  font-size: 3.2vw;
  font-weight: 400;
  letter-spacing: 1.5px;
  text-align: center;
  width: 8em;
  padding-top: 4em;
  position: relative;
}

.l-totop a::before {
  content: "";
  display: block;
  width: 0.75em;
  height: 0.75em;
  border-top: 1px solid #333;
  border-left: 1px solid #333;
  position: absolute;
  top: 2.75em;
  left: 3.65em;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  -webkit-transition: top 0.3s;
  transition: top 0.3s;
}

@media print,
screen and (max-width: 750px) {
  .l-totop a::before {
    left: 3.45em;
  }
}

@media print,
screen and (min-width: 751px) {
  .l-totop a:hover::before {
    top: 1.25em;
  }
}

.m_small {
  vertical-align: 30%;
  padding-right: 5px;
}

.m_small_sgp {
  vertical-align: 175%;
  padding-right: 5px;
}

.m_small_sgp_one {
  vertical-align: -25%;
}

.m_small_sgp_features_one {
  vertical-align: -27%;
}

.m_small_sgp_mTop {
  vertical-align: 180%;
  padding-right: 5px;
}

.m_small_sgp_features {
  vertical-align: 130%;
  padding-right: 5px;
}

.m_small_sgp_top {
  vertical-align: 130%;
}

.text_sgp {
  display: none;
}

@media print,
screen and (min-width: 1025px) {
  .m_small {
    font-size: 11px;
    font-size: 0.6875rem;
  }

  .m_small_sgp {
    font-size: 4px;
  }

  .m_small_sgp_mTop {
    font-size: 4px;
  }

  .m_small_sgp_features {
    font-size: 3px;
  }

  .m_small_sgp_features_one {
    font-size: 9px;
  }

  .m_small_sgp_one {
    font-size: 11px;
  }
}

@media (max-width: 1200px) {
  .m_small {
    font-size: 0.91vw;
  }

  .m_small_sgp {
    font-size: 0.44vw;
  }

  .m_small_sgp_mTop {
    font-size: 0.6vw;
  }

  .m_small_sgp_one {
    font-size: 9px;
  }

  .m_small_sgp_features {
    font-size: 0.33vw;
    vertical-align: 80%;
  }
}

@media (min-width: 1200px) and (max-width: 1300px) {
  .text_sgp {
    display: block;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_small {
    font-size: 11px;
    font-size: 2.93333vw;
  }

  .m_small_sgp {
    font-size: 5px;
    vertical-align: 100%;
  }

  .m_small_sgp_one {
    font-size: 9px;
    vertical-align: -17%;
  }

  .m_small_sgp_mTop {
    font-size: 5px;
    vertical-align: 150%;
  }

  .m_small_sgp_features {
    vertical-align: 85%;
    padding-right: 5px;
  }

  .m_small_sgp_features {
    font-size: 1vw;
  }

  .m_small_sgp_features_one {
    font-size: 10px;
    vertical-align: -22%;
  }
}

.m_small-L {
  vertical-align: 0%;
  padding-right: 5px;
}

@media print,
screen and (min-width: 1025px) {
  .m_small-L {
    font-size: 20px;
    font-size: 1.25rem;
  }
}

@media (max-width: 1200px) {
  .m_small-L {
    font-size: 1.66vw;
  }

  .m_small_sgp_features_one {
    font-size: 8px;
    vertical-align: -24%;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_small-L {
    font-size: 16px;
    font-size: 4.26667vw;
  }
}

.m_small-SS {
  vertical-align: 14%;
  padding-right: 2px;
}

.m_small-SS_sgp {
  vertical-align: 14%;
  padding-right: 2px;
  position: absolute;
  left: 0;
  top: 0;
}

@media print,
screen and (min-width: 1025px) {
  .m_small-SS {
    font-size: 8px;
    font-size: 0.5rem;
  }
}

@media (max-width: 1200px) {
  .m_small-SS {
    font-size: 0.66vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_small-SS {
    font-size: 8px;
    font-size: 2.13333vw;
  }
}

.m_breadcrumbs {
  height: 40px;
}

.m_breadcrumbs-section {
  background-color: #fff9ed;
}

.m_breadcrumbs-section.is-bgWhite {
  background-color: #fff;
}

.m_breadcrumbs-section.is-in {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  background-color: transparent;
}

@media print,
screen and (max-width: 750px) {
  .m_breadcrumbs-section.is-in {
    position: relative;
  }
}

@media print,
screen and (min-width: 751px) {
  .m_breadcrumbs-section .l_inner {
    max-width: 100%;
  }
}

@media print,
screen and (min-width: 1025px) {
  .m_breadcrumbs {
    width: calc(100% + 70px);
  }
}

.m_breadcrumbs-inner {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
}

.m_breadcrumbs-inner span,
.m_breadcrumbs-inner a {
  font-size: 12px;
  font-size: 0.75rem;
  -webkit-transition: opacity 200ms ease;
  transition: opacity 200ms ease;
  color: #333;
  font-weight: 400;
}

.m_breadcrumbs-inner span:hover,
.m_breadcrumbs-inner a:hover {
  opacity: 0.8;
}

.m_breadcrumbs-inner>span {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
}

.m_breadcrumbs-inner>span:not(:first-of-type) {
  margin-left: 11px;
}

@media print,
screen and (max-width: 750px) {
  .m_breadcrumbs-inner>span:not(:first-of-type) {
    min-width: 4.4rem;
  }
}

.m_breadcrumbs-inner>span:not(:first-of-type):before {
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  content: "";
  display: inline-block;
  vertical-align: middle;
  border-style: solid;
  border-width: 0 0.5px 0.5px 0;
  vertical-align: middle;
  height: 5px;
  width: 5px;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  margin-top: 0;
  margin-right: 13px;
}

@media all and (-ms-high-contrast: none) {
  .m_breadcrumbs-inner>span:not(:first-of-type):before {
    margin-bottom: 1px !important;
  }
}

.m_breadcrumbs-inner>span:last-of-type {
  cursor: text;
  line-height: 1.2;
}

.m_breadcrumbs-inner>span:last-of-type:hover {
  opacity: 1;
}

.m_breadcrumbs-inner>span:last-of-type a,
.m_breadcrumbs-inner>span:last-of-type span {
  font-weight: 400;
}

.m_breadcrumbs-inner>span:last-of-type a {
  cursor: text;
  pointer-events: none;
}

.m_breadcrumbs-inner>span:last-of-type a:hover {
  opacity: 1;
}

.m_breadcrumbs-inner a {
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  height: 100%;
}

@-webkit-keyframes fadeInMenu {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes fadeInMenu {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.m_datail {
  background-color: #dbdbdb;
}

@media print,
screen and (min-width: 751px) {
  .m_datail {
    height: 60px;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_datail {
    height: 3.34rem;
  }
}

.m_datail_nav {
  background-color: #dbdbdb;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  width: 100%;
  z-index: 11;
}

@media print,
screen and (min-width: 751px) {
  .m_datail_nav {
    height: 60px;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_datail_nav {
    height: 3.34rem;
  }
}

.m_datail_nav.is_fixed {
  position: fixed;
  width: 100%;
  left: 0;
  top: 0;
  z-index: 11;
}

.is-navOpen .m_datail_nav {
  z-index: 1;
}

.m_datail_nav__inner {
  width: 100%;
}

@media print,
screen and (min-width: 751px) {
  .m_datail_nav__inner {
    max-width: 1280px;
    padding: 0 40px;
    margin-left: auto;
    margin-right: auto;
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_datail_nav__inner {
    padding: 0.7rem 0 0.7rem;
    padding-left: 5vw;
  }
}

.m_datail_nav__wrap {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media print,
screen and (max-width: 750px) {
  .m_datail_nav__wrap {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
  }
}

.m_datail_nav__name {
  font-weight: bold;
}

.is-green .m_datail_nav__name {
  color: #007f41;
}

.is-blue .m_datail_nav__name {
  color: #1d2088;
}

.is-chi .is-blue .m_datail_nav__list-area,
.is-page-sg .is-blue .m_datail_nav__list-area,
.is-phl .is-blue .m_datail_nav__list-area,
.is-idn .is-blue .m_datail_nav__list-area{
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

@media print,
screen and (min-width: 1025px) {
  .is-chi .is-blue .m_datail_nav__list-area--item,
  .is-page-sg .is-blue .m_datail_nav__list-area--item,
  .is-phl .is-blue .m_datail_nav__list-area--item,
  .is-idn  .is-blue .m_datail_nav__list-area--item{
    margin-left: 0px;
  }
}

@media print,
screen and (min-width: 751px) {
  .m_datail_nav__name {
    font-size: 21px;
    font-size: 1.3125rem;
    width: 25%;
  }
}

@media (max-width: 1200px) {
  .m_datail_nav__name {
    font-size: 1.75vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_datail_nav__name {
    font-size: 16px;
    font-size: 4.26667vw;
  }
}

@media print,
screen and (min-width: 751px) {
  .m_datail_nav__list {
    width: 70%;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_datail_nav__list {
    display: none;
    position: absolute;
    top: 3rem;
    background-color: #dbdbdb;
    width: 100vw;
    max-height: 100vh;
    left: 0px;
    overflow: auto;
  }
}

.m_datail_nav__list-area {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.is-green .m_datail_nav__list-area {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.is-blue .m_datail_nav__list-area {
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  justify-content: flex-start;
}

.is-vnm .is-blue .m_datail_nav__list-area {
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.is-en .is-green .m_datail_nav__list-area {
  -ms-flex-pack: distribute;
  justify-content: space-around;
}

@media print,
screen and (max-width: 750px) {
  .m_datail_nav__list-area {
    padding-top: 5vw;
    padding-bottom: 11rem;
    padding-left: 5vw;
    padding-right: 5vw;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-flow: column;
    flex-flow: column;
  }
}

@media print,
screen and (min-width: 1025px) {
  .is-blue .m_datail_nav__list-area--item {
    margin-left: 94px;
  }
}

@media (max-width: 1200px) {
  .is-blue .m_datail_nav__list-area--item {
    margin-left: 7.83vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .is-blue .m_datail_nav__list-area--item {
    margin-left: 0;
  }
}

@media print,
screen and (min-width: 1025px) {
  .is-vnm .is-blue .m_datail_nav__list-area--item {
    margin-left: 0px;
  }
}

@media (max-width: 1200px) {
  .is-vnm .is-blue .m_datail_nav__list-area--item {
    margin-left: 6.16vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .is-vnm .is-blue .m_datail_nav__list-area--item {
    margin-left: 0;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_datail_nav__list-area--item {
    border-bottom: 1px solid #707070;
  }

  .m_datail_nav__list-area--item:first-child {
    border-top: 1px solid #707070;
  }
}

.m_datail_nav__list-area--item a {
  display: block;
}

@media print,
screen and (min-width: 751px) {
  .m_datail_nav__list-area--item a {
    padding: 18px 0;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_datail_nav__list-area--item a {
    padding: 2.5vw 5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_datail_nav__list-area--item a.is-arrow_right {
    position: relative;
    background: url(../images/common/icon_arrow_right.svg) no-repeat right center;
    background-size: 1rem 1em;
    margin-right: 5vw;
  }
}

@media print,
screen and (min-width: 751px) {
  .m_datail_nav__list-area--item a.is-arrow_down {
    position: relative;
    background: url(../images/common/icon_arrow_down.svg) no-repeat right center;
    background-size: 0.7rem 0.7em;
    padding-right: 30px;
  }
}

.m_datail_nav__list-area--item a.is-pdf {
  position: relative;
  background: url(../images/common/icon_pdf.svg) no-repeat right center;
  background-size: 1rem 1em;
}

@media print,
screen and (min-width: 751px) {
  .m_datail_nav__list-area--item a.is-pdf {
    padding-right: 25px;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_datail_nav__list-area--item a.is-pdf {
    background-size: 1.2rem 1.2em;
    margin-right: 5vw;
  }
}

.m_datail_nav__list-area--item>a {
  position: relative;
  -webkit-transition: 0.35s;
  transition: 0.35s;
  font-weight: 500;
}

@media print,
screen and (min-width: 1025px) {
  .m_datail_nav__list-area--item>a {
    font-size: 18px;
    font-size: 1.125rem;
  }
}

@media (max-width: 1200px) {
  .m_datail_nav__list-area--item>a {
    font-size: 1.5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_datail_nav__list-area--item>a {
    font-size: 16px;
    font-size: 4.26667vw;
  }
}

@media print,
screen and (min-width: 751px) {
  .m_datail_nav__list-area--item>a::before {
    content: "";
    display: block;
    position: absolute;
    left: 0;
    top: 65%;
    bottom: -1rem;
    right: 0;
    z-index: 2;
  }

  .m_datail_nav__list-area--item>a::after {
    content: "";
    display: block;
    position: absolute;
    left: 0;
    bottom: -0.2rem;
    opacity: 0;
    width: 100%;
    z-index: 7;
    -webkit-transition: 0.35s;
    transition: 0.35s;
  }

  .is-green .m_datail_nav__list-area--item>a::after {
    border-bottom: 5px solid #007f41;
  }

  .is-blue .m_datail_nav__list-area--item>a::after {
    border-bottom: 5px solid #1d2088;
  }

  .m_datail_nav__list-area--item>a:hover::after {
    opacity: 1;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_datail_nav__list-area--item {
    cursor: pointer;
    position: relative;
  }

  .m_datail_nav__list-area--item--spAccoBtn {
    position: absolute;
    top: 0;
    right: 0;
    width: 3rem;
    height: 2.3rem;
    padding: 0.3rem;
    display: block;
  }

  .m_datail_nav__list-area--item--spAccoBtn::before,
  .m_datail_nav__list-area--item--spAccoBtn::after {
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    content: "";
    display: inline-block;
    vertical-align: middle;
    background-color: #333;
    bottom: 0;
    height: 1px;
    margin: auto;
    position: absolute;
    width: 1rem;
    -webkit-transition: -webkit-transform 200ms;
    transition: -webkit-transform 200ms;
    transition: transform 200ms;
    transition: transform 200ms, -webkit-transform 200ms;
    top: 5%;
    right: 5vw;
  }

  .m_datail_nav__list-area--item--spAccoBtn::after {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
  }

  .m_datail_nav__list-area--item--spAccoBtn.is-accoOpen::before,
  .m_datail_nav__list-area--item--spAccoBtn.is-accoOpen::after {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
  }

  .m_datail_nav__list-area--item--spAccoBtn.is-accoOpen::after {
    -webkit-transform: rotate(-180deg);
    transform: rotate(-180deg);
  }
}

.m_datail_nav__list-area--item span {
  letter-spacing: 2.8px;
  padding-right: 1rem;
}

.m_datail_nav__list-area--item .new {
  letter-spacing: 0;
  padding-right: 5px;
  text-align: center;
  font-size: 13px;
  font-weight: bold;
  color: #e50113;
}

.m_datail_nav__list-area--child {
  background-color: #dbdbdb;
}

@media print,
screen and (min-width: 751px) {
  .m_datail_nav__list-area--child {
    opacity: 0;
    display: none;
    padding: 2rem 2rem 1rem;
    padding-bottom: 43.5px;
    position: absolute;
    width: 100%;
    left: 0;
    bottom: 15px;
    z-index: 10;
    -webkit-transform: translateY(100%);
    transform: translateY(100%);
    -webkit-transition: 0.35s;
    transition: 0.35s;
    -webkit-animation: fadeInMenu 0.35s 0s ease-in-out forwards;
    animation: fadeInMenu 0.35s 0s ease-in-out forwards;
  }

  .is-navSubOpen .m_datail_nav__list-area--child {
    opacity: 1;
    display: block;
    z-index: 6;
  }
}

@media (max-width: 1200px) {
  .m_datail_nav__list-area--child {
    padding: 2.66vw 2.66vw 34.66vw;
    padding-bottom: 3.62vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_datail_nav__list-area--child {
    padding: 0;
    display: none;
  }
}

@media print,
screen and (min-width: 751px) {
  .m_datail_nav__list-area--child__inner {
    max-width: 1280px;
    padding: 0 40px;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: flex-start;
    padding-left: 100px;
  }

  .m_datail_nav__list-area--child__inner.is-center {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }

  .is-hkg .m_datail_nav__list-area--child__inner {
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: start;
  }

  .is-tha .m_datail_nav__list-area--child__inner,
  .is-idn .m_datail_nav__list-area--child__inner,
  .is-vnm .m_datail_nav__list-area--child__inner,
  .is-mys .m_datail_nav__list-area--child__inner,
  .is-mys_en .m_datail_nav__list-area--child__inner,
  .is-phl .m_datail_nav__list-area--child__inner {
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
}

@media (max-width: 1200px) {
  .m_datail_nav__list-area--child__inner {
    max-width: 106.66vw;
    padding: 0 3.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_datail_nav__list-area--child__inner {
    padding: 0;
    padding-left: 5vw;
  }
}

@media print,
screen and (min-width: 751px) {
  .m_datail_nav__list-area--child__item {
    border-right: 1px solid #b1b1b1;
    line-height: 1.75;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 0 30px;
    width: 20%;
  }

  .m_datail_nav__list-area--child__item:nth-child(6) {
    flex-direction: column;
    justify-content: flex-start;
  }

  .m_datail_nav__list-area--child__item:nth-child(6) a {
    padding: 18px 0 0;
  }

  .m_datail_nav__list-area--child__item:nth-child(6) .m_datail_nav__list-area--child--child .m_datail_nav__list-area--child--child__inner {
    padding-left: 0;
  }

  .m_datail_nav__list-area--child__item:nth-child(2) {
    justify-content: center;
  }
  .is-mys_md .m_datail_nav__list-area--child__item:nth-child(2) {
    justify-content: space-between;
  }
  .is-hkg .m_datail_nav__list-area--child__item,
  .is-idn .m_datail_nav__list-area--child__item,
  .is-vnm .m_datail_nav__list-area--child__item,
  .is-mys .m_datail_nav__list-area--child__item,
  .is-phl .m_datail_nav__list-area--child__item,
  .is-uae .m_datail_nav__list-area--child__item,
  .is-chi .m_datail_nav__list-area--child__item,
  .is-mys_md .m_datail_nav__list-area--child__item,
  .is-mys_gg .m_datail_nav__list-area--child__item ,
  .is-page-sg .m_datail_nav__list-area--child__item {
    width: 35%;
  }
  .is-uae .m_datail_nav__list-area--child__inner{
    justify-content: center;
  }
  .is-hkg .m_datail_nav__list-area--child__item:nth-child(5) {
    padding: 0 15px;
  }

  .is-hkg .m_datail_nav__list-area--child__item:nth-child(5) .m_datail_nav__list-area--child--child {
    flex: 0 0 auto;
    text-align: right;
  }

  .is-hkg .m_datail_nav__list-area--child__item:nth-child(5) .m_datail_nav__list-area--child--child__inner {
    padding-left: 10px;
  }

  .is-hkg .m_datail_nav__list-area--child__item:nth-child(4) a {
    padding: 18px 0 0;
  }
}

@media print and (max-width: 1200px),
screen and (min-width: 751px) and (max-width: 1200px) {
  .m_datail_nav__list-area--child__item {
    padding: 0 2.5vw;
    box-sizing: content-box;
  }
}

@media print,
screen and (min-width: 751px) {
  .m_datail_nav__list-area--child__item:first-child {
    border-left: 1px solid #b1b1b1;
  }
}

@media print,
screen and (min-width: 1025px) {
  .m_datail_nav__list-area--child__item a {
    -webkit-transition: 0.35s;
    transition: 0.35s;
  }

  .is-green .m_datail_nav__list-area--child__item a:hover {
    color: #007f41;
  }

  .is-blue .m_datail_nav__list-area--child__item a:hover {
    color: #1d2088;
  }
}

@media (max-width: 1200px) {
  .m_datail_nav__list-area--child__item {
    font-size: 1.33vw;
  }

  .m_datail_nav__list-area--child__item .new {
    font-size: 1.084vw;
    padding-right: 0.417vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_datail_nav__list-area--child__item {
    border-top: 1px solid #707070;
    font-size: 16px;
    font-size: 4.26667vw;
  }
}

.m_datail_nav__list-area--child__item>a {
  font-weight: 500;
}

.m_datail_nav__list-area--child__item>a.is-nowrap {
  white-space: nowrap;
}

@media print,
screen and (max-width: 750px) {
  .m_datail_nav__list-area--child__item>a {
    font-size: 16px;
    font-size: 4.26667vw;
  }

  .m_datail_nav__list-area--child__item .new {
    display: inline;
    font-size: 4.26667vw;
    padding-right: 0.617vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_datail_nav__list-area--child__item {
    cursor: pointer;
    position: relative;
  }

  .m_datail_nav__list-area--child__item--spAccoBtn {
    position: absolute;
    top: 0;
    right: 0;
    width: 3rem;
    height: 2.3rem;
    padding: 0.3rem;
    display: block;
  }

  .m_datail_nav__list-area--child__item--spAccoBtn::before,
  .m_datail_nav__list-area--child__item--spAccoBtn::after {
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    content: "";
    display: inline-block;
    vertical-align: middle;
    background-color: #333;
    bottom: 0;
    height: 1px;
    margin: auto;
    position: absolute;
    width: 1rem;
    -webkit-transition: -webkit-transform 200ms;
    transition: -webkit-transform 200ms;
    transition: transform 200ms;
    transition: transform 200ms, -webkit-transform 200ms;
    top: 5%;
    right: 5vw;
  }

  .m_datail_nav__list-area--child__item--spAccoBtn::after {
    -webkit-transform: rotate(90deg);
    transform: rotate(90deg);
  }

  .m_datail_nav__list-area--child__item--spAccoBtn.is-accoOpen::before,
  .m_datail_nav__list-area--child__item--spAccoBtn.is-accoOpen::after {
    -webkit-transform: rotate(180deg);
    transform: rotate(180deg);
  }

  .m_datail_nav__list-area--child__item--spAccoBtn.is-accoOpen::after {
    -webkit-transform: rotate(-180deg);
    transform: rotate(-180deg);
  }
}

.m_datail_nav__list-area--child--child {
  background-color: #dbdbdb;
}

@media print,
screen and (min-width: 751px) {
  .m_datail_nav__list-area--child--child.is-navOpen {
    opacity: 1;
    z-index: 6;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_datail_nav__list-area--child--child {
    display: none;
  }
}

@media print,
screen and (min-width: 751px) {
  .m_datail_nav__list-area--child--child__inner {
    max-width: 1280px;
    padding: 0 40px;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    padding-right: 0;
    padding-left: 30px;
  }
}

@media (max-width: 1200px) {
  .m_datail_nav__list-area--child--child__inner {
    max-width: 106.66vw;
    /* padding: 0 3.33vw; */
    padding-left: 2.5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_datail_nav__list-area--child--child__inner {
    padding: 0;
    padding-left: 5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_datail_nav__list-area--child--child__item {
    border-top: 1px solid #acacac;
  }
}

@media print,
screen and (min-width: 751px) {
  .m_datail_nav__list-area--child--child__item>a {
    padding: 16px 0 0;
  }
}

.m_datail_nav--navtrigger {
  z-index: 15;
}

@media print,
screen and (min-width: 751px) {
  .m_datail_nav--navtrigger {
    display: none;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_datail_nav--navtrigger {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-flow: column;
    flex-flow: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    font-size: 12px;
    font-size: 3.2vw;
  }

  .m_datail_nav--navtrigger p {
    margin-bottom: 0.1rem;
  }
}

.m_datail_nav--navtrigger_btn {
  width: 55px;
  height: 55px;
  margin-left: 9px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  z-index: 15;
}

@media print,
screen and (max-width: 750px) {
  .m_datail_nav--navtrigger_btn {
    width: 4.342rem;
    height: 1rem;
    margin-left: 0;
  }
}

.m_datail_nav--navtrigger_btn div {
  position: relative;
}

@media print,
screen and (max-width: 750px) {
  .m_datail_nav--navtrigger_btn div {
    width: 1.429rem;
    height: 0.5rem;
  }
}

.m_datail_nav--navtrigger_btn div span {
  width: 100%;
  height: 1px;
  display: block;
  position: absolute;
  left: 0px;
}

.is-green .m_datail_nav--navtrigger_btn div span {
  background: #007f41;
}

.is-blue .m_datail_nav--navtrigger_btn div span {
  background: #1d2088;
}

.m_datail_nav--navtrigger_btn div span:nth-child(1) {
  top: 0px;
}

.is-navOpen .m_datail_nav--navtrigger_btn div span:nth-child(1) {
  -webkit-transform: translate3d(0px, 4px, 0px) rotate(45deg);
  transform: translate3d(0px, 4px, 0px) rotate(45deg);
}

@media print,
screen and (max-width: 750px) {
  .is-navOpen .m_datail_nav--navtrigger_btn div span:nth-child(1) {
    -webkit-transform: translate3d(0px, 0.3rem, 0px) rotate(25deg);
    transform: translate3d(0px, 0.3rem, 0px) rotate(25deg);
  }
}

.m_datail_nav--navtrigger_btn div span:nth-child(2) {
  bottom: 0px;
}

.is-navOpen .m_datail_nav--navtrigger_btn div span:nth-child(2) {
  -webkit-transform: translate3d(0px, -5px, 0px) rotate(-45deg);
  transform: translate3d(0px, -5px, 0px) rotate(-45deg);
}

@media print,
screen and (max-width: 750px) {
  .is-navOpen .m_datail_nav--navtrigger_btn div span:nth-child(2) {
    -webkit-transform: translate3d(0px, -0.15rem, 0px) rotate(-25deg);
    transform: translate3d(0px, -0.15rem, 0px) rotate(-25deg);
  }
}

.m_datail_nav--navtrigger_btn span {
  -webkit-transition: all 0.4s ease;
  transition: all 0.4s ease;
}

.m_image_lead {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.m_image_lead+.m_image_lead {
  margin-top: 40px;
}

@media print,
screen and (max-width: 750px) {
  .m_image_lead {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-flow: column;
    flex-flow: column;
  }
}

.m_image_lead dt,
.m_image_lead dd {
  width: 48%;
}

@media print,
screen and (max-width: 750px) {

  .m_image_lead dt,
  .m_image_lead dd {
    width: 100%;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_image_lead dt {
    margin-bottom: 1rem;
  }
}

.m_image_lead dt .m_image_lead--img {
  width: 100%;
}

@media print,
screen and (max-width: 750px) {
  .m_image_lead dt .m_image_lead--img {
    padding: 0 5vw;
  }

  .is-blue .m_image_lead dt .m_image_lead--img {
    padding: 0;
  }
}

@media print,
screen and (min-width: 751px) {
  .m_image_lead dd {
    margin-left: 4%;
  }
}

.m_image_lead dd .m_image_lead--ttl {
  font-weight: bold;
  letter-spacing: 0.36px;
  line-height: 1.5;
  margin-bottom: 7px;
  margin-top: 23px;
  font-size: 18px;
  font-size: 1.125rem;
}

@media (max-width: 1200px) {
  .m_image_lead dd .m_image_lead--ttl {
    font-size: 1.5vw;
    margin-bottom: 0.58vw;
    margin-top: 1.91vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_image_lead dd .m_image_lead--ttl {
    font-size: 18px;
    font-size: 4.8vw;
    margin-bottom: 0.8rem;
    margin-top: 1.5rem;
  }
}

.m_image_lead dd .m_image_lead--ttl.is-mTop0 {
  margin-top: 0;
}

.m_image_lead dd .m_image_lead--txt {
  font-size: 16px;
  font-size: 1rem;
  font-weight: 300;
  line-height: 1.65;
  letter-spacing: 0.17px;
}

@media (max-width: 1200px) {
  .m_image_lead dd .m_image_lead--txt {
    font-size: 1.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_image_lead dd .m_image_lead--txt {
    font-size: 14px;
    font-size: 3.73333vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_image_lead.is-sprev+.m_image_lead.is-sprev.is-sprev {
    margin-top: 1.5rem;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_image_lead.is-sprev.is-sprev dt {
    -webkit-box-ordinal-group: 3;
    -ms-flex-order: 2;
    order: 2;
    margin-top: 1.5rem;
    margin-bottom: 0;
  }

  .m_image_lead.is-sprev.is-sprev dd {
    -webkit-box-ordinal-group: 2;
    -ms-flex-order: 1;
    order: 1;
  }
}

.qp-table table {
  border-collapse: collapse;
  margin: 0 auto;
  background-color: rgb(233, 235, 245);
  border-top: #ccc 1px solid;
  border-left: #ccc 1px solid;
}

.qp-table table tr:nth-child(odd) {
  background-color: #cdcfde;
}

.qp-table table tr td:nth-child(1) {
  width: 32%;
  font-weight: bold;
  background: #efefef;
}

.product-ingredients>table {
  width: 100%;
}

.product-ingredients>table td,
.product-ingredients>table th {
  border: 1px solid rgb(2, 2, 2);
  padding: 10px;
  font-weight: 300;
  line-height: 1.7;
}

.product-txt {
  line-height: 1.7;
}

.product-detail {
  margin-top: 34px;
  padding: 15px;
  line-height: 1.7;
  border: 1px solid rgb(2, 2, 2);
}

.qp-table>table th,
.qp-table>table td {
  padding: 15px;
  line-height: 1.7;
  background: #fff9ed;
  border-right: #ccc 1px solid;
  border-bottom: #ccc 1px solid;
}

.qp_title {
  font-weight: bold;
  text-align: center;
  line-height: 1.45;
  letter-spacing: 0;
  position: relative;
  font-size: 42px;
  font-size: 2.225rem;
  padding-bottom: 55px;
}

@media (max-width: 1200px) {
  .qp_title {
    font-size: 3.5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .qp_title {
    font-size: 26px;
    font-size: 6.93333vw;
  }
}

.m_title {
  font-weight: bold;
  text-align: center;
  line-height: 1.45;
  letter-spacing: 0;
  position: relative;
  font-size: 42px;
  font-size: 2.625rem;
}

@media (max-width: 1200px) {
  .m_title {
    font-size: 3.5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_title {
    font-size: 26px;
    font-size: 6.93333vw;
  }
}

.m_title:after {
  content: "";
  display: block;
  background-color: #007f41;
  width: 47px;
  height: 1px;
  margin: 13px auto 0;
}

.is-green .m_title:after {
  background-color: #007f41;
}

.is-blue .m_title:after {
  background-color: #1d2088;
}

.m_card dl {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.m_card dl+.m_card dl {
  margin-top: 40px;
}

@media print,
screen and (max-width: 750px) {
  .m_card dl {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-flow: column;
    flex-flow: column;
  }
}

.m_card dl dt,
.m_card dl dd {
  width: 48%;
}

@media print,
screen and (max-width: 750px) {

  .m_card dl dt,
  .m_card dl dd {
    width: 100%;
  }
}

@media print,
screen and (min-width: 751px) {
  .m_card dl dd {
    margin-left: 4%;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_card dl dd {
    margin-top: 1rem;
  }
}

.m_card dl .m_card--box {
  background-color: #fff;
  -webkit-box-shadow: 5px 10px 25px #0000001a;
  box-shadow: 5px 10px 25px #0000001a;
  position: relative;
  height: 100%;
  width: 100%;
  border-radius: 25px;
}

.m_card dl .m_card__inner {
  padding: 28px 27px;
  padding-left: 10px;
}

.m_card dl .m_card__inner.is-male {
  padding-left: 18px;
}

@media (max-width: 1200px) {
  .m_card dl .m_card__inner {
    padding: 2.33vw 2.25vw;
    padding-left: 0.83vw;
  }

  .m_card dl .m_card__inner.is-male {
    padding-left: 1.5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_card dl .m_card__inner {
    padding: 1.5rem 5vw;
  }
}

.m_card dl .m_card--flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.m_card dl .m_card--img {
  text-align: center;
  width: 18%;
  margin-top: 7px;
}

@media (max-width: 1200px) {
  .m_card dl .m_card--img {
    margin-top: 0.58vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_card dl .m_card--img {
    width: 29%;
    margin-top: 0.8rem;
  }
}

.m_card dl .m_card--img__female {
  max-width: 30px;
  margin: 0 auto;
}

@media (max-width: 1200px) {
  .m_card dl .m_card--img__female {
    max-width: 2.5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_card dl .m_card--img__female {
    max-width: 35%;
    margin-left: 19%;
  }
}

.m_card dl .m_card--img__male {
  max-width: 59.27px;
  margin: 0 auto;
}

@media (max-width: 1200px) {
  .m_card dl .m_card--img__male {
    max-width: 4.93vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_card dl .m_card--img__male {
    max-width: 74%;
    margin-left: 7%;
  }
}

.m_card dl .m_card--img__male.is-male02 {
  max-width: 30px;
  margin: 0 auto;
}

@media (max-width: 1200px) {
  .m_card dl .m_card--img__male.is-male02 {
    max-width: 2.5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_card dl .m_card--img__male.is-male02 {
    max-width: 35%;
    margin-left: auto;
  }
}

.m_card dl .m_card--txt {
  width: 78%;
}

@media print,
screen and (min-width: 751px) {
  .m_card dl .m_card--txt {
    margin-left: 2%;
  }
}

.m_card dl .m_card--txt p {
  font-size: 16px;
  font-size: 1rem;
  line-height: 1.75;
  font-weight: 300;
}

@media (max-width: 1200px) {
  .m_card dl .m_card--txt p {
    font-size: 1.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_card dl .m_card--txt p {
    font-size: 14px;
    font-size: 3.73333vw;
  }
}

.m_linup {
  padding-top: 40px;
  margin-top: 20px;
}

@media (max-width: 1200px) {
  .m_linup {
    padding-top: 3.33vw;
    margin-top: 1.66vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup {
    margin-top: 1rem;
    padding-top: 2.1rem;
  }

  .is-blue .m_linup {
    margin-top: 0;
    padding-top: 0;
  }
}

.m_linup.is-custom {
  margin-top: 40px;
}

@media (max-width: 1200px) {
  .m_linup.is-custom {
    margin-top: 3.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup.is-custom {
    margin-top: 1rem;
  }
}

.m_linup--title {
  text-align: center;
}

.m_linup--title h4 {
  font-size: 36px;
  font-size: 2.25rem;
  font-weight: 500;
}

@media (max-width: 1200px) {
  .m_linup--title h4 {
    font-size: 3vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--title h4 {
    font-size: 24px;
    font-size: 6.4vw;
  }
}

.is-green .m_linup--title h4 {
  color: #007f41;
}

.is-blue .m_linup--title h4 {
  color: #1d2088;
}

@media print,
screen and (min-width: 751px) {
  .m_linup--section {
    padding-top: 18px;
    margin-top: -18px;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--section {
    padding-top: 1.75rem;
  }
}

.m_linup--box {
  background-color: #fff;
  border-radius: 20px;
  -webkit-box-shadow: 5px 10px 25px #0000001a;
  box-shadow: 5px 10px 25px #0000001a;
  margin-top: 36px;
  width: 100%;
}

@media (max-width: 1200px) {
  .m_linup--box {
    margin-top: 3vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--box {
    margin-top: 1rem;
  }
}

.m_linup__inner {
  padding-top: 69px;
  padding-bottom: 36.5px;
  padding-right: 45px;
  padding-left: 45px;
}

@media (max-width: 1200px) {
  .m_linup__inner {
    padding-top: 5.75vw;
    padding-bottom: 3.04vw;
    padding-right: 3.75vw;
    padding-left: 3.75vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup__inner {
    padding: 2rem 5vw;
  }
}

@media print,
screen and (min-width: 751px) {
  .m_linup--area {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }

  .m_linup--area--left {
    width: 34.17%;
  }

  .m_linup--area--right {
    width: 60.08%;
    margin-left: 4.94%;
  }
}

@media print,
screen and (min-width: 1025px) {
  .m_linup--ttl {
    margin-bottom: 20px;
  }
}

@media (max-width: 1200px) {
  .m_linup--ttl {
    margin-bottom: 1.66vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--ttl {
    margin-bottom: 1rem;
  }
}

.l-qp--ttl p {
  font-size: 16px;
  font-size: 1rem;
  font-weight: 500;
  color: #e50113;
}

.l-qp--ttl p::after {
  content: "";
  display: block;
  background-color: #e50113;
  width: 28.28px;
  height: 1px;
  margin-top: 16px;
}

.l-qp--subttl p {
  font-size: 2rem;
  font-weight: 500;
  margin-top: 15px;
}

.m_linup--ttl p {
  font-size: 16px;
  font-size: 1rem;
  font-weight: 500;
}

@media (max-width: 1200px) {
  .m_linup--ttl p {
    font-size: 1.33vw;
  }

  .l-qp--subttl p {
    font-size: 2.66vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--ttl p {
    text-align: center;
    font-size: 16px;
    font-size: 4.26667vw;
  }

  .l-qp--subttl p {
    text-align: center;
    font-size: 28px;
    font-size: 5.46667vw;
  }
}

.is-green .m_linup--ttl p {
  color: #007f41;
}

.is-blue .m_linup--ttl p {
  color: #1d2088;
}

.m_linup--ttl:after {
  content: "";
  display: block;
  background-color: #007f41;
  width: 28.28px;
  height: 1px;
  margin-top: 16px;
}

.is-green .m_linup--ttl:after {
  background-color: #007f41;
}

.is-blue .m_linup--ttl:after {
  background-color: #1d2088;
}

@media print,
screen and (max-width: 750px) {
  .m_linup--ttl:after {
    margin-top: 13px;
    width: 3.6rem;
    margin-left: auto;
    margin-right: auto;
    margin-top: 0.9rem;
  }
}

.m_linup--subttl p {
  font-size: 32px;
  font-size: 2rem;
  font-weight: 500;
}

@media (max-width: 1200px) {
  .m_linup--subttl p {
    font-size: 2.66vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--subttl p {
    text-align: center;
    font-size: 28px;
    font-size: 7.46667vw;
  }
}

.m_linup--image {
  max-width: 14rem;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 0.6rem;
  padding-bottom: 1rem;
}

@media print,
screen and (max-width: 750px) {
  .m_linup--image {
    max-width: 12rem;
    padding-top: 1.7rem;
  }
}

.is-blue .m_linup--image {
  max-width: 13rem;
}

@media print,
screen and (max-width: 750px) {
  .is-blue .m_linup--image {
    max-width: 12rem;
  }
}

.m_linup__txt {
  font-size: 14px;
  font-size: 0.875rem;
  line-height: 1.6;
  font-weight: 300;
}

@media (max-width: 1200px) {
  .m_linup__txt {
    font-size: 1.16vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup__txt {
    font-size: 14px;
    font-size: 3.73333vw;
  }
}

.m_linup__lead {
  font-size: 18px;
  font-size: 1.125rem;
  line-height: 1.6;
  font-weight: 300;
  margin-top: 15px;
}

@media (max-width: 1200px) {
  .m_linup__lead {
    font-size: 1.5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup__lead {
    font-size: 14px;
    font-size: 3.73333vw;
  }
}

.m_linup__ann {
  font-size: 12px;
  font-size: 0.75rem;
  line-height: 1.6;
  font-weight: 300;
  margin-top: 15px;
}

@media (max-width: 1200px) {
  .m_linup__ann {
    font-size: 1vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup__ann {
    font-size: 12px;
    font-size: 3.2vw;
  }
}

.m_linup__ann.is-flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.m_linup__ann.is-flex span:first-child {
  margin-right: 0.3rem;
}


/* .m_linup--accodion {
    border-bottom: 1px solid #707070
} */

.m_linup--accodion.is-first {
  border-top: 1px solid #707070;
  margin-top: 34px;
}

@media (max-width: 1200px) {
  .m_linup--accodion.is-first {
    margin-top: 2.83vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--accodion>ul {
    padding: 1rem 0;
  }
}

@media print,
screen and (min-width: 1025px) {
  .m_linup--accodion>ul>li {
    margin-top: 30px;
  }

  .m_linup--accodion>ul>li:first-child {
    margin-top: 0;
  }

  .m_linup--accodion>ul>li:last-child {
    margin-bottom: 35px;
  }
}

@media (max-width: 1200px) {
  .m_linup--accodion>ul>li {
    margin-top: 2.5vw;
  }

  .m_linup--accodion>ul>li:first-child {
    margin-top: 0;
  }

  .m_linup--accodion>ul>li:last-child {
    margin-bottom: 2.91vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--accodion>ul>li {
    margin-top: 2rem;
  }

  .m_linup--accodion>ul>li:first-child {
    margin-top: 0;
  }
}

.m_linup--accodion.is-accoBtn ul {
  display: none;
}

@media print,
screen and (min-width: 751px) {
  .m_linup--accodion.is-accoBtnSp ul {
    display: block !important;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--accodion.is-accoBtnSp ul {
    display: none;
  }
}

.m_linup--accodion.is-accoDefaltOpen ul {
  display: block;
}

@media print,
screen and (max-width: 750px) {
  .m_linup--accodion.is-accoDefaltOpen ul {
    display: none;
  }
}

.m_linup--accodion__ttl {
  position: relative;
  padding-left: 18px;
  padding-bottom: 19px;
  padding-top: 19px;
}

@media (max-width: 1200px) {

  .m_qp--accodion__ttl,
  .m_linup--accodion__ttl {
    padding-left: 1.5vw;
    padding-bottom: 1.58vw;
    padding-top: 1.58vw;
  }
}

@media print,
screen and (max-width: 750px) {

  .m_qp--accodion__ttl,
  .m_linup--accodion__ttl {
    padding-bottom: 1rem;
    padding-top: 1rem;
    padding-left: 5vw;
  }
}

.m_qp--accodion__ttl.js-accoBtnSp,
.m_linup--accodion__ttl.js-accoBtn,
.m_linup--accodion__ttl.js-accoBtnSp {
  cursor: pointer;
}

.m_qp--accodion__ttl.js-accoBtnSp::before,
.m_qp--accodion__ttl.js-accoBtnSp::after,
.m_linup--accodion__ttl.js-accoBtn::before,
.m_linup--accodion__ttl.js-accoBtn::after,
.m_linup--accodion__ttl.js-accoBtnSp::before,
.m_linup--accodion__ttl.js-accoBtnSp::after {
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  content: "";
  display: inline-block;
  vertical-align: middle;
  background-color: #707070;
  bottom: 0;
  height: 1px;
  margin: auto;
  position: absolute;
  width: 1rem;
  -webkit-transition: -webkit-transform 200ms;
  transition: -webkit-transform 200ms;
  transition: transform 200ms;
  transition: transform 200ms, -webkit-transform 200ms;
  right: 13px;
  top: 0;
}

@media (max-width: 1200px) {

  .m_qp--accodion__ttl.js-accoBtnSp::before,
  .m_qp--accodion__ttl.js-accoBtnSp::after,
  .m_linup--accodion__ttl.js-accoBtn::before,
  .m_linup--accodion__ttl.js-accoBtn::after,
  .m_linup--accodion__ttl.js-accoBtnSp::before,
  .m_linup--accodion__ttl.js-accoBtnSp::after {
    right: 1.08vw;
  }
}

@media print,
screen and (max-width: 750px) {

  .m_qp--accodion__ttl.js-accoBtnSp::before,
  .m_qp--accodion__ttl.js-accoBtnSp::after,
  .m_linup--accodion__ttl.js-accoBtn::before,
  .m_linup--accodion__ttl.js-accoBtn::after,
  .m_linup--accodion__ttl.js-accoBtnSp::before,
  .m_linup--accodion__ttl.js-accoBtnSp::after {
    right: 5vw;
  }
}

.m_qp--accodion__ttl.js-accoBtnSp::after,
.m_linup--accodion__ttl.js-accoBtn::after,
.m_linup--accodion__ttl.js-accoBtnSp::after {
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
}

.m_qp--accodion__ttl.is-accoOpen::before,
.m_qp--accodion__ttl.is-accoOpen::after,
.m_linup--accodion__ttl.is-accoOpen::before,
.m_linup--accodion__ttl.is-accoOpen::after {
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}

.m_linup--accodion__ttl.is-accoOpen::after {
  -webkit-transform: rotate(-180deg);
  transform: rotate(-180deg);
}

@media print,
screen and (min-width: 751px) {

  .m_qp--accodion__ttl.js-accoBtnSp,
  .m_linup--accodion__ttl.js-accoBtnSp {
    cursor: text;
  }
}

.m_qp--accodion__ttl.js-accoBtnSp::before,
.m_qp--accodion__ttl.js-accoBtnSp::after,
.m_linup--accodion__ttl.js-accoBtnSp::before,
.m_linup--accodion__ttl.js-accoBtnSp::after {
  display: none;
}

@media print,
screen and (max-width: 750px) {

  .m_qp--accodion__ttl.js-accoBtnSp::before,
  .m_qp--accodion__ttl.js-accoBtnSp::after,
  .m_linup--accodion__ttl.js-accoBtnSp::before,
  .m_linup--accodion__ttl.js-accoBtnSp::after {
    display: block;
  }
}

.m_qp--accodion__ttl {
  padding-bottom: 19px;
  padding-top: 19px;
  position: relative;
}

.m_qp--accodion__ttl p {
  padding-left: 10px;
  font-size: 21px;
  font-size: 1.3125rem;
  font-weight: 400;
}

.m_linup--accodion__ttl p {
  font-size: 21px;
  font-size: 1.3125rem;
  font-weight: 400;
}

@media (max-width: 1200px) {
  .m_linup--accodion__ttl p {
    font-size: 1.75vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_qp--accodion__ttl p::before {
    width: 15px;
    height: 15px;
    top: 50%;
    left: 2px;
    transform: translate(0%, -50%);
  }

  .m_linup--accodion__ttl p {
    font-size: 16px;
    font-size: 4.26667vw;
  }
}

@media print,
screen and (min-width: 751px) {
  .m_linup--accodion__card {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }
}

@media print,
screen and (min-width: 1025px) {
  .m_linup--accodion__card--img {
    max-width: 104px;
    padding: 10px;
    margin-right: 30px;
    width: 100%;
  }
}

@media (max-width: 1200px) {
  .m_linup--accodion__card--img {
    max-width: 8.66vw;
    padding: 0.83vw;
    margin-right: 2.5vw;
  }

  .m_linup--accodion__card--img.is-noImage {
    width: 100%;
  }
}

@media print,
screen and (min-width: 751px) {
  .is-extra .m_linup--accodion__card--img {
    max-width: 35%;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--accodion__card--img {
    max-width: 46.67%;
    margin: 0 auto;
    margin-bottom: 1rem;
    padding: 0;
  }
}


/* box */

.box_add .m_linup--accodion__card--lead {
  width: 100%;
}

.m_linup--accodion__card--lead {
  width: 65%;
}

@media print,
screen and (max-width: 750px) {
  .m_linup--accodion__card--lead {
    width: 100%;
  }

  .box_add .m_linup--accodion__card--lead {
    width: 100%;
    margin-bottom: 28px;
  }

  .box_add .m_linup--accodion__card--lead:last-child {
    margin-bottom: 0;
  }
}

.is-blue .m_linup--accodion__card--lead {
  padding: 0 20px;
  width: 100%;
}

@media (max-width: 1200px) {
  .is-blue .m_linup--accodion__card--lead {
    padding: 0 1.66vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .is-blue .m_linup--accodion__card--lead {
    padding: 0 5vw;
  }
}

@media print,
screen and (min-width: 751px) {
  .is-extra .m_linup--accodion__card--lead {
    max-width: 65%;
  }

  .is-extra .box_add .m_linup--accodion__card--lead {
    max-width: 100%;
    margin-bottom: 30px;
  }

  .is-extra .box_add .m_linup--accodion__card--lead:last-child {
    margin-bottom: 0;
  }
}

.m_qp--accodion__card--lead {
  padding: 0 10px;
  width: 100%;
}

.m_qp--accodion__card--lead--txt,
.m_qp--accodion__card--lead--txt p {
  font-size: 18px;
  font-size: 1.125rem;
  line-height: 1.75;
  font-weight: 300;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}

.m_qp--accodion__card--lead--txt picture {
  float: right;
  margin-top: -30px;
  margin-left: 10px;
}

.m_linup--accodion__card--lead--ttl {
  font-size: 18px;
  font-size: 1.125rem;
  line-height: 1.75;
  font-weight: bold;
  margin-bottom: 10px;
}

.product-ingredients>table td:nth-child(2) {
  width: 120px;
  text-align: center;
}

@media (max-width: 1200px) {
  .m_linup--accodion__card--lead--ttl {
    font-size: 1.5vw;
    margin-bottom: 0.83vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--accodion__card--lead--ttl {
    font-size: 18px;
    font-size: 4.8vw;
    margin-bottom: 0.8rem;
  }

  .is-chi .m_linup--accodion__card--lead--ttl {
    letter-spacing: -0.25px;
  }

  .is-chi .m_linup--accodion__card--lead--txt {
    letter-spacing: -0.25px;
  }

  .m_qp--accodion__card--lead--txt picture {
    margin-top: 0;
  }
}

.m_linup--accodion__card--lead--txt {
  font-size: 14px;
  font-size: 0.875rem;
  line-height: 1.6;
  font-weight: 300;
  text-align: justify;
}

@media (max-width: 1200px) {
  .m_linup--accodion__card--lead--txt {
    font-size: 1.16vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--accodion__card--lead--txt {
    font-size: 14px;
    font-size: 3.73333vw;
  }
}

.is-blue .m_linup--accodion__card--lead--txt {
  font-size: 18px;
  font-size: 1.125rem;
  line-height: 1.6;
  font-weight: 300;
}

@media (max-width: 1200px) {
  .is-blue .m_linup--accodion__card--lead--txt {
    font-size: 1.5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .is-blue .m_linup--accodion__card--lead--txt {
    font-size: 14px;
    font-size: 3.73333vw;
  }
}

.m_linup--accodion__size {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
  -ms-flex-align: end;
  align-items: flex-end;
}

.m_linup_box {
  flex-wrap: wrap;
  justify-content: flex-start !important;
  row-gap: 35px;
}

.is-Dimension .m_linup--accodion__size,
.is-Fragrance .m_linup--accodion__size {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.is-Vantelin .m_linup--accodion__size {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

@media print,
screen and (max-width: 750px) {
  .is-Vantelin .m_linup--accodion__size {
    -ms-flex-flow: wrap;
    flex-flow: wrap;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
  }
}

.m_linup--accodion__size--box {
  width: 49%;
}

.m_linup--accodion__size--box:nth-child(2n) {
  margin-left: 2%;
}

@media print,
screen and (max-width: 750px) {

  .m_linup--accodion__size--box:nth-child(1),
  .m_linup--accodion__size--box:nth-child(2) {
    margin-bottom: 1.5rem;
  }

  .m_linup--accodion__size--box:nth-child(2n) {
    margin-left: 5vw;
  }
}

.is-Dimension .m_linup--accodion__size--box,
.is-Fragrance .m_linup--accodion__size--box {
  width: 25%;
  margin-left: 2%;
}

.is-Dimension .m_linup--accodion__size--box:first-child,
.is-Fragrance .m_linup--accodion__size--box:first-child {
  margin-left: 0;
}

@media print,
screen and (max-width: 750px) {

  .is-Dimension .m_linup--accodion__size--box,
  .is-Fragrance .m_linup--accodion__size--box {
    width: 33%;
    margin-left: 5vw;
  }

  .is-Dimension .m_linup--accodion__size--box:nth-child(1),
  .is-Dimension .m_linup--accodion__size--box:nth-child(2),
  .is-Dimension .m_linup--accodion__size--box:nth-child(3),
  .is-Fragrance .m_linup--accodion__size--box:nth-child(1),
  .is-Fragrance .m_linup--accodion__size--box:nth-child(2),
  .is-Fragrance .m_linup--accodion__size--box:nth-child(3) {
    margin-bottom: 1.5rem;
  }

  .is-Dimension .m_linup--accodion__size--box:first-child,
  .is-Fragrance .m_linup--accodion__size--box:first-child {
    margin-left: 0;
  }
}

.is-Vantelin .m_linup--accodion__size--box {
  width: 23%;
  margin-left: 2%;
}

.is-Vantelin .m_linup--accodion__size--box:first-child {
  margin-left: 0;
}

@media print,
screen and (max-width: 750px) {
  .is-Vantelin .m_linup--accodion__size--box {
    width: 49%;
    margin-left: 0;
  }

  .is-Vantelin .m_linup--accodion__size--box:nth-child(2n) {
    margin-left: 2%;
  }
}

.m_linup--accodion__size--box.is-XLL {
  width: 36%;
}
@media print,
screen and (min-width: 751px) {
.lumbar .m_linup--accodion__size--box.is-XLL {
  width: 45%;
}
}
@media print,
screen and (max-width: 750px) {
  .lumbar .m_linup--accodion__size--box.is-XLL {
    width: 100%;
  }
  .m_linup--accodion__size--box.is-XLL .m_linup--accodion__size--img {
     max-width:200px
  }
}


@media print,
screen and (max-width: 750px) {
  .m_linup--accodion__size--box.is-XLL {
    width: 49%;
  }
}

@media print,
screen and (min-width: 751px) {
  .m_linup--accodion__size__inner {
    max-width: 100%;
    margin: 0 auto;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--accodion__size__inner {
    padding: 0 5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--accodion__size--img {
    max-width: 110px;
    max-width: 86%;
    margin: 0 auto;
  }

  .is-Fragrance .m_linup--accodion__size--img {
    max-width: 100%;
  }
}

.m_linup--accodion__size--lead {
  text-align: center;
  font-size: 20px;
  font-size: 1.15rem;
  line-height: 1.6;
  font-weight: 300;
  margin-top: 1rem;
}

.lead_thumb p {
  text-align: left;
}

@media (max-width: 1200px) {
  .m_linup--accodion__size--lead {
    font-size: 1.66vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--accodion__size--lead {
    font-size: 14px;
    font-size: 3.73333vw;
    margin-top: 0.5rem;
  }

  .lead_thumb p {
    text-align: center;
  }
}

.m_linup--accodion__size--lead.is-midolFont,
.is-vnm .is-Fragrance .m_linup--accodion__size--lead {
  font-size: 16px;
  font-size: 1rem;
}

@media (max-width: 1200px) {

  .m_linup--accodion__size--lead.is-midolFont,
  .is-vnm .is-Fragrance .m_linup--accodion__size--lead {
    font-size: 1.33vw;
  }
}

@media print,
screen and (max-width: 750px) {

  .m_linup--accodion__size--lead.is-midolFont,
  .is-vnm .is-Fragrance .m_linup--accodion__size--lead {
    font-size: 14px;
    font-size: 3.73333vw;
  }
}

.m_linup--btn {
  margin-top: 36px;
}

@media (max-width: 1200px) {
  .m_linup--btn {
    margin-top: 3vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--btn {
    margin-top: 1.5rem;
  }
}

.m_linup--btn p {
  display: table;
  border-collapse: separate;
  margin: 0 auto;
  position: relative;
  width: 165px;
  height: 48px;
}

@media (max-width: 1200px) {
  .m_linup--btn p {
    width: 13.75vw;
    height: 4vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--btn p {
    width: 11.46rem;
    height: 3.2rem;
  }
}

.m_linup--btn p>a,
.m_linup--btn p>span {
  border-radius: 24px;
  border: 1px solid #afafaf;
  background-color: #afafaf;
  color: #fff;
  display: table-cell;
  cursor: pointer;
  font: inherit;
  font-size: 16px;
  font-size: 1rem;
  font-family: "noto-sans-cjk-jp", "Noto Sans JP", "游ゴシック体", "Yu Gothic", YuGothic, sans-serif;
  letter-spacing: 0;
  text-align: center;
  vertical-align: middle;
  line-height: 1.45;
  text-decoration: none !important;
  padding: 0;
  white-space: nowrap;
  width: 100%;
  background-image: url(../images/common/icon_arrow_up_white.svg);
  background-size: auto 7px;
  background-repeat: no-repeat;
  background-position: 90% center;
  -webkit-transition: 0.35s;
  transition: 0.35s;
}

@media (max-width: 1200px) {

  .m_linup--btn p>a,
  .m_linup--btn p>span {
    background-image: url(../images/common/icon_arrow_up_white.svg);
    background-size: auto 0.58vw;
    border-radius: 2vw;
    font-size: 1.33vw;
  }
}

@media print,
screen and (max-width: 750px) {

  .m_linup--btn p>a,
  .m_linup--btn p>span {
    border-radius: 1.65rem;
    background-size: auto 0.5rem;
    font-size: 16px;
    font-size: 4.26667vw;
  }
}

@media print,
screen and (min-width: 1025px) {

  .is-green .m_linup--btn p>a:hover,
  .is-green .m_linup--btn p>span:hover {
    background-color: #007f41;
  }

  .is-blue .m_linup--btn p>a:hover,
  .is-blue .m_linup--btn p>span:hover {
    background-color: #1d2088;
  }
}

.m_linup--main--image {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  margin-top: 47px;
}

@media (max-width: 1200px) {
  .m_linup--main--image {
    margin-top: 3.91vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--main--image {
    margin-top: 2.4rem;
  }
}

.m_linup--main--img {
  width: 100%;
  height: 240px;
  text-align: center;
}

@media (max-width: 1200px) {
  .m_linup--main--img {
    height: 20vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--main--img {
    height: 100%;
    width: 61%;
    margin: 0 auto;
  }
}

.m_linup--main--lead {
  text-align: center;
  margin-top: 35px;
}

@media (max-width: 1200px) {
  .m_linup--main--lead {
    margin-top: 2.91vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--main--lead {
    text-align: left;
    margin-top: 1.3rem;
  }
}

.m_linup--main--lead p {
  font-size: 16px;
  font-size: 1rem;
  line-height: 1.6;
  font-weight: 300;
  margin-top: 1rem;
}

@media (max-width: 1200px) {
  .m_linup--main--lead p {
    font-size: 1.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--main--lead p {
    font-size: 14px;
    font-size: 3.73333vw;
  }
}

.is-green .m_linup--main--lead p span {
  color: #007f41;
}

.is-blue .m_linup--main--lead p span {
  color: #1d2088;
}

.m_linup--main--lead p strong {
  font-weight: bold;
  margin-bottom: 1rem;
  font-size: 18px;
  font-size: 1.125rem;
}

@media (max-width: 1200px) {
  .m_linup--main--lead p strong {
    font-size: 1.5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--main--lead p strong {
    font-size: 16px;
    font-size: 4.26667vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--ec {
    margin-top: 2rem;
  }
}

.m_linup--ec--box {
  padding: 20px 20px 40px;
  width: 100%;
}

.is-green .m_linup--ec--box {
  background-color: #007f41;
}

.is-blue .m_linup--ec--box {
  background-color: #1d2088;
}

@media (max-width: 1200px) {
  .m_linup--ec--box {
    padding: 1.66vw 1.66vw 3.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--ec--box {
    padding: 1.3rem 5vw 3rem;
  }
}

.m_linup--ec--ttl {
  text-align: center;
}

.m_linup--ec--ttl p {
  color: #fff;
  font-size: 21px;
  font-size: 1.3125rem;
  font-weight: bold;
  margin-top: 1rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (max-width: 1200px) {
  .m_linup--ec--ttl p {
    font-size: 1.75vw;
    margin-top: 1.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--ec--ttl p {
    font-size: 20px;
    font-size: 5.33333vw;
  }
}

.m_linup--ec--ttl p span {
  margin-right: 12.7px;
}

@media (max-width: 1200px) {
  .m_linup--ec--ttl p span {
    margin-right: 1.05vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--ec--ttl p span {
    margin-right: 0.7rem;
  }
}

.m_linup--ec--list {
  margin-top: 10px;
}

@media (max-width: 1200px) {
  .m_linup--ec--list {
    margin-top: 0.83vw;
  }
}

.m_linup--ec--list ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-flow: column;
  flex-flow: column;
}

.m_linup--ec--list ul li {
  width: 100%;
}

@media print,
screen and (min-width: 1025px) {
  .m_linup--ec--list ul li {
    margin-top: 10px;
  }
}

@media (max-width: 1200px) {
  .m_linup--ec--list ul li {
    margin-top: 0.83vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--ec--list ul li {
    width: 100%;
    margin-top: 1rem;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--ec--list ul li:nth-child(2n) {
    margin-left: 0;
  }
}

.m_linup--ec--list ul li a {
  border-radius: 30px;
  background-color: #fff;
  color: #fff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  padding: 10px 18px;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 48px;
  background-size: 14px 11px;
  background-repeat: no-repeat;
  background-position: 93% center;
  -webkit-transition: 0.35s;
  transition: 0.35s;
}

.is-green .m_linup--ec--list ul li a {
  background-image: url(../images/common/icon_bank_green.svg);
}

.is-blue .m_linup--ec--list ul li a {
  background-image: url(../images/common/icon_bank_blue.svg);
}

@media (max-width: 1200px) {
  .m_linup--ec--list ul li a {
    width: 100%;
    padding: 0.83vw 1.5vw 0.83vw;
    height: 4vw;
    background-size: 1.16vw 0.91vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--ec--list ul li a {
    width: 100%;
    height: 3.3rem;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    background-size: 1rem;
    border-radius: 2.4rem;
  }
}

.m_linup--ec--list ul li a p {
  display: inline-block;
  width: 100%;
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  max-width: 100%;
  height: 25px;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

@media (max-width: 1200px) {
  .m_linup--ec--list ul li a p {
    height: 2.5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .m_linup--ec--list ul li a p {
    max-width: 60%;
    height: 2rem;
  }
}

.m_linup--ec--list ul li a p img {
  -o-object-fit: contain;
  object-fit: contain;
  font-family: "object-fit: contain;";
}

@media print,
screen and (min-width: 1025px) {
  .m_linup--ec--list ul li a:hover {
    opacity: 0.7;
  }
}

.slick-dots {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  position: absolute;
  padding-top: 2rem;
  left: 50%;
  bottom: 5%;
  -webkit-transform: translate(-50%, 100%);
  transform: translate(-50%, 100%);
  z-index: 3;
}

.is-top .slick-dots {
  bottom: 15%;
}

.is-navOpen .slick-dots {
  z-index: 1;
}

@media print,
screen and (min-width: 1025px) {
  .slick-dots li {
    cursor: pointer;
  }
}

.slick-dots__dot {
  width: 12px;
  height: 12px;
  border: 1px solid #c9c9c9;
  background-color: #c9c9c9;
  border-radius: 50%;
  margin: 0 0.45rem;
}


/*.is-mys.is-masks .slick-dots__dot,
.is-vnm .is-masks .slick-dots__dot,
.is-tha .is-masks .slick-dots__dot,
.is-mys_en .is-masks .slick-dots__dot,
.is-vnm .is-tvantelin .slick-dots__dot,
.is-idn .is-tvantelin .slick-dots__dot,
.is-phl .slick-dots__dot {
  display: none;
}
*/


/* .is-vnm .is-masks .slick-dots__dot,
.is-vnm .is-tvantelin .slick-dots__dot,
.is-idn .is-tvantelin .slick-dots__dot {
  display: none;
} */

@media print,
screen and (max-width: 750px) {
  .slick-dots__dot {
    margin: 0 0.75rem;
  }
}

.slick-active .slick-dots__dot {
  border: 1px solid #333333;
  background-color: #333333;
}

.slick-slide img {
  margin: 0 auto;
}

.slick-arrow {
  position: absolute;
  top: 60%;
  margin-top: -2rem;
  width: 1.5rem;
  height: 1.5rem;
  right: 0;
  cursor: pointer;
  z-index: 5;
}

@media (max-width: 1200px) {
  .slick-arrow {
    max-width: 5.53vw;
    width: 1.25vw;
    height: 1.25vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .slick-arrow {
    width: 1.5rem;
    height: 1.5rem;
  }
}

.slick-prev {
  left: 2.5rem;
  -webkit-transform: rotate(-135deg);
  transform: rotate(-135deg);
}

@media print,
screen and (min-width: 1441px) {
  .slick-prev {
    left: 50%;
    margin-left: -650px;
  }
}

@media print,
screen and (min-width: 751px) and (max-width: 1024px) {
  .slick-prev {
    left: 1.5rem;
  }
}

.slick-next {
  right: 2.5rem;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}

@media print,
screen and (min-width: 1441px) {
  .slick-next {
    right: 50%;
    margin-right: -650px;
  }
}

@media print,
screen and (min-width: 751px) and (max-width: 1024px) {
  .slick-next {
    right: 1.5rem;
  }
}

.m_slick__arrow {
  position: absolute;
  width: 20px;
  height: 20px;
  border: none;
  outline: none;
  margin: 0;
  padding: 0;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background: transparent;
  top: 50%;
  border-top: solid 1px #707070;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  z-index: 5;
}

.m_slick__arrow_prev {
  left: -1.5rem;
  border-left: solid 1px #707070;
  -webkit-transform-origin: left top;
  transform-origin: left top;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

.m_slick__arrow_next {
  right: -1.5rem;
  border-right: solid 1px #707070;
  -webkit-transform-origin: right top;
  transform-origin: right top;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}

@-webkit-keyframes fadezoom {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }

  100% {
    -webkit-transform: scale(1.2);
    transform: scale(1.2);
  }
}

@keyframes fadezoom {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }

  100% {
    -webkit-transform: scale(1.2);
    transform: scale(1.2);
  }
}

.uq_top-mv {
  width: 100%;
  height: 100%;
  position: relative;
  min-height: 860px;
}

@media print,
screen and (max-width: 750px) {
  .uq_top-mv {
    min-height: calc(100vh - 8rem);
    margin-top: 1rem;
  }
}

.uq_top-mv__slide--item:not(:first-child) {
  display: none;
}

.slick-initialized .uq_top-mv__slide--item {
  display: block;
}

.uq_top-mv__slide--item img {
  width: 100vw;
  height: 100vh;
  -o-object-fit: cover;
  object-fit: cover;
  font-family: "object-fit: cover;";
  height: calc(var(--vh, 1vh) * 100);
}

.uq_top-mv__banner {
  width: 100%;
  margin-top: 80px;
}

.uq_top-mv__banner a {
  display: block;
  text-align: center;
  background-color: #e1f9fb;
  padding: 5px 0;
}

@media print,
screen and (max-width: 750px) {
  .uq_top-mv__banner {
    width: 100%;
    margin-top: 3.35rem;
    margin-bottom: -1rem;
  }

  .uq_top-mv__banner a {
    padding: 0;
  }
}

.uq_top-mv__catchcopy {
  position: absolute;
  top: 25.9%;
  left: 18%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

@media (max-width: 1000px) {
  .uq_top-mv__catchcopy {
    top: 20.9%;
    left: 22%;
  }
}

@media print,
screen and (max-width: 750px) {
  .is-page-top .uq_top-mv__catchcopy {
    top: 24vw;
    left: 49%;
    width: 85%;
  }

  .is-page-mys .uq_top-mv__catchcopy {
    top: 50vw;
    left: 49%;
    width: 85%;
  }

  .uq_top-mv__catchcopy {
    top: 19.9%;
    left: 49%;
    width: 85%;
  }
}

.uq_top-mv__catchcopy p {
  line-height: 1.6;
  font-size: 54px;
  font-size: 3.375rem;
}

@media (max-width: 1200px) {
  .uq_top-mv__catchcopy p {
    font-size: 4.5vw;
  }
}

@media (max-width: 900px) {
  .uq_top-mv__catchcopy p {
    font-size: 48px;
    font-size: 3rem;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_top-mv__catchcopy p {
    font-size: 34px;
    font-size: 9.06667vw;
  }
}

.uq_top-mv .js-topSlide .slide-animation {
  -webkit-animation: fadezoom 8s 0s ease-in-out forwards;
  animation: fadezoom 8s 0s ease-in-out forwards;
}

.uq_top-mv__lead {
  position: absolute;
  bottom: -115px;
  left: 0;
  width: 100%;
}

@media (max-width: 1200px) {
  .uq_top-mv__lead {
    bottom: -9.59vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_top-mv__lead {
    bottom: -8.7rem;
  }
}

.uq_top-mv__lead--box {
  height: 185px;
  width: 84.72%;
  max-width: 1220px;
  background: #fff;
  border-top-right-radius: 40px;
  border-bottom-right-radius: 40px;
  position: relative;
}

@media (max-width: 1200px) {
  .uq_top-mv__lead--box {
    height: 15.41vw;
    border-top-right-radius: 3.33vw;
    border-bottom-right-radius: 3.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_top-mv__lead--box {
    height: 100%;
    width: 94.72%;
    border-top-right-radius: 1.3rem;
    border-bottom-right-radius: 1.3rem;
  }
}

.uq_top-mv__lead--box::after {
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  border-top-right-radius: 40px;
  border-bottom-right-radius: 40px;
  background: transparent -webkit-gradient(linear, left top, right top, from(#f5bd41), color-stop(66%, #ef7c31), to(#e50012)) 0% 0% no-repeat padding-box;
  background: transparent linear-gradient(90deg, #f5bd41 0%, #ef7c31 66%, #e50012 100%) 0% 0% no-repeat padding-box;
  bottom: -10px;
  left: -10px;
  z-index: -1;
}

@media (max-width: 1200px) {
  .uq_top-mv__lead--box::after {
    border-top-right-radius: 3.33vw;
    border-bottom-right-radius: 3.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_top-mv__lead--box::after {
    border-top-right-radius: 1.3rem;
    border-bottom-right-radius: 1.3rem;
  }
}

.uq_top-mv__lead--box__inner {
  max-width: 1080px;
  margin-left: auto;
  padding: 60px 0;
  padding-left: 40px;
  padding-right: 40px;
}

@media (max-width: 1200px) {
  .uq_top-mv__lead--box__inner {
    padding-top: 5vw;
    padding-bottom: 5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_top-mv__lead--box__inner {
    padding-top: 2.5rem;
    padding-bottom: 2.8rem;
  }
}

.uq_top-mv__lead--box--txt p {
  font-size: 21px;
  font-size: 1.3125rem;
  line-height: 1.75;
  font-weight: 300;
}

@media (max-width: 1200px) {
  .uq_top-mv__lead--box--txt p {
    font-size: 1.75vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_top-mv__lead--box--txt p {
    font-size: 14px;
    font-size: 3.73333vw;
  }
}

@media print,
screen and (min-width: 1025px) {
  .uq_top-vantelin_support--section {
    margin-top: 180px;
    padding-top: 40px;
  }
}

@media (max-width: 1200px) {
  .uq_top-vantelin_support--section {
    margin-top: 15vw;
    padding-top: 3.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_top-vantelin_support--section {
    margin-top: 14rem;
    padding-top: 1.7rem;
  }
}

.uq_top-vantelin_support--bg {
  width: 100%;
  height: 100%;
  position: relative;
}

.uq_top-vantelin_support--bg--img img {
  height: 450px;
  width: 100vw;
  -o-object-fit: cover;
  object-fit: cover;
  font-family: "object-fit: cover;";
}

@media (max-width: 1200px) {
  .uq_top-vantelin_support--bg--img img {
    height: 37.5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_top-vantelin_support--bg--img img {
    height: 100%;
  }
}

@media print,
screen and (min-width: 1025px) {
  .uq_top-mask--section {
    margin-top: 50px;
    padding-top: 40px;
  }
}

@media (max-width: 1200px) {
  .uq_top-mask--section {
    margin-top: 4.16vw;
    padding-top: 3.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_top-mask--section {
    margin-top: 4rem;
    padding-top: 1.9rem;
  }
}

@media print,
screen and (min-width: 1025px) {
  .uq_top-mask--section.is-maskMgnTop {
    margin-top: 180px;
    padding-top: 40px;
  }
}

@media (max-width: 1200px) {
  .uq_top-mask--section.is-maskMgnTop {
    margin-top: 15vw;
    padding-top: 3.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_top-mask--section.is-maskMgnTop {
    margin-top: 14rem;
    padding-top: 1.7rem;
  }
}

.uq_top-mask--bg {
  width: 100%;
  height: 100%;
  position: relative;
}

.uq_top-mask--bg--img img {
  height: 450px;
  width: 100vw;
  -o-object-fit: cover;
  object-fit: cover;
  font-family: "object-fit: cover;";
}

@media (max-width: 1200px) {
  .uq_top-mask--bg--img img {
    height: 37.5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_top-mask--bg--img img {
    height: 100%;
  }
}

.uq_top__lead {
  width: 100%;
}

.uq_top__lead--area {
  position: relative;
}

@media print,
screen and (min-width: 1025px) {
  .uq_top__lead--area {
    margin-top: -64px;
  }
}

@media (max-width: 1200px) {
  .uq_top__lead--area {
    margin-top: -5.34vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_top__lead--area {
    margin-top: -3rem;
  }
}

.uq_top__lead--box {
  width: 84.72%;
  max-width: 1220px;
  margin-left: auto;
  background: #fff;
  border-top-left-radius: 40px;
  border-bottom-left-radius: 40px;
  position: relative;
}

@media (max-width: 1200px) {
  .uq_top__lead--box {
    border-top-left-radius: 3.33vw;
    border-bottom-left-radius: 3.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_top__lead--box {
    height: 100%;
    width: 94.72%;
    border-top-left-radius: 1.3rem;
    border-bottom-left-radius: 1.3rem;
  }
}

.uq_top__lead--box.is-vantelin_support {
  height: 240px;
}

@media (max-width: 1200px) {
  .uq_top__lead--box.is-vantelin_support {
    height: 100%;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_top__lead--box.is-vantelin_support {
    height: 100%;
  }
}

.uq_top__lead--box::after {
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  border-top-left-radius: 40px;
  border-bottom-left-radius: 40px;
  background: transparent -webkit-gradient(linear, left top, right top, from(#f5bd41), color-stop(66%, #ef7c31), to(#e50012)) 0% 0% no-repeat padding-box;
  background: transparent linear-gradient(90deg, #f5bd41 0%, #ef7c31 66%, #e50012 100%) 0% 0% no-repeat padding-box;
  bottom: -10px;
  right: -10px;
  z-index: -1;
}

@media (max-width: 1200px) {
  .uq_top__lead--box::after {
    border-top-left-radius: 3.33vw;
    border-bottom-left-radius: 3.33vw;
  }
}

.uq_top__lead--box__inner {
  max-width: 1140px;
  margin-right: auto;
  padding: 40px 0 40px;
  padding-left: 60px;
  padding-right: 40px;
}

@media (max-width: 1200px) {
  .uq_top__lead--box__inner {
    padding-left: 5vw;
    padding-right: 3.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_top__lead--box__inner {
    padding-top: 2.3rem;
    padding-bottom: 2.5rem;
    padding-left: 2.13rem;
    padding-right: 2.13rem;
  }
}

.uq_top__lead--box--txt p {
  font-size: 21px;
  font-size: 1.3125rem;
  line-height: 1.75;
  font-weight: 300;
}

@media (max-width: 1200px) {
  .uq_top__lead--box--txt p {
    font-size: 1.75vw;
  }
}

.uq_top__lead__lineup {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  position: relative;
}

.uq_top__lead__lineup--img {
  max-width: 302px;
  margin-right: 40px;
  width: 40%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

@media (max-width: 1200px) {
  .uq_top__lead__lineup--img {
    max-width: 25.16vw;
    margin-right: 3.33vw;
    width: 30%;
  }
}

@media print,
screen and (min-width: 751px) and (max-width: 1024px) {
  .uq_top__lead__lineup--img {
    width: 25%;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_top__lead__lineup--img {
    display: none;
    width: 100%;
    max-width: 100%;
    padding: 0 5vw;
    margin-top: 3rem;
  }
}

.uq_top__lead__lineup--txt {
  max-width: 500px;
  width: 45%;
}

.uq_top__lead__lineup--txt.no-catchcopy {
  display: flex;
  justify-content: center;
  flex-direction: column;
}

@media (max-width: 1200px) {
  .uq_top__lead__lineup--txt {
    max-width: 41.66vw;
  }
}

@media print,
screen and (min-width: 751px) and (max-width: 1024px) {
  .uq_top__lead__lineup--txt {
    width: 48%;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_top__lead__lineup--txt {
    width: 100%;
    max-width: 100%;
  }
}

.uq_top__lead__lineup--catchcopy {
  font-size: 28px;
  font-size: 1.75rem;
  font-weight: 300;
  margin-bottom: 3rem;
  letter-spacing: 0.4px;
  position: relative;
}

.uq_top__lead__lineup--catchcopy .new {
  color: #e50113;
  font-size: 20px;
  font-weight: bold;
  margin-left: 10px;
}

@media (max-width: 1200px) {
  .uq_top__lead__lineup--catchcopy {
    font-size: 2.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_top__lead__lineup--catchcopy {
    font-size: 21px;
    font-size: 5.6vw;
    margin-bottom: 2.76rem;
  }

  .uq_top__lead__lineup--catchcopy .new {
    font-size: 4vw;
  }
}

@media print,
screen and (min-width: 751px) {
  .is-mys .uq_top__lead__lineup--catchcopy {
    width: 50vw;
  }
}

.uq_top__lead__lineup--catchcopy:after {
  content: "";
  position: absolute;
  width: 100px;
  height: 1px;
  background-color: #e50113;
  bottom: -1.4rem;
  left: 0;
}

@media print,
screen and (max-width: 750px) {
  .uq_top__lead__lineup--catchcopy:after {
    width: 3.7rem;
  }
}

.uq_top__lead__lineup--title {
  font-weight: bold;
  font-size: 36px;
  font-size: 2.25rem;
}

@media (max-width: 1200px) {
  .uq_top__lead__lineup--title {
    font-size: 3vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_top__lead__lineup--title {
    font-size: 26px;
    font-size: 6.93333vw;
  }
}

.uq_top__lead__lineup--lead {
  line-height: 1.75;
  font-weight: 300;
  margin-top: 23px;
  font-size: 16px;
  font-size: 1rem;
}

@media (max-width: 1200px) {
  .uq_top__lead__lineup--lead {
    font-size: 1.33vw;
    margin-top: 1.91vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_top__lead__lineup--lead {
    font-size: 14px;
    font-size: 3.73333vw;
    margin-top: 1rem;
  }
}

.uq_top__lead__lineup--ann {
  line-height: 1.75;
  font-weight: 300;
  margin-top: 10px;
  font-size: 12px;
  font-size: 0.75rem;
}

.ann_sgp {
  padding-left: 7px;
  position: relative;
}

@media (max-width: 1200px) {
  .uq_top__lead__lineup--ann {
    font-size: 1vw;
    margin-top: 0.83vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_top__lead__lineup--ann {
    font-size: 10px;
    font-size: 2.66667vw;
    margin-top: 0.5rem;
  }
}

.uq_top__lead__lineup--btn {
  position: absolute;
  right: 0;
  bottom: 20px;
}

@media (max-width: 1200px) {
  .uq_top__lead__lineup--btn {
    bottom: 1.66vw;
  }
}

@media print,
screen and (min-width: 751px) and (max-width: 1024px) {
  .uq_top__lead__lineup--btn {
    bottom: 0;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_top__lead__lineup--btn {
    display: none;
    position: relative;
    margin-top: 1.5rem;
  }
}

.uq_top__lead__lineup--btn p {
  width: 150px;
}

@media (max-width: 1200px) {
  .uq_top__lead__lineup--btn p {
    width: 12.5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_top__lead__lineup--btn p {
    width: 10.5rem;
  }
}

@media print,
screen and (min-width: 751px) {
  .uq_tvantelin_kv--section {
    margin-top: 40px;
    padding-top: 40px;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin_kv--section {
    padding-top: 3.2rem;
  }
}

.uq_tvantelin-lineup {
  margin-top: 18px;
  margin-bottom: 80px;
}

@media (max-width: 1200px) {
  .uq_tvantelin-lineup {
    margin-top: 1.5vw;
    margin-bottom: 6.66vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-lineup {
    margin-top: 1rem;
    margin-bottom: 0.2rem;
  }
}

@media print,
screen and (min-width: 751px) {
  .uq_tvantelin-lineup__inner {
    max-width: 1280px;
    padding: 0 40px;
    margin-left: auto;
    margin-right: auto;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-lineup__inner {
    padding-left: 5vw;
    padding-right: 5vw;
  }
}

@media print,
screen and (min-width: 751px) {
  .uq_tvantelin-lineup--box {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-lineup--box {
    display: block;
  }
}

@media print,
screen and (min-width: 751px) {
  .uq_tvantelin-lineup--slider {
    width: 36.7%;
    margin-left: 7%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-flow: column;
    flex-flow: column;
    justify-items: center;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-lineup--slider {
    width: 90%;
    margin: 0 auto;
  }
}

.uq_tvantelin-lineup__slide--item:not(:first-child) {
  display: none;
}

.slick-initialized .uq_tvantelin-lineup__slide--item {
  display: block;
}

@media print,
screen and (min-width: 751px) {
  .uq_tvantelin-lineup__lead--area {
    width: 50%;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-lineup__lead--area {
    width: 100%;
  }
}

@media print,
screen and (min-width: 751px) {
  .uq_tvantelin-lineup__lead {
    opacity: 0;
    width: 51vw;
    margin-left: auto;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-lineup__lead {
    margin: 0 calc(50% - 50vw);
    width: 100vw;
    margin-top: 9.33vw;
    opacity: 1;
    -webkit-transition: 0s;
    transition: 0s;
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}

.uq_tvantelin-lineup__lead--box {
  width: 100%;
  max-width: 1220px;
  background: #fff;
  border-top-left-radius: 40px;
  border-bottom-left-radius: 40px;
  position: relative;
}

@media (max-width: 1200px) {
  .uq_tvantelin-lineup__lead--box {
    border-top-left-radius: 3.33vw;
    border-bottom-left-radius: 3.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-lineup__lead--box {
    height: 100%;
    width: 100%;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}

.uq_tvantelin-lineup__lead--box::after {
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  border-top-left-radius: 40px;
  border-bottom-left-radius: 40px;
  background: #007f41;
  bottom: -10px;
  right: -10px;
  z-index: -1;
}

@media (max-width: 1200px) {
  .uq_tvantelin-lineup__lead--box::after {
    border-top-left-radius: 3.33vw;
    border-bottom-left-radius: 3.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-lineup__lead--box::after {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    right: 0;
  }
}

.uq_tvantelin-lineup__lead--box__inner {
  max-width: 880px;
  margin-right: auto;
  padding: 23px 0 47px;
  padding-left: 89px;
  padding-right: 80px;
}

@media (max-width: 1200px) {
  .uq_tvantelin-lineup__lead--box__inner {
    padding-top: 1.91vw;
    padding-bottom: 3.91vw;
    padding-left: 7.41vw;
    padding-right: 6.66vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-lineup__lead--box__inner {
    padding-top: 2.5rem;
    padding-bottom: 2.8rem;
  }
}

.uq_tvantelin-lineup__lead--box--title h1 {
  font-size: 48px;
  font-size: 3rem;
  line-height: 1.75;
  font-weight: bold;
  color: #007f41;
  margin-bottom: 21px;
}

@media (max-width: 1200px) {
  .uq_tvantelin-lineup__lead--box--title h1 {
    font-size: 4vw;
    margin-bottom: 1.75vw;
  }
}

.uq_tvantelin-lineup__lead--box--txt p {
  font-size: 21px;
  font-size: 1.3125rem;
  line-height: 1.4;
  font-weight: 300;
}

@media (max-width: 1200px) {
  .uq_tvantelin-lineup__lead--box--txt p {
    font-size: 1.75vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-lineup__lead--box--txt p {
    font-size: 14px;
    font-size: 3.73333vw;
  }
}

.uq_tvantelin-common__inner {
  max-width: 1080px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 40px;
  padding-right: 40px;
}

.uq_tvantelin-instruction--section02 .uq_tvantelin-common__inner {
  background-color: #fff;
  padding: 40px 30px;
}

.uq_tvantelin-instruction--section02 .uq_tvantelin-common__inner .archive-corner {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}

.uq_tvantelin-instruction--section02 .uq_tvantelin-common__inner .archive-corner a {
  text-decoration: underline;
  color: blue;
  display: inline-block;
  margin: 0 6% 30px;
}

.uq_tvantelin-instruction--section02 .uq_tvantelin-common__inner .archive-corner a.not-underline {
  text-decoration: none;
  color: #333;
}

.uq_tvantelin-instruction__linup--title02 .m_mask {
  margin-bottom: 2rem;
  display: flex;
}

.uq_tvantelin-instruction__linup--title02 .m_title {
  font-size: 1.0rem;
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-common__inner {
    padding-left: 5vw;
    padding-right: 5vw;
  }
}

.uq_tvantelin-features--section {
  position: relative;
  z-index: 10;
  padding-top: 18px;
  margin-top: -18px;
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-features--section {
    padding-top: 0.5rem;
    margin-top: -0.5rem;
  }
}

.is-navOpen .uq_tvantelin-features--section {
  z-index: 1;
}

.uq_tvantelin-features__linup--section {
  padding-top: 38px;
  padding-bottom: 38px;
}

@media (max-width: 1200px) {
  .uq_tvantelin-features__linup--section {
    padding-top: 3.16vw;
    padding-bottom: 3.16vw;
  }
}

.uq_tvantelin-features__linup--title {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.uq_tvantelin-features__linup--lead {
  padding-top: 61px;
  padding-bottom: 77px;
}

@media (max-width: 1200px) {
  .uq_tvantelin-features__linup--lead {
    padding-top: 5.08vw;
    padding-bottom: 6.41vw;
  }
}

.uq_tvantelin-users_voice--section {
  position: relative;
  z-index: 10;
  padding-top: 18px;
  margin-top: -18px;
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-users_voice--section {
    padding-top: 0.5rem;
    margin-top: -0.5rem;
  }
}

.is-navOpen .uq_tvantelin-users_voice--section {
  z-index: 1;
}

.uq_tvantelin-users_voice__linup--section {
  padding-top: 62px;
  padding-bottom: 62px;
}

@media (max-width: 1200px) {
  .uq_tvantelin-users_voice__linup--section {
    padding-top: 5.16vw;
    padding-bottom: 5.16vw;
  }
}

.uq_tvantelin-users_voice__linup--title {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.uq_tvantelin-users_voice__linup--voice {
  padding-top: 60px;
  padding-bottom: 60px;
}

@media (max-width: 1200px) {
  .uq_tvantelin-users_voice__linup--voice {
    padding-top: 5vw;
    padding-bottom: 5vw;
  }
}

.uq_tvantelin-products--section {
  position: relative;
  z-index: 10;
  padding-top: 18px;
  margin-top: -18px;
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-products--section {
    padding-top: 0.5rem;
    margin-top: -0.5rem;
  }
}

.is-navOpen .uq_tvantelin-products--section {
  z-index: 1;
}

.uq_tvantelin-products__linup--section {
  padding-top: 62px;
  padding-bottom: 62px;
}

@media (max-width: 1200px) {
  .uq_tvantelin-products__linup--section {
    padding-top: 5.16vw;
    padding-bottom: 5.16vw;
  }
}

.uq_tvantelin-products__linup--title {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.uq_tvantelin-products__linup--unker {
  padding-top: 62px;
  padding-bottom: 10px;
}

@media (max-width: 1200px) {
  .uq_tvantelin-products__linup--unker {
    padding-top: 5.16vw;
    padding-bottom: 0.66vw;
  }
}

.uq_tvantelin-products__linup--unker ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  flex-wrap: wrap;
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-products__linup--unker ul {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-flow: column;
    flex-flow: column;
  }
}

.uq_tvantelin-products__linup--unker ul li {
  width: 23.5%;
}

@media print,
screen and (min-width: 751px) {
  .uq_tvantelin-products__linup--unker ul li {
    margin-left: 1.5%;
    margin-bottom: 1.5%;
  }

  .uq_tvantelin-products__linup--unker ul li:first-child {
    margin-left: 0;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-products__linup--unker ul li {
    width: 100%;
    margin-bottom: 0.8rem;
  }

  .uq_tvantelin-products__linup--unker ul li:last-child {
    margin-bottom: 0;
  }
}

.uq_tvantelin-products__linup--unker ul li a {
  padding: 17px;
  color: #007f41;
  background-color: #fff;
  border: 1px solid #007f41;
  border-radius: 30px;
  font-size: 18px;
  font-size: 1.125rem;
  font-weight: 500;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 60px;
  position: relative;
  -webkit-transition: 0.35s;
  transition: 0.35s;
  flex-wrap: wrap;
}

.uq_tvantelin-products__linup--unker ul .compression_wear a {
  padding: 10px 17px;
}


/* .uq_tvantelin-products__linup--unker ul .thumb_armor a {
  padding: 13px 17px;
} */

.uq_tvantelin-products__linup--unker ul li a>.new {
  font-size: 14px;
  font-weight: bold;
  color: #e50113;
  padding-top: 2px;
  padding-right: 5px;
}

@media (max-width: 1200px) {
  .uq_tvantelin-products__linup--unker ul li a {
    font-size: 1.5vw;
    padding: 1.41vw;
    height: 5vw;
  }

  .uq_tvantelin-products__linup--unker ul li:nth-child(2) a {
    padding: 1.083vw 1.41vw;
  }

  .uq_tvantelin-products__linup--unker ul li a>.new {
    font-size: 1.166vw;
    padding-top: 0.167vw;
    padding-right: 0.417vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-products__linup--unker ul li a {
    height: 3.4rem;
    font-size: 14px;
    font-size: 3.73333vw;
    border-radius: 2.4rem;
  }

  .uq_tvantelin-products__linup--unker ul li:nth-child(2) a {
    padding: 3.18vw 1.41vw;
  }

  .uq_tvantelin-products__linup--unker ul li a>.new {
    font-size: 2.5vw;
    padding-top: 0.533vw;
    padding-right: 1.067vw;
  }
}

.uq_tvantelin-products__linup--unker ul li a:after {
  content: "";
  display: inline-block;
  background: url(../images/common/icon_arrow_down_green.svg) no-repeat right center;
  background-size: 10px;
  position: absolute;
  right: 12px;
  top: 25px;
  width: 10px;
  height: 10px;
}

@media (max-width: 1200px) {
  .uq_tvantelin-products__linup--unker ul li a:after {
    background-size: 0.83vw;
    right: 1vw;
    top: 2.08vw;
    width: 0.83vw;
    height: 0.83vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-products__linup--unker ul li a:after {
    top: 1.2rem;
    right: 1rem;
    width: 1rem;
    height: 1rem;
    background-size: 1rem;
  }
}

@media print,
screen and (min-width: 1025px) {
  .uq_tvantelin-products__linup--unker ul li a:hover {
    color: #fff;
    background-color: #007f41;
  }

  .uq_tvantelin-products__linup--unker ul li a:hover:after {
    background: url(../images/common/icon_arrow_down_white.svg) no-repeat right center;
    background-size: 0.7rem 0.7em;
  }
}

.uq_tvantelin-faq--section {
  position: relative;
  z-index: 10;
  padding-top: 18px;
  margin-top: -18px;
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-faq--section {
    padding-top: 0.5rem;
    margin-top: -0.5rem;
  }
}

.is-navOpen .uq_tvantelin-faq--section {
  z-index: 1;
}

.uq_tvantelin-faq__linup--section {
  padding-top: 62px;
  padding-bottom: 62px;
}

@media (max-width: 1200px) {
  .uq_tvantelin-faq__linup--section {
    padding-top: 5.16vw;
    padding-bottom: 5.16vw;
  }
}

.uq_tvantelin-faq__linup--title {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.uq_tvantelin-faq__linup--faq_list {
  padding-top: 60px;
  padding-bottom: 60px;
}

@media (max-width: 1200px) {
  .uq_tvantelin-faq__linup--faq_list {
    padding-top: 5vw;
    padding-bottom: 5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-faq__linup--faq_list--accodion.is-first {
    padding: 1rem 0 0;
  }
}

.uq_tvantelin-faq__linup--faq_list--accodion>ul>li {
  border-bottom: 1px solid #707070;
  padding-left: 25px;
}

@media (max-width: 1200px) {
  .uq_tvantelin-faq__linup--faq_list--accodion>ul>li {
    padding-left: 2.08vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-faq__linup--faq_list--accodion>ul>li {
    padding-left: 5vw;
    padding-top: 0.4rem;
    padding-bottom: 0.4rem;
  }
}

.uq_tvantelin-faq__linup--faq_list--accodion>ul>li:first-child {
  border-top: 1px solid #707070;
}

.uq_tvantelin-faq__linup--faq_list--accodion>ul>li.is-border_non {
  border-top: none;
}

.uq_tvantelin-faq__linup--faq_list--accodion .js-accoBtn+div {
  display: none;
}

.uq_tvantelin-faq__linup--faq_list--accodion.is-moreBtn {
  display: none;
}

.uq_tvantelin-faq__linup--faq_list__ttl {
  font-size: 18px;
  font-size: 1.125rem;
  line-height: 1.5;
  font-weight: 400;
}

@media (max-width: 1200px) {
  .uq_tvantelin-faq__linup--faq_list__ttl {
    font-size: 1.5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-faq__linup--faq_list__ttl {
    font-size: 15px;
    font-size: 4vw;
  }
}

.uq_tvantelin-faq__linup--faq_list__ttl p {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding-right: 3rem;
}

.uq_tvantelin-faq__linup--faq_list__ttl span {
  font-size: 40px;
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 10px;
  margin-right: 35px;
}

@media (max-width: 1200px) {
  .uq_tvantelin-faq__linup--faq_list__ttl span {
    font-size: 3.33vw;
    margin-bottom: 0.83vw;
    margin-right: 2.91vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-faq__linup--faq_list__ttl span {
    font-size: 28px;
    font-size: 7.46667vw;
  }
}

.uq_tvantelin-faq__linup--faq_list__ttl {
  cursor: pointer;
  position: relative;
}

.uq_tvantelin-faq__linup--faq_list__ttl::before,
.uq_tvantelin-faq__linup--faq_list__ttl::after {
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  content: "";
  display: inline-block;
  vertical-align: middle;
  background-color: #707070;
  bottom: 0;
  height: 1px;
  margin: auto;
  position: absolute;
  width: 1rem;
  -webkit-transition: -webkit-transform 200ms;
  transition: -webkit-transform 200ms;
  transition: transform 200ms;
  transition: transform 200ms, -webkit-transform 200ms;
  right: 13px;
  top: 0;
}

@media (max-width: 1200px) {

  .uq_tvantelin-faq__linup--faq_list__ttl::before,
  .uq_tvantelin-faq__linup--faq_list__ttl::after {
    right: 1.08vw;
  }
}

@media print,
screen and (max-width: 750px) {

  .uq_tvantelin-faq__linup--faq_list__ttl::before,
  .uq_tvantelin-faq__linup--faq_list__ttl::after {
    right: 5vw;
  }
}

.uq_tvantelin-faq__linup--faq_list__ttl::after {
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
}

.uq_tvantelin-faq__linup--faq_list__ttl.is-accoOpen::before,
.uq_tvantelin-faq__linup--faq_list__ttl.is-accoOpen::after {
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}

.uq_tvantelin-faq__linup--faq_list__ttl.is-accoOpen::after {
  -webkit-transform: rotate(-180deg);
  transform: rotate(-180deg);
}

.uq_tvantelin-faq__linup--faq_list__ans {
  font-size: 16px;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
}

@media (max-width: 1200px) {
  .uq_tvantelin-faq__linup--faq_list__ans {
    font-size: 1.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-faq__linup--faq_list__ans {
    font-size: 14px;
    font-size: 3.73333vw;
  }
}

.uq_tvantelin-faq__linup--faq_list__ans p {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.uq_tvantelin-faq__linup--faq_list__ans span {
  color: #007f41;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  font-size: 40px;
  font-size: 2.5rem;
  font-weight: bold;
  margin-right: 35px;
  margin-bottom: 10px;
  padding-left: 7px;
}

@media (max-width: 1200px) {
  .uq_tvantelin-faq__linup--faq_list__ans span {
    font-size: 3.33vw;
    margin-right: 2.91vw;
    margin-bottom: 0.83vw;
    padding-left: 0.58vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-faq__linup--faq_list__ans span {
    font-size: 28px;
    font-size: 7.46667vw;
    padding-left: 0;
  }
}

.uq_tvantelin-faq__linup--faq_list__ans {
  cursor: pointer;
  position: relative;
  padding-bottom: 17px;
  padding-top: 6px;
}

@media (max-width: 1200px) {
  .uq_tvantelin-faq__linup--faq_list__ans {
    padding-bottom: 1.41vw;
    padding-top: 0.5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-faq__linup--faq_list__ans {
    padding-bottom: 1rem;
    padding-top: 1rem;
  }
}

.uq_tvantelin-faq__linup--faq_list--btn {
  margin-top: 36px;
  -webkit-transition: 0.35s;
  transition: 0.35s;
}

@media (max-width: 1200px) {
  .uq_tvantelin-faq__linup--faq_list--btn {
    margin-top: 3vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-faq__linup--faq_list--btn {
    margin-top: 1.5rem;
  }
}

.uq_tvantelin-faq__linup--faq_list--btn.is-accoOpen p>a::before,
.uq_tvantelin-faq__linup--faq_list--btn.is-accoOpen p>a::after,
.uq_tvantelin-faq__linup--faq_list--btn.is-accoOpen p>span::before,
.uq_tvantelin-faq__linup--faq_list--btn.is-accoOpen p>span::after {
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}

.uq_tvantelin-faq__linup--faq_list--btn.is-accoOpen p>a::after,
.uq_tvantelin-faq__linup--faq_list--btn.is-accoOpen p>span::after {
  -webkit-transform: rotate(-180deg);
  transform: rotate(-180deg);
}

.uq_tvantelin-faq__linup--faq_list--btn p {
  display: table;
  border-collapse: separate;
  margin: 0 auto;
  position: relative;
  width: 165px;
  height: 48px;
}

.is-idn .uq_tvantelin-faq__linup--faq_list--btn p {
  width: 200px;
}

@media (max-width: 1200px) {
  .uq_tvantelin-faq__linup--faq_list--btn p {
    width: 17.83vw;
    height: 4vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-faq__linup--faq_list--btn p {
    width: 14.46rem;
    height: 3.2rem;
  }
}

.uq_tvantelin-faq__linup--faq_list--btn p>a,
.uq_tvantelin-faq__linup--faq_list--btn p>span {
  border-radius: 24px;
  border: 1px solid #007f41;
  background-color: #fff;
  color: #007f41;
  display: table-cell;
  cursor: pointer;
  font: inherit;
  font-size: 16px;
  font-size: 1rem;
  font-family: "noto-sans-cjk-jp", "Noto Sans JP", "游ゴシック体", "Yu Gothic", YuGothic, sans-serif;
  letter-spacing: 0;
  text-align: center;
  vertical-align: middle;
  line-height: 1.45;
  text-decoration: none !important;
  padding: 0;
  white-space: nowrap;
  width: 100%;
  -webkit-transition: 0.35s;
  transition: 0.35s;
  position: relative;
}

@media (max-width: 1200px) {

  .uq_tvantelin-faq__linup--faq_list--btn p>a,
  .uq_tvantelin-faq__linup--faq_list--btn p>span {
    font-size: 1.33vw;
  }
}

.uq_tvantelin-faq__linup--faq_list--btn p>a::before,
.uq_tvantelin-faq__linup--faq_list--btn p>a::after,
.uq_tvantelin-faq__linup--faq_list--btn p>span::before,
.uq_tvantelin-faq__linup--faq_list--btn p>span::after {
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  content: "";
  display: inline-block;
  vertical-align: middle;
  background-color: #007f41;
  bottom: 0;
  height: 1px;
  margin: auto;
  position: absolute;
  width: 1rem;
  -webkit-transition: -webkit-transform 200ms;
  transition: -webkit-transform 200ms;
  transition: transform 200ms;
  transition: transform 200ms, -webkit-transform 200ms;
  right: 13px;
  top: 0;
}

.is-idn .uq_tvantelin-faq__linup--faq_list--btn p>a::before,
.is-idn .uq_tvantelin-faq__linup--faq_list--btn p>a::after,
.is-idn .uq_tvantelin-faq__linup--faq_list--btn p>span::before,
.is-idn .uq_tvantelin-faq__linup--faq_list--btn p>span::after {
  right: 10px;
}

@media (max-width: 1200px) {

  .uq_tvantelin-faq__linup--faq_list--btn p>a::before,
  .uq_tvantelin-faq__linup--faq_list--btn p>a::after,
  .uq_tvantelin-faq__linup--faq_list--btn p>span::before,
  .uq_tvantelin-faq__linup--faq_list--btn p>span::after {
    right: 1.08vw;
    width: 1.33vw;
  }
}

@media print,
screen and (max-width: 750px) {

  .uq_tvantelin-faq__linup--faq_list--btn p>a::before,
  .uq_tvantelin-faq__linup--faq_list--btn p>a::after,
  .uq_tvantelin-faq__linup--faq_list--btn p>span::before,
  .uq_tvantelin-faq__linup--faq_list--btn p>span::after {
    right: 3vw;
    width: 1rem;
  }
}

.uq_tvantelin-faq__linup--faq_list--btn p>a::after,
.uq_tvantelin-faq__linup--faq_list--btn p>span::after {
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
}

@media print,
screen and (max-width: 750px) {

  .uq_tvantelin-faq__linup--faq_list--btn p>a,
  .uq_tvantelin-faq__linup--faq_list--btn p>span {
    font-size: 16px;
    font-size: 4.26667vw;
    border-radius: 1.65rem;
  }
}

@media print,
screen and (min-width: 1025px) {

  .uq_tvantelin-faq__linup--faq_list--btn p>a:hover,
  .uq_tvantelin-faq__linup--faq_list--btn p>span:hover {
    color: #fff;
    background-color: #007f41;
  }

  .uq_tvantelin-faq__linup--faq_list--btn p>a:hover::before,
  .uq_tvantelin-faq__linup--faq_list--btn p>a:hover::after,
  .uq_tvantelin-faq__linup--faq_list--btn p>span:hover::before,
  .uq_tvantelin-faq__linup--faq_list--btn p>span:hover::after {
    background-color: #fff;
  }
}

.uq_tvantelin-instruction--section {
  position: relative;
  z-index: 10;
  padding-top: 18px;
  margin-top: -18px;
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-instruction--section {
    padding-top: 0.5rem;
    margin-top: -0.5rem;
  }
}

.is-navOpen .uq_tvantelin-instruction--section {
  z-index: 1;
}

.uq_tvantelin-instruction__linup--section {
  padding-top: 62px;
  padding-bottom: 62px;
}

@media (max-width: 1200px) {
  .uq_tvantelin-instruction__linup--section {
    padding-top: 5.16vw;
    padding-bottom: 5.16vw;
  }
}

.uq_tvantelin-instruction__linup--title {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.uq_tvantelin-instruction__linup--btn {
  margin-top: 65px;
  -webkit-transition: 0.35s;
  transition: 0.35s;
}

@media (max-width: 1200px) {
  .uq_tvantelin-instruction__linup--btn {
    margin-top: 5.41vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-instruction__linup--btn {
    margin-top: 1.5rem;
  }
}

.uq_tvantelin-instruction__linup--btn.is-close {
  display: none;
}

.uq_tvantelin-instruction__linup--btn p {
  display: table;
  border-collapse: separate;
  margin: 0 auto;
  position: relative;
  width: 293px;
  height: 48px;
}

@media (max-width: 1200px) {
  .uq_tvantelin-instruction__linup--btn p {
    width: 24.41vw;
    height: 4vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_tvantelin-instruction__linup--btn p {
    width: 20rem;
    height: 3.2rem;
  }
}

.uq_tvantelin-instruction__linup--btn p>a,
.uq_tvantelin-instruction__linup--btn p>span {
  border-radius: 24px;
  border: 1px solid #007f41;
  background-color: #007f41;
  color: #fff;
  display: table-cell;
  cursor: pointer;
  font: inherit;
  font-size: 16px;
  font-size: 1rem;
  font-family: "noto-sans-cjk-jp", "Noto Sans JP", "游ゴシック体", "Yu Gothic", YuGothic, sans-serif;
  letter-spacing: 0;
  text-align: center;
  vertical-align: middle;
  line-height: 1.45;
  text-decoration: none !important;
  padding: 0;
  white-space: nowrap;
  width: 100%;
  background-image: url(../images/common/icon_pdf_white.svg);
  background-size: auto 20px;
  background-repeat: no-repeat;
  background-position: 93% center;
  -webkit-transition: 0.35s;
  transition: 0.35s;
}

.uq_mask-btn {
  border: 1px solid #1d2088 !important;
  background-color: #1d2088 !important;
}

.uq_mask-btn:hover {
  background-color: #fff !important;
  color: #1d2088 !important;
  background-image: url(../images/common/icon_pdf_blue.svg) !important;
}

@media (max-width: 1200px) {

  .uq_tvantelin-instruction__linup--btn p>a,
  .uq_tvantelin-instruction__linup--btn p>span {
    background-image: url(../images/common/icon_pdf_white.svg);
    background-size: auto 1.66vw;
    font-size: 1.33vw;
  }
}

@media print,
screen and (max-width: 750px) {

  .uq_tvantelin-instruction__linup--btn p>a,
  .uq_tvantelin-instruction__linup--btn p>span {
    font-size: 16px;
    font-size: 4.26667vw;
    border-radius: 1.65rem;
    background-size: auto 1.3rem;
  }
}

@media print,
screen and (min-width: 1025px) {

  .uq_tvantelin-instruction__linup--btn p>a:hover,
  .uq_tvantelin-instruction__linup--btn p>span:hover {
    color: #007f41;
    background-color: #fff;
    background-image: url(../images/common/icon_pdf_green.svg);
  }
}

@media print,
screen and (min-width: 751px) {
  .uq_mask_kv--section {
    margin-top: 40px;
    padding-top: 40px;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask_kv--section {
    padding-top: 3.2rem;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask_kv--img {
    background: #f0f3f8;
    padding-top: 2rem;
  }
}

.uq_mask-lineup {
  margin-top: 18px;
  margin-bottom: 80px;
}

@media (max-width: 1200px) {
  .uq_mask-lineup {
    margin-top: 1.5vw;
    margin-bottom: 6.66vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-lineup {
    margin-top: 1rem;
    margin-bottom: 0.2rem;
  }
}

@media print,
screen and (min-width: 751px) {
  .uq_mask-lineup__inner {
    max-width: 1280px;
    padding: 0 40px;
    margin-left: auto;
    margin-right: auto;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-lineup__inner {
    padding-left: 5vw;
    padding-right: 5vw;
  }
}

@media print,
screen and (min-width: 751px) {
  .uq_mask-lineup--box {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-lineup--box {
    display: block;
  }
}

@media print,
screen and (min-width: 751px) {
  .uq_mask-lineup--slider {
    width: 36.7%;
    margin-left: 7%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-flow: column;
    flex-flow: column;
    justify-items: center;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-lineup--slider {
    width: 90%;
    margin: 0 auto;
  }
}

.uq_mask-lineup__slide--item:not(:first-child) {
  display: none;
}

.slick-initialized .uq_mask-lineup__slide--item {
  display: block;
}

@media print,
screen and (min-width: 751px) {
  .uq_mask-lineup__lead--area {
    width: 50%;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-lineup__lead--area {
    width: 100%;
  }
}

@media print,
screen and (min-width: 751px) {
  .uq_mask-lineup__lead {
    opacity: 0;
    width: 51vw;
    margin-left: auto;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-lineup__lead {
    margin: 0 calc(50% - 50vw);
    width: 100vw;
    margin-top: 9.33vw;
    opacity: 1;
    -webkit-transition: 0s;
    transition: 0s;
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
}

.uq_mask-lineup__lead--box {
  width: 100%;
  max-width: 1220px;
  background: #fff;
  border-top-left-radius: 40px;
  border-bottom-left-radius: 40px;
  position: relative;
}

@media (max-width: 1200px) {
  .uq_mask-lineup__lead--box {
    border-top-left-radius: 3.33vw;
    border-bottom-left-radius: 3.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-lineup__lead--box {
    height: 100%;
    width: 100%;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}

.uq_mask-lineup__lead--box::after {
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  border-top-left-radius: 40px;
  border-bottom-left-radius: 40px;
  background: #1d2088;
  bottom: -10px;
  right: -10px;
  z-index: -1;
}

@media (max-width: 1200px) {
  .uq_mask-lineup__lead--box::after {
    border-top-left-radius: 3.33vw;
    border-bottom-left-radius: 3.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-lineup__lead--box::after {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    right: 0;
  }
}

.uq_mask-lineup__lead--box__inner {
  max-width: 880px;
  margin-right: auto;
  padding: 23px 0 47px;
  padding-left: 89px;
  padding-right: 40px;
}

@media (max-width: 1200px) {
  .uq_mask-lineup__lead--box__inner {
    padding-top: 1.91vw;
    padding-bottom: 3.91vw;
    padding-left: 7.41vw;
    padding-right: 3.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-lineup__lead--box__inner {
    padding-top: 2.5rem;
    padding-bottom: 2.8rem;
  }
}

.uq_mask-lineup__lead--box--title h1 {
  font-size: 48px;
  font-size: 3rem;
  line-height: 1.75;
  font-weight: bold;
  color: #1d2088;
  margin-bottom: 21px;
}

@media (max-width: 1300px) {
  .uq_mask-lineup__lead--box--title h1 {
    font-size: 3.46vw;
  }
}

@media (max-width: 1200px) {
  .uq_mask-lineup__lead--box--title h1 {
    margin-bottom: 1.75vw;
  }
}

.uq_mask-lineup__lead--box--txt p {
  font-size: 21px;
  font-size: 1.3125rem;
  line-height: 1.4;
  font-weight: 300;
}

.uq_mask-lineup__lead--box--txt.chile p {
  font-weight: 400;
  font-family: "Yu Gothic", YuGothic, sans-serif;
}

@media (max-width: 1200px) {
  .uq_mask-lineup__lead--box--txt p {
    font-size: 1.75vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-lineup__lead--box--txt p {
    font-size: 14px;
    font-size: 3.73333vw;
  }
}

.uq_mask-common__inner {
  max-width: 1080px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 40px;
  padding-right: 40px;
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-common__inner {
    padding-left: 5vw;
    padding-right: 5vw;
  }
}

.uq_mask-features--section {
  position: relative;
  z-index: 10;
  padding-top: 18px;
  margin-top: -18px;
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-features--section {
    padding-top: 0.5rem;
    margin-top: -0.5rem;
  }
}

.is-navOpen .uq_mask-features--section {
  z-index: 1;
}

.uq_mask-features__linup--section {
  padding-top: 38px;
  padding-bottom: 38px;
}

@media (max-width: 1200px) {
  .uq_mask-features__linup--section {
    padding-top: 3.16vw;
    padding-bottom: 3.16vw;
  }
}

.uq_mask-features__linup--title {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.uq_mask-features__linup--lead {
  padding-top: 61px;
  padding-bottom: 77px;
}

.uq_qp-features__linup--lead {
  text-align: center;
  max-width: 850px;
  position: relative;
}

@keyframes fadeInBottom {
  0% {
    opacity: 0;
    transform: translateX(-100px);
  }

  100% {
    opacity: 1;
    transform: translateX(0px);
  }
}


/* @keyframes moved {
    0% {
        transform: translateY(-5px);
    }

    100% {
        transform: translateY(0px);
    }
} */

.uq_qp-features__linup--lead .pic {
  opacity: 1;
  z-index: 9;
}

.uq_qp-features__linup--lead>.pic:nth-child(2) {
  bottom: 0;
  left: 0;
  width: 84.47058823529412%;
}

.uq_qp-features__linup--lead>.pic:nth-child(3) {
  top: 1.41176470588235%;
  right: 04.91764705882353%;
  width: 38.82352941176471%;
}

.uq_qp-features__linup--lead>.pic:nth-child(4) {
  bottom: 4.83333333333333%;
  right: 2.88235294117647%;
  width: 40.82352941176471%;
}

.uq_qp-features__linup--lead.m_mask.is-show>.pic:nth-child(2) {
  animation: fadeInBottom 0.3s linear 0.5s backwards;
}

.uq_qp-features__linup--lead.m_mask.is-show>.pic:nth-child(3) {
  animation: fadeInBottom 0.3s linear 1s backwards;
}

.uq_qp-features__linup--lead.m_mask.is-show>.pic:nth-child(4) {
  animation: fadeInBottom 0.3s linear 1.5s backwards;
}


/* .uq_qp-features__linup--lead.js-slideIn.is-show>.pic:nth-child(2) >picture {
    animation: 6s ease-in-out infinite alternate moved;
}

.uq_qp-features__linup--lead.js-slideIn.is-show>.pic:nth-child(3) >picture{
    animation: 5s ease-in-out infinite alternate moved;
}

.uq_qp-features__linup--lead.js-slideIn.is-show>.pic:nth-child(4) >picture{
    animation: 5s ease-in-out infinite alternate moved;
} */

@media (max-width: 1200px) {
  .uq_mask-features__linup--lead {
    padding-top: 5.08vw;
    padding-bottom: 6.41vw;
  }
}

.uq_mask-features__linup--ann {
  margin-top: 33px;
}

@media (max-width: 1200px) {
  .uq_mask-features__linup--ann {
    margin-top: 2.75vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_qp-features__linup--lead {
    margin: 30px auto 30px;
  }

  .uq_mask-features__linup--ann {
    margin-top: 1rem;
  }
}

.uq_mask-features__linup--ann p {
  font-size: 12px;
  font-size: 0.75rem;
  font-weight: 300;
  line-height: 1.5;
}

@media (max-width: 1200px) {
  .uq_mask-features__linup--ann p {
    font-size: 1vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-features__linup--ann p {
    font-size: 12px;
    font-size: 3.2vw;
  }
}

.uq_mask-users_voice--section {
  position: relative;
  z-index: 10;
  padding-top: 18px;
  margin-top: -18px;
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-users_voice--section {
    padding-top: 0.5rem;
    margin-top: -0.5rem;
  }
}

.is-navOpen .uq_mask-users_voice--section {
  z-index: 1;
}

.uq_mask-users_voice__linup--section {
  padding-top: 62px;
  padding-bottom: 62px;
}

@media (max-width: 1200px) {
  .uq_mask-users_voice__linup--section {
    padding-top: 5.16vw;
    padding-bottom: 5.16vw;
  }
}

.uq_mask-users_voice__linup--title {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.uq_mask-users_voice__linup--voice {
  padding-top: 60px;
  padding-bottom: 60px;
}

@media (max-width: 1200px) {
  .uq_mask-users_voice__linup--voice {
    padding-top: 5vw;
    padding-bottom: 5vw;
  }
}

.uq_mask-products--section {
  position: relative;
  z-index: 10;
  padding-top: 18px;
  margin-top: -18px;
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-products--section {
    padding-top: 0.5rem;
    margin-top: -0.5rem;
  }
}

.is-navOpen .uq_mask-products--section {
  z-index: 1;
}

.uq_mask-products__linup--section {
  padding-top: 62px;
  padding-bottom: 62px;
}

@media (max-width: 1200px) {
  .uq_mask-products__linup--section {
    padding-top: 5.16vw;
    padding-bottom: 5.16vw;
  }
}

.uq_mask-products__linup--title {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.uq_mask-products__linup--unker {
  padding-top: 62px;
  padding-bottom: 30px;
}

@media (max-width: 1200px) {
  .uq_mask-products__linup--unker {
    padding-top: 5.16vw;
    padding-bottom: 2.5vw;
  }
}

.uq_mask-products__linup--unker ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-products__linup--unker ul {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-flow: column;
    flex-flow: column;
  }
}

.uq_mask-products__linup--unker ul li {
  width: 48%;
  max-width: 360px;
}

@media print,
screen and (min-width: 751px) {
  .uq_mask-products__linup--unker ul li {
    margin-left: 1.5%;
  }

  .uq_mask-products__linup--unker ul li:first-child {
    margin-left: 0;
  }
}

@media (max-width: 1200px) {
  .uq_mask-products__linup--unker ul li {
    max-width: 30vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-products__linup--unker ul li {
    width: 100%;
    max-width: 100%;
    margin-bottom: 0.8rem;
  }

  .uq_mask-products__linup--unker ul li:last-child {
    margin-bottom: 0;
  }
}

.uq_mask-products__linup--unker ul li a {
  padding: 17px;
  color: #1d2088;
  background-color: #fff;
  border: 1px solid #1d2088;
  border-radius: 30px;
  font-size: 18px;
  font-size: 1.125rem;
  font-weight: 500;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  height: 60px;
  position: relative;
  -webkit-transition: 0.35s;
  transition: 0.35s;
}

@media (max-width: 1200px) {
  .uq_mask-products__linup--unker ul li a {
    font-size: 1.5vw;
    padding: 1.41vw;
    height: 5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-products__linup--unker ul li a {
    height: 3.4rem;
    font-size: 14px;
    font-size: 3.73333vw;
    border-radius: 2.4rem;
  }
}

.uq_mask-products__linup--unker ul li a:after {
  content: "";
  display: inline-block;
  background: url(../images/common/icon_arrow_down_blue.svg) no-repeat right center;
  background-size: 10px;
  position: absolute;
  right: 12px;
  top: 25px;
  width: 10px;
  height: 10px;
}

@media (max-width: 1200px) {
  .uq_mask-products__linup--unker ul li a:after {
    background-size: 0.83vw;
    right: 1vw;
    top: 2.08vw;
    width: 0.83vw;
    height: 0.83vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-products__linup--unker ul li a:after {
    top: 1.2rem;
    right: 1rem;
    width: 1rem;
    height: 1rem;
    background-size: 1rem;
  }
}

@media print,
screen and (min-width: 1025px) {
  .uq_mask-products__linup--unker ul li a:hover {
    color: #fff;
    background-color: #1d2088;
  }

  .uq_mask-products__linup--unker ul li a:hover:after {
    background: url(../images/common/icon_arrow_down_white.svg) no-repeat right center;
    background-size: 0.7rem 0.7em;
  }
}

.uq_mask-faq--section {
  position: relative;
  z-index: 10;
  padding-top: 18px;
  margin-top: -18px;
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-faq--section {
    padding-top: 0.5rem;
    margin-top: -0.5rem;
  }
}

.is-navOpen .uq_mask-faq--section {
  z-index: 1;
}

.uq_mask-faq__linup--section {
  padding-top: 62px;
  padding-bottom: 62px;
}

@media (max-width: 1200px) {
  .uq_mask-faq__linup--section {
    padding-top: 5.16vw;
    padding-bottom: 5.16vw;
  }
}

.uq_mask-faq__linup--title {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

.uq_mask-faq__linup--faq_list {
  padding-top: 60px;
  padding-bottom: 60px;
}

@media (max-width: 1200px) {
  .uq_mask-faq__linup--faq_list {
    padding-top: 5vw;
    padding-bottom: 5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-faq__linup--faq_list--accodion.is-first {
    padding: 1rem 0 0;
  }
}

.uq_mask-faq__linup--faq_list--accodion>ul>li {
  border-bottom: 1px solid #707070;
  padding-left: 25px;
}

@media (max-width: 1200px) {
  .uq_mask-faq__linup--faq_list--accodion>ul>li {
    padding-left: 2.08vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-faq__linup--faq_list--accodion>ul>li {
    padding-left: 5vw;
    padding-top: 0.4rem;
    padding-bottom: 0.4rem;
  }
}

.uq_mask-faq__linup--faq_list--accodion>ul>li:first-child {
  border-top: 1px solid #707070;
}

.uq_mask-faq__linup--faq_list--accodion>ul>li.is-border_non {
  border-top: none;
}

.uq_mask-faq__linup--faq_list--accodion .js-accoBtn+div {
  display: none;
}

.uq_mask-faq__linup--faq_list--accodion.is-moreBtn {
  display: none;
}

.uq_mask-faq__linup--faq_list__ttl {
  font-size: 18px;
  font-size: 1.125rem;
  line-height: 1.5;
  font-weight: 400;
}

@media (max-width: 1200px) {
  .uq_mask-faq__linup--faq_list__ttl {
    font-size: 1.5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-faq__linup--faq_list__ttl {
    font-size: 15px;
    font-size: 4vw;
  }
}

.uq_mask-faq__linup--faq_list__ttl p {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding-right: 3rem;
}

.uq_mask-faq__linup--faq_list__ttl span {
  font-size: 40px;
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 10px;
  margin-right: 35px;
}

@media (max-width: 1200px) {
  .uq_mask-faq__linup--faq_list__ttl span {
    font-size: 3.33vw;
    margin-bottom: 0.83vw;
    margin-right: 2.91vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-faq__linup--faq_list__ttl span {
    font-size: 28px;
    font-size: 7.46667vw;
  }
}

.uq_mask-faq__linup--faq_list__ttl {
  cursor: pointer;
  position: relative;
}

.uq_mask-faq__linup--faq_list__ttl::before,
.uq_mask-faq__linup--faq_list__ttl::after {
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  content: "";
  display: inline-block;
  vertical-align: middle;
  background-color: #707070;
  bottom: 0;
  height: 1px;
  margin: auto;
  position: absolute;
  width: 1rem;
  -webkit-transition: -webkit-transform 200ms;
  transition: -webkit-transform 200ms;
  transition: transform 200ms;
  transition: transform 200ms, -webkit-transform 200ms;
  right: 13px;
  top: 0;
}

@media (max-width: 1200px) {

  .uq_mask-faq__linup--faq_list__ttl::before,
  .uq_mask-faq__linup--faq_list__ttl::after {
    right: 1.08vw;
  }
}

@media print,
screen and (max-width: 750px) {

  .uq_mask-faq__linup--faq_list__ttl::before,
  .uq_mask-faq__linup--faq_list__ttl::after {
    right: 5vw;
  }
}

.uq_mask-faq__linup--faq_list__ttl::after {
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
}

.uq_mask-faq__linup--faq_list__ttl.is-accoOpen::before,
.uq_mask-faq__linup--faq_list__ttl.is-accoOpen::after {
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}

.uq_mask-faq__linup--faq_list__ttl.is-accoOpen::after {
  -webkit-transform: rotate(-180deg);
  transform: rotate(-180deg);
}

.uq_mask-faq__linup--faq_list__ans {
  font-size: 16px;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
}

@media (max-width: 1200px) {
  .uq_mask-faq__linup--faq_list__ans {
    font-size: 1.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-faq__linup--faq_list__ans {
    font-size: 14px;
    font-size: 3.73333vw;
  }
}

.uq_mask-faq__linup--faq_list__ans p {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.uq_mask-faq__linup--faq_list__ans span {
  color: #1d2088;
  -ms-flex-item-align: stretch;
  align-self: stretch;
  font-size: 40px;
  font-size: 2.5rem;
  font-weight: bold;
  margin-right: 35px;
  margin-bottom: 10px;
  padding-left: 7px;
}

@media (max-width: 1200px) {
  .uq_mask-faq__linup--faq_list__ans span {
    font-size: 3.33vw;
    margin-right: 2.91vw;
    margin-bottom: 0.83vw;
    padding-left: 0.58vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-faq__linup--faq_list__ans span {
    font-size: 28px;
    font-size: 7.46667vw;
    padding-left: 0;
  }
}

.uq_mask-faq__linup--faq_list__ans {
  cursor: pointer;
  position: relative;
  padding-bottom: 17px;
  padding-top: 6px;
}

@media (max-width: 1200px) {
  .uq_mask-faq__linup--faq_list__ans {
    padding-left: 1.41vw;
    padding-bottom: 0.5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-faq__linup--faq_list__ans {
    padding-left: 0;
    padding-bottom: 1rem;
    padding-top: 1rem;
  }
}

.uq_mask-faq__linup--faq_list--btn {
  margin-top: 36px;
  -webkit-transition: 0.35s;
  transition: 0.35s;
}

@media (max-width: 1200px) {
  .uq_mask-faq__linup--faq_list--btn {
    margin-top: 3vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-faq__linup--faq_list--btn {
    margin-top: 1.5rem;
  }
}

.uq_mask-faq__linup--faq_list--btn.is-accoOpen p>a::before,
.uq_mask-faq__linup--faq_list--btn.is-accoOpen p>a::after,
.uq_mask-faq__linup--faq_list--btn.is-accoOpen p>span::before,
.uq_mask-faq__linup--faq_list--btn.is-accoOpen p>span::after {
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}

.uq_mask-faq__linup--faq_list--btn.is-accoOpen p>a::after,
.uq_mask-faq__linup--faq_list--btn.is-accoOpen p>span::after {
  -webkit-transform: rotate(-180deg);
  transform: rotate(-180deg);
}

.uq_mask-faq__linup--faq_list--btn p {
  display: table;
  border-collapse: separate;
  margin: 0 auto;
  position: relative;
  width: 165px;
  height: 48px;
}

@media (max-width: 1200px) {
  .uq_mask-faq__linup--faq_list--btn p {
    width: 15.83vw;
    height: 4vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-faq__linup--faq_list--btn p {
    width: 11.46rem;
    height: 3.2rem;
  }
}

.uq_mask-faq__linup--faq_list--btn p>a,
.uq_mask-faq__linup--faq_list--btn p>span {
  border-radius: 24px;
  border: 1px solid #1d2088;
  background-color: #fff;
  color: #1d2088;
  display: table-cell;
  cursor: pointer;
  font: inherit;
  font-size: 16px;
  font-size: 1rem;
  font-family: "noto-sans-cjk-jp", "Noto Sans JP", "游ゴシック体", "Yu Gothic", YuGothic, sans-serif;
  letter-spacing: 0;
  text-align: center;
  vertical-align: middle;
  line-height: 1.45;
  text-decoration: none !important;
  padding: 0;
  white-space: nowrap;
  width: 100%;
  -webkit-transition: 0.35s;
  transition: 0.35s;
  position: relative;
}

@media (max-width: 1200px) {

  .uq_mask-faq__linup--faq_list--btn p>a,
  .uq_mask-faq__linup--faq_list--btn p>span {
    font-size: 1.33vw;
  }
}

.uq_mask-faq__linup--faq_list--btn p>a::before,
.uq_mask-faq__linup--faq_list--btn p>a::after,
.uq_mask-faq__linup--faq_list--btn p>span::before,
.uq_mask-faq__linup--faq_list--btn p>span::after {
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  content: "";
  display: inline-block;
  vertical-align: middle;
  background-color: #1d2088;
  bottom: 0;
  height: 1px;
  margin: auto;
  position: absolute;
  width: 1rem;
  -webkit-transition: -webkit-transform 200ms;
  transition: -webkit-transform 200ms;
  transition: transform 200ms;
  transition: transform 200ms, -webkit-transform 200ms;
  right: 13px;
  top: 0;
}

@media (max-width: 1200px) {

  .uq_mask-faq__linup--faq_list--btn p>a::before,
  .uq_mask-faq__linup--faq_list--btn p>a::after,
  .uq_mask-faq__linup--faq_list--btn p>span::before,
  .uq_mask-faq__linup--faq_list--btn p>span::after {
    right: 1.08vw;
    width: 1.33vw;
  }
}

@media print,
screen and (max-width: 750px) {

  .uq_mask-faq__linup--faq_list--btn p>a::before,
  .uq_mask-faq__linup--faq_list--btn p>a::after,
  .uq_mask-faq__linup--faq_list--btn p>span::before,
  .uq_mask-faq__linup--faq_list--btn p>span::after {
    right: 5vw;
    width: 1rem;
  }
}

.uq_mask-faq__linup--faq_list--btn p>a::after,
.uq_mask-faq__linup--faq_list--btn p>span::after {
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
}

@media print,
screen and (max-width: 750px) {

  .uq_mask-faq__linup--faq_list--btn p>a,
  .uq_mask-faq__linup--faq_list--btn p>span {
    font-size: 16px;
    font-size: 4.26667vw;
    border-radius: 1.65rem;
  }
}

@media print,
screen and (min-width: 1025px) {

  .uq_mask-faq__linup--faq_list--btn p>a:hover,
  .uq_mask-faq__linup--faq_list--btn p>span:hover {
    color: #fff;
    background-color: #1d2088;
  }

  .uq_mask-faq__linup--faq_list--btn p>a:hover::before,
  .uq_mask-faq__linup--faq_list--btn p>a:hover::after,
  .uq_mask-faq__linup--faq_list--btn p>span:hover::before,
  .uq_mask-faq__linup--faq_list--btn p>span:hover::after {
    background-color: #fff;
  }
}

.uq_mask-faq__ann p {
  font-size: 12px;
  font-size: 0.75rem;
  line-height: 1.75;
  font-weight: 300;
  margin-top: 1rem;
}

@media (max-width: 1200px) {
  .uq_mask-faq__ann p {
    font-size: 1vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_mask-faq__ann p {
    font-size: 8px;
    font-size: 2.13333vw;
  }
}

@media print,
screen and (min-width: 751px) {
  .uq_contact--section {
    margin-top: 40px;
    padding-top: 40px;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_contact--section {
    padding-top: 3.2rem;
  }
}

.uq_contact--bg {
  background-color: #fff;
  position: relative;
}

.uq_contact--bg::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  height: 10px;
  width: 100%;
  background: transparent -webkit-gradient(linear, left top, right top, from(#f5bd41), color-stop(66%, #ef7c31), to(#e50012)) 0% 0% no-repeat padding-box;
  background: transparent linear-gradient(90deg, #f5bd41 0%, #ef7c31 66%, #e50012 100%) 0% 0% no-repeat padding-box;
}

@media print,
screen and (min-width: 751px) {
  .uq_contact__inner {
    max-width: 1080px;
    padding: 0 40px;
    margin-left: auto;
    margin-right: auto;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_contact__inner {
    padding-left: 5vw;
    padding-right: 5vw;
  }
}

.uq_contact--head {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-flow: column;
  flex-flow: column;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  width: 100%;
}

.uq_contact--title {
  margin-top: 35px;
}

@media (max-width: 1200px) {
  .uq_contact--title {
    margin-top: 2.91vw;
  }
}

.uq_contact--title h1 {
  color: #e50113;
  font-size: 48px;
  font-size: 3rem;
  font-weight: bold;
}

@media (max-width: 1200px) {
  .uq_contact--title h1 {
    font-size: 4vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_contact--title h1 {
    font-size: 30px;
    font-size: 8vw;
  }
}

.uq_contact-nav {
  margin-top: 40px;
  padding: 0 40px;
  width: 100%;
}

@media (max-width: 1200px) {
  .uq_contact-nav {
    margin-top: 3.33vw;
    padding: 0 3.33vw;
  }
}

.uq_contact-nav__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  text-align: center;
  margin-bottom: 71.9px;
}

@media (max-width: 1200px) {
  .uq_contact-nav__list {
    margin-bottom: 5.99vw;
  }
}

.uq_contact-nav__item {
  color: #676767;
  font-size: 18px;
  font-size: 1.125rem;
  width: 29%;
  padding: 1rem 0.5rem;
  position: relative;
}

@media print,
screen and (min-width: 751px) and (max-width: 1024px) {
  .uq_contact-nav__item {
    font-size: 16px;
    font-size: 1rem;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_contact-nav__item {
    font-size: 12px;
    font-size: 3.2vw;
    white-space: nowrap;
    padding: 0.75rem 0.5rem;
  }
}

.uq_contact-nav__item.is-act {
  color: #e50113;
  font-weight: bold;
}

.uq_contact-nav__item+.uq_contact-nav__item::before {
  content: "";
  display: block;
  width: 18px;
  height: 18px;
  border-top: 1px solid #333;
  border-right: 1px solid #333;
  position: absolute;
  left: -15%;
  top: 50%;
  margin: -0.5rem 0 0 -0.25rem;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}

@media print,
screen and (max-width: 750px) {
  .uq_contact-nav__item+.uq_contact-nav__item::before {
    width: 0.65rem;
    height: 0.65rem;
    margin-left: -0.45rem;
    top: 60%;
  }
}

.uq_contact-col {
  padding: 1rem 0;
}

@media print,
screen and (min-width: 751px) {
  .uq_contact-col {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  }
}

@media print,
screen and (min-width: 751px) and (max-width: 1024px) {
  .uq_contact-col {
    margin: 0 -35px;
  }
}

.uq_contact-col:first-of-type {
  padding-top: 2.5rem;
}

.uq_contact-col+.uq_contact-col {
  position: relative;
}

.uq_contact-col+.uq_contact-col::before {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  border-top: 1px solid #707070;
}

.uq_contact-col__label,
.uq_contact-col__body {
  line-height: 1.45;
}

@media print,
screen and (max-width: 750px) {

  .uq_contact-col__label,
  .uq_contact-col__body {
    font-size: 14px;
    font-size: 3.73333vw;
  }
}

.uq_contact-col__label {
  font-size: 14px;
  font-size: 0.875rem;
  font-weight: 400;
  position: relative;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.uq_contact-col__label.is-label_top {
  -webkit-box-align: baseline;
  -ms-flex-align: baseline;
  align-items: baseline;
  padding-top: 15px;
}

@media print,
screen and (min-width: 1025px) {
  .uq_contact-col__label {
    width: 17rem;
    padding-left: 42px;
  }
}

@media (max-width: 1200px) {
  .uq_contact-col__label {
    padding-left: 3.5vw;
    padding-top: 1rem;
    padding-bottom: 1.5rem;
  }
}

@media print,
screen and (min-width: 751px) and (max-width: 1024px) {
  .uq_contact-col__label {
    width: 15rem;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_contact-col__label {
    font-size: 14px;
    font-size: 3.73333vw;
  }
}

body.is-confirm .uq_contact-col__label {
  padding-top: 1.15rem;
}

@media print,
screen and (min-width: 1025px) {
  .uq_contact-col__body {
    width: calc(100% - 17rem);
    padding: 0 4rem 0 2rem;
  }
}

@media print,
screen and (min-width: 751px) and (max-width: 1024px) {
  .uq_contact-col__body {
    font-size: 15px;
    font-size: 0.9375rem;
    width: calc(100% - 15rem);
    padding: 1rem 1.5rem 1.5rem;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_contact-col__body {
    padding: 0.75rem 4vw 1.25rem;
  }
}

.uq_contact-input+.uq_contact-input {
  margin-top: 0.75rem;
}

.uq_contact-input input:not([type="radio"]):not([type="checkbox"]),
.uq_contact-input select,
.uq_contact-input textarea {
  color: inherit;
  font: inherit;
  width: 100%;
  padding: 1rem;
  overflow: hidden;
  vertical-align: middle;
}

@media print,
screen and (min-width: 751px) and (max-width: 1024px) {

  .uq_contact-input input:not([type="radio"]):not([type="checkbox"]),
  .uq_contact-input select,
  .uq_contact-input textarea {
    padding: 1rem;
  }
}

@media print,
screen and (max-width: 750px) {

  .uq_contact-input input:not([type="radio"]):not([type="checkbox"]),
  .uq_contact-input select,
  .uq_contact-input textarea {
    font-size: 14px;
    font-size: 3.73333vw;
    padding: 0.65rem;
  }
}

.uq_contact-input input:not([type="radio"]):not([type="checkbox"])[disabled] {
  background-color: #efefef;
}

.uq_contact-input textarea {
  line-height: 1.45;
  height: 14rem;
  padding-top: 1rem;
}

.uq_contact-input.is-textarea {
  width: 100%;
}

@media print,
screen and (min-width: 751px) {
  .uq_contact-input.is-radio {
    padding-top: 0.75rem;
  }
}

.uq_contact-input.is-radio>label {
  display: inline-block;
  font-size: 16px;
  font-size: 1rem;
  padding: 0.55rem 0;
  cursor: pointer;
}

@media print,
screen and (max-width: 1024px) {
  .uq_contact-input.is-radio>label {
    display: block;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_contact-input.is-radio>label {
    font-size: 14px;
    font-size: 3.73333vw;
  }
}

.uq_contact-input.is-radio>label:not(:last-child) {
  margin-right: 1.5rem;
}

.uq_contact-input.is-radio>label::before {
  content: "";
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  background: url(../images/common/input_radio.svg) no-repeat center;
  background-size: 96% auto;
  vertical-align: -0.35em;
  margin-right: 0.25rem;
}

.uq_contact-input.is-radio input[type="radio"] {
  display: none;
}

.uq_contact-input.is-radio input[type="radio"]:checked+label::before {
  background-image: url(../images/common/input_radio_checked.svg);
}

.uq_contact-input a {
  text-decoration: underline;
}

.uq_contact-input-txt {
  line-height: 1.85;
  margin: 0.75rem 0 0.25rem;
}

.uq_contact-input-txt a {
  text-decoration: underline;
}

.form-alert {
  font-size: 14px;
  font-size: 0.875rem;
  color: #f20000;
  font-weight: bold;
  line-height: 1.45;
  margin-top: 0.75em;
  position: relative;
}

.form-alert+.form-alert {
  margin-top: 0.4em;
}

@media print,
screen and (max-width: 750px) {
  .form-alert {
    font-size: 12px;
    font-size: 3.2vw;
  }
}

.form-alert+.is-textarea {
  margin-top: 1rem;
}

.uq_contact-required,
.uq_contact-optional {
  display: inline-block;
  color: #f00;
  font-size: 10px;
  font-size: 0.625rem;
  font-weight: bold;
  line-height: 1;
  border: 1px solid #f00;
  padding: 0.5em 0.75em;
}

@media print,
screen and (min-width: 751px) {

  .uq_contact-required,
  .uq_contact-optional {
    position: absolute;
    top: 1rem;
    right: 0;
  }
}

@media print,
screen and (min-width: 751px) and (max-width: 1024px) {

  .uq_contact-required,
  .uq_contact-optional {
    top: 2rem;
  }

  .is-label_top .uq_contact-required,
  .is-label_top .uq_contact-optional {
    top: 1rem;
  }
}

@media print,
screen and (max-width: 750px) {

  .uq_contact-required,
  .uq_contact-optional {
    margin-left: 0.75rem;
    vertical-align: 0.15em;
  }
}

.uq_contact-optional {
  color: #333;
  border: 1px solid #333;
}

.uq_contact-btn {
  padding-top: 61px;
  margin-top: 12px;
  margin-bottom: 90px;
  border-top: 1px solid;
}

@media (max-width: 1200px) {
  .uq_contact-btn {
    padding-top: 5.08vw;
    margin-top: 1vw;
    margin-bottom: 7.5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_contact-btn {
    border-top: none;
    margin-top: 0;
    margin-bottom: 5rem;
  }
}

@media print,
screen and (min-width: 751px) {
  .uq_contact-btn_inner {
    max-width: 626px;
    margin-left: auto;
    margin-right: auto;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_contact-btn_inner {
    padding-left: 5vw;
  }
}

.uq_contact-btn-col {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}

.uq_contact-btn-col.is-center {
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
}

@media print,
screen and (max-width: 750px) {
  .uq_contact-btn-col {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-flow: column;
    flex-flow: column;
  }
}

.uq_contact-btn-col>li {
  width: 49%;
}

.uq_contact-btn-col>li:nth-child(2) {
  margin-left: 2%;
}

@media print,
screen and (max-width: 750px) {
  .uq_contact-btn-col>li {
    width: 100%;
  }

  .uq_contact-btn-col>li:nth-child(2) {
    margin-left: 0;
    margin-top: 1rem;
  }
}

.uq_contact-btn-col>li p {
  display: table;
  border-collapse: separate;
  margin: 0 auto;
  position: relative;
  width: 100%;
  max-width: 293px;
  height: 48px;
}

@media print,
screen and (max-width: 750px) {
  .uq_contact-btn-col>li p {
    max-width: 19rem;
    height: 3rem;
  }
}

.uq_contact-btn-col>li p>a,
.uq_contact-btn-col>li p>span,
.uq_contact-btn-col>li p>input[type="submit"] {
  border-radius: 24px;
  border: 1px solid #e50113;
  font: inherit;
  font-family: "noto-sans-cjk-jp", "Noto Sans JP", "游ゴシック体", "Yu Gothic", YuGothic, sans-serif;
  font-weight: 500;
  letter-spacing: 0;
  font-size: 14px;
  font-size: 0.875rem;
  display: table-cell;
  text-align: center;
  vertical-align: middle;
  width: 100%;
  height: 48px;
  background-color: #e50113;
  line-height: 1.45;
  text-decoration: none !important;
  padding: 0.25rem 2.5rem 0.25rem 1.5rem;
  border: 1px solid #e50113;
  background-color: #fff;
  color: #e50113;
  cursor: pointer;
  white-space: nowrap;
  background-image: url(../images/common/icon_arrow_red.svg);
  background-size: auto 9px;
  background-repeat: no-repeat;
  background-position: 93% center;
}

@media (max-width: 1200px) {

  .uq_contact-btn-col>li p>a,
  .uq_contact-btn-col>li p>span,
  .uq_contact-btn-col>li p>input[type="submit"] {
    background-image: url(../images/common/icon_arrow_red.svg);
    background-size: auto 0.75vw;
  }
}

@media print,
screen and (max-width: 750px) {

  .uq_contact-btn-col>li p>a,
  .uq_contact-btn-col>li p>span,
  .uq_contact-btn-col>li p>input[type="submit"] {
    font-size: 14px;
    font-size: 3.73333vw;
    height: 3rem;
    border-radius: 2.3rem;
    background-size: auto 0.6rem;
  }
}

@media print,
screen and (min-width: 1025px) {

  .uq_contact-btn-col>li p>a,
  .uq_contact-btn-col>li p>input[type="submit"] {
    -webkit-transition: 0.35s;
    transition: 0.35s;
  }

  .uq_contact-btn-col>li p>a:hover,
  .uq_contact-btn-col>li p>input[type="submit"]:hover {
    color: #fff;
    background-color: #e50113;
    border-color: #e50113;
    background-image: url(../images/common/icon_arrow_white.svg);
    background-position: 95% center;
  }
}

.uq_contact-btn-col>li p.is-cancell>a,
.uq_contact-btn-col>li p.is-cancell>span,
.uq_contact-btn-col>li p.is-cancell>input[type="submit"] {
  color: #fff;
  background-color: #afafaf;
  border-color: #afafaf;
  background-image: none;
}

@media print,
screen and (min-width: 1025px) {

  .uq_contact-btn-col>li p.is-cancell>a:hover,
  .uq_contact-btn-col>li p.is-cancell>span:hover,
  .uq_contact-btn-col>li p.is-cancell>input[type="submit"]:hover {
    opacity: 0.7;
  }
}

.uq_contact-terms {
  font-size: 14px;
  font-size: 0.875rem;
  line-height: 1.45;
  margin-bottom: 1.5rem;
  height: 9.5rem;
  padding: 1rem;
  overflow: auto;
  position: relative;
  background-color: #fff;
}

.uq_contact-terms a {
  text-decoration: underline;
}

@media print,
screen and (max-width: 750px) {
  .uq_contact-terms {
    font-size: 12px;
    font-size: 3.2vw;
    height: 8rem;
  }
}

#privacy {
  display: none;
}

.privacy-check {
  display: inline-block;
  margin-right: 1em;
  position: relative;
  cursor: pointer;
}

.privacy-check::before,
.privacy-check::after {
  content: "";
  display: inline-block;
  width: 1em;
  height: 1em;
}

@media print,
screen and (max-width: 750px) {

  .privacy-check::before,
  .privacy-check::after {
    width: 1.25em;
    height: 1.25em;
  }
}

.privacy-check::before {
  margin-right: 0.5rem;
  background-color: #fff;
  border: 1px solid #cdd6dd;
  vertical-align: -0.15em;
  z-index: 1;
}

@media print,
screen and (max-width: 750px) {
  .privacy-check::before {
    vertical-align: -0.25em;
  }
}

.privacy-check::after {
  position: absolute;
  left: 0.1em;
  top: 0.25em;
  background: url(../images/common/input_check.svg) no-repeat center;
  background-size: 0.95em auto;
  opacity: 0;
}

@media print,
screen and (max-width: 750px) {
  .privacy-check::after {
    background-size: contain;
  }
}

input:checked+.privacy-check::after {
  opacity: 1;
}

.uq_contact--thanks {
  margin-top: 57px;
}

@media (max-width: 1200px) {
  .uq_contact--thanks {
    margin-top: 4.75vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_contact--thanks {
    margin-top: 3rem;
  }
}

@media print,
screen and (min-width: 751px) {
  .uq_contact--thanks__inner {
    max-width: 880px;
    padding: 0 40px;
    margin-left: auto;
    margin-right: auto;
  }
}

@media (max-width: 1200px) {
  .uq_contact--thanks__inner {
    max-width: 73.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_contact--thanks__inner {
    padding-left: 5vw;
    padding-right: 5vw;
    max-width: 100%;
  }
}

.uq_contact--thanks--ttl p {
  text-align: center;
  font-size: 28px;
  font-size: 1.75rem;
  line-height: 1.5;
  font-weight: 300;
}

@media (max-width: 1200px) {
  .uq_contact--thanks--ttl p {
    font-size: 2.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_contact--thanks--ttl p {
    text-align: left;
    font-size: 21px;
    font-size: 5.6vw;
  }
}

.uq_contact--thanks--lead {
  margin-top: 20px;
}

@media (max-width: 1200px) {
  .uq_contact--thanks--lead {
    margin-top: 1.66vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_contact--thanks--lead {
    margin-top: 2rem;
  }
}

.uq_contact--thanks--lead p {
  font-size: 16px;
  font-size: 1rem;
  line-height: 1.5;
  font-weight: 300;
  text-align: center;
}

@media (max-width: 1200px) {
  .uq_contact--thanks--lead p {
    font-size: 1.33vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_contact--thanks--lead p {
    font-size: 14px;
    font-size: 3.73333vw;
  }
}

.uq_contact--thanks--btn {
  padding-top: 61px;
  margin-top: 12px;
  margin-bottom: 90px;
}

@media (max-width: 1200px) {
  .uq_contact--thanks--btn {
    padding-top: 5.08vw;
    margin-top: 1vw;
    margin-bottom: 7.5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_contact--thanks--btn {
    margin-top: 2rem;
    margin-bottom: 4rem;
  }
}

.uq_contact-bottom p {
  text-align: center;
  margin-top: 30px;
  font-size: 14px;
  font-size: 0.875rem;
  line-height: 1.5;
  font-weight: bold;
}

@media (max-width: 1200px) {
  .uq_contact-bottom p {
    font-size: 1.16vw;
    margin-top: 2.5vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .uq_contact-bottom p {
    margin-top: 1.5rem;
    font-size: 12px;
    font-size: 3.2vw;
  }
}


/* CSS Vantelin Support */

.instruction-list {
  display: flex;
  width: 100%;
  justify-content: space-around;
  margin-top: 100px;
  align-items: baseline;
}


/* .instruction-list_new {
  display: flex;
  justify-content: flex-start;
  column-gap: 58px;
  margin-top: 100px;
  justify-content: center;
} */

.instruction-list__items {
  width: 17%;
  text-align: center;
}

.instruction-list__items--info {
  margin-top: 15px;
  min-height: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.instruction-list__items--btn {
  margin-top: 10px;
  -webkit-transition: 0.35s;
  transition: 0.35s;
}

@media (max-width: 1200px) {
  .instruction-list__items--btn {
    margin-top: 3vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .instruction-list {
    flex-wrap: wrap;
    margin-top: 50px;
  }

  /* .instruction-list_new {
    flex-wrap: wrap;
    column-gap: 25px;
    margin-top: 25px;
  } */
  .instruction-list__items {
    width: 45%;
    margin-bottom: 50px;
  }

  .instruction-list__items--btn {
    margin-top: 0.25rem;
  }
}

.instruction-list__items--btn.is-accoOpen p>a::before,
.instruction-list__items--btn.is-accoOpen p>a::after,
.instruction-list__items--btn.is-accoOpen p>span::before,
.instruction-list__items--btn.is-accoOpen p>span::after {
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}

.instruction-list__items--btn.is-accoOpen p>a::after,
.instruction-list__items--btn.is-accoOpen p>span::after {
  -webkit-transform: rotate(-180deg);
  transform: rotate(-180deg);
}

.instruction-list__items--btn p {
  display: table;
  border-collapse: separate;
  margin: 0 auto;
  position: relative;
  width: 165px;
  height: 48px;
}

@media (max-width: 1200px) {
  .instruction-list__items--btn p {
    width: 15.83vw;
    height: 4vw;
  }
}

@media print,
screen and (max-width: 750px) {
  .instruction-list__items--btn p {
    width: 7.46rem;
    height: 2.2rem;
  }
}

.instruction-list__items--btn p>a,
.instruction-list__items--btn p>span {
  border-radius: 24px;
  border: 1px solid #007f41;
  background-color: #007f41;
  color: #fff;
  display: table-cell;
  cursor: pointer;
  font: inherit;
  font-size: 16px;
  font-size: 1rem;
  font-family: "noto-sans-cjk-jp", "Noto Sans JP", "游ゴシック体", "Yu Gothic", YuGothic, sans-serif;
  letter-spacing: 0;
  text-align: center;
  vertical-align: middle;
  line-height: 1.45;
  text-decoration: none !important;
  padding: 0;
  white-space: nowrap;
  width: 100%;
  background-image: url(../images/common/icon_pdf_white.svg);
  background-size: auto 20px;
  background-repeat: no-repeat;
  background-position: 93% center;
  -webkit-transition: 0.35s;
  transition: 0.35s;
}

@media (max-width: 1200px) {

  .instruction-list__items--btn p>a,
  .instruction-list__items--btn p>span {
    font-size: 1.33vw;
  }
}

@media (max-width: 1200px) {

  .instruction-list__items--btn p>a::before,
  .instruction-list__items--btn p>a::after,
  .instruction-list__items--btn p>span::before,
  .instruction-list__items--btn p>span::after {
    right: 1.08vw;
    width: 1.33vw;
  }
}

@media print,
screen and (max-width: 750px) {

  .instruction-list__items--btn p>a::before,
  .instruction-list__items--btn p>a::after,
  .instruction-list__items--btn p>span::before,
  .instruction-list__items--btn p>span::after {
    right: 5vw;
    width: 1rem;
  }
}

.instruction-list__items--btn p>a::after,
.instruction-list__items--btn p>span::after {
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
}

@media print,
screen and (max-width: 750px) {

  .instruction-list__items--btn p>a,
  .instruction-list__items--btn p>span {
    font-size: 3.1vw;
    background-size: auto 15px;
    border-radius: 1.65rem;
  }
}

@media print,
screen and (min-width: 1025px) {

  .instruction-list__items--btn p>a:hover,
  .instruction-list__items--btn p>span:hover {
    color: #007f41;
    background-color: #fff;
    background-image: url(../images/common/icon_pdf_green.svg);
  }
}

.mt-5 {
  margin-top: 50px;
}

.mt-3 {
  margin-top: 30px;
}

.fw {
  max-width: 95%;
}

.pb-0 {
  padding-bottom: 0;
}

#content_lang {
  text-align: center;
  margin-top: 82px;
}

@media print,
screen and (max-width: 750px) {
  #content_lang {
    margin-top: 52px;
  }
}

#content_lang h2 {
  background-color: #000;
  color: #fff;
  padding: 25px 0 20px;
  font-size: 22px;
}

#content_lang ul {
  width: 280px;
  margin: 50px auto;
}

#content_lang ul li {
  border: 1px solid #444;
  margin-bottom: 20px;
}

#content_lang ul li a {
  text-decoration: none;
  display: block;
  padding: 25px 10px;
}

.is-idn .instruction-list__items--btn p {
  width: 200px;
}

.is-idn .is-Vantelin.thumb .m_linup--accodion__size--box{
  margin-left: 7%;
}
.is-idn .is-Vantelin.thumb .m_linup--accodion__size--box:first-child{
  margin-left: 0;
}

.is-idn .instruction-list__items--btn p>a,
.instruction-list__items--btn p>span {
  background-position: 97% center;
}
@media (min-width: 751px) {
  .is-idn.is-regionAll .instruction-list__items--btn p {
    width: 165px;
  }
  .is-idn.is-regionAll .instruction-list__items--btn p>a,
  .is-regionAll .instruction-list__items--btn p>span {
    padding: 0 23px;
    background-position: 98% center;
  }
}
@media (max-width: 1200px) {
  .is-idn .instruction-list__items--btn p {
    width: 16vw;
  }

  .is-idn .instruction-list__items--btn p>a,
  .instruction-list__items--btn p>span {
    font-size: 1.28vw;
    background-size: auto 15px;
    background-position: 97% center;
  }
}

@media print,
screen and (max-width: 750px) {
  .is-idn .instruction-list__items--btn p {
    width: 10rem;
  }

  .is-idn .instruction-list__items--btn p>a,
  .instruction-list__items--btn p>span {
    font-size: 2.8vw;
    background-size: auto 14px;
    background-position: 97% center;
  }
}


/*# sourceMappingURL=base.css.map */


/* Esport Thumb Armor
=================================================================================*/

.is-esport {
  background-color: #000;
}

#pageEsports .is-sp {
  display: none !important;
}

#pageEsports .is-pc {
  display: block !important;
}

#pageEsports {
  max-width: 1280px;
  margin: 0 auto;
}

#pageEsports .lineTtl {
  position: relative;
}

#pageEsports .lineTtl::before {
  content: '';
  position: absolute;
  width: 48px;
  height: 1px;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  background: #b90078;
  z-index: 1;
}

#pageEsports .lineTtl.lineClr01::before {
  background: #fcff01;
}

#pageEsports .question {
  position: relative;
  background-color: #fff;
}

#pageEsports .question .container {
  padding: 67px 0 82px;
  width: 800px;
  margin: 0 auto;
}

#pageEsports .question_txt,
#pageEsports .question_item {
  font-size: 20px;
  line-height: 1.65;
  color: #333333;
  position: relative;
}

#pageEsports .question_txt {
  font-weight: bold;
  margin-top: 7px;
}

#pageEsports .question_item {
  padding-left: 35px;
}

#pageEsports .point {
  padding-top: 30px;
  background-image: linear-gradient(90deg, rgb(125, 11, 84) 0%, rgb(72, 18, 98) 100%);
  padding-bottom: 4px;
}

#pageEsports .point .subImage {
  align-items: center;
  display: flex;
  justify-content: space-between;
  position: absolute;
  width: 550px
}

#pageEsports .point .point-01,
#pageEsports .point .point-02,
#pageEsports .point .point-03 {
  position: relative;
}

#pageEsports .point .point-02 {
  margin-top: -88px;
}

#pageEsports .point .point-03 {
  margin-top: -80px;
}

#pageEsports .point .subImage.point--01_subImage {
  bottom: 128px;
  left: 100px;
}

#pageEsports .point .subImage.point--02_subImage {
  right: 100px;
  bottom: 106px;
}

#pageEsports .point .subImage.point--03_subImage {
  bottom: 70px;
  left: 100px;
}

#pageEsports .point .subImage li {
  width: calc(100% / 2 - 30px * 1 / 2);
}

#pageEsports .point .subImage button:hover {
  filter: brightness(1.2);
}

#pageEsports .injuries>.container::before,
#pageEsports .question_item::before,
#pageEsports .injuries_ttl::before {
  content: '';
  position: absolute;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  pointer-events: none;
  z-index: 1;
}

#pageEsports .question_item::before {
  width: 16px;
  height: 16px;
  left: 0;
  top: 7px;
  background-image: url(../images/vantelin/esports/<EMAIL>);
}

#pageEsports .injuries_ttl {
  background: linear-gradient(90deg, rgb(125, 11, 84) 0%, rgb(72, 18, 98) 100%);
  text-align: center;
  font-size: 36px;
  color: #fcff01;
  line-height: 1.38;
  font-weight: bold;
  padding: 30px 0 40px;
  position: relative;
}

#pageEsports .injuries_ttl::before {
  width: 1280px;
  height: 26px;
  left: 50%;
  transform: translateX(-50%);
  top: 100%;
  background-image: url(../images/vantelin/esports/<EMAIL>);
}

#pageEsports .injuries>.container {
  position: relative;
  padding: 110px 0 100px;
}

#pageEsports .injuries>.container::before {
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background-image: url(../images/vantelin/esports/<EMAIL>);
  z-index: -1;
}

#pageEsports .injuries_content {
  width: 1080px;
  margin: 0 auto;
}

#pageEsports .injuries_content>.box {
  background-color: #fff;
  border-radius: 15px;
  margin-bottom: 80px;
  padding: 40px 67px 75px;
}

#pageEsports .injuries_content>.box:last-child {
  margin-bottom: 0;
  padding: 40px 67px 62px;
}

#pageEsports .injuries_secondary {
  font-size: 24px;
  color: rgb(51, 51, 51);
  font-weight: bold;
  line-height: 2.25;
  text-align: center;
  position: relative;
  margin-bottom: 60px;
  padding-bottom: 5px;
  font-style: italic;
}

#pageEsports .injuries_img {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40px;
}

#pageEsports .injuries_img>.img {
  width: calc(100%/ 2 - 66px * 1 /2);
}

#pageEsports .injuries .sym_item,
#pageEsports .injuries_txt {
  font-size: 16px;
  color: rgb(51, 51, 51);
  line-height: 1.625;
  text-align: justify;
}

#pageEsports .injuries .sym_item {
  padding-left: 15px;
  position: relative;
}

#pageEsports .injuries .sym_item::before {
  content: '';
  position: absolute;
  width: 5px;
  height: 5px;
  left: 0;
  top: 12px;
  background: #b90078;
  border-radius: 50%;
  z-index: 1;
}

#pageEsports .point_ttl {
  font-size: 36px;
  color: #fcff01;
  font-weight: bold;
  line-height: 1.385;
  margin-bottom: 10px;
  text-align: center;
  padding: 35px 0 30px;
}

#pageEsports .prevent_sgt {
  max-width: 1080px;
  margin: 0 auto;
}

#pageEsports .prevent_sgt-ttl {
  font-size: 44px;
  color: #fff;
  font-weight: bold;
  line-height: 1.667;
  text-align: center;
  margin-bottom: 43px;
  margin-top: 80px;
}

#pageEsports .prevent_sgt-img {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  position: relative;
}

#pageEsports .prevent_sgt-img {
  border: 1px solid #ba40d5;
}

#pageEsports .prevent_sgt-img::before,
#pageEsports .prevent_sgt-img::after {
  content: '';
  position: absolute;
  background-color: #ba40d5;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 1px;
  transform: translate(-50%, -50%);
  z-index: 1;
}

#pageEsports .prevent_sgt-img::after {
  height: 100%;
  width: 1px;
}

#pageEsports .prevent_sgt-img.subImg {
  flex-wrap: nowrap;
  /* margin-top: 87px; */
  border: none;
}

#pageEsports .prevent_sgt-img.subImg::before,
#pageEsports .prevent_sgt-img.subImg::after {
  content: none;
}

#pageEsports .prevent_sgt-img.subImg>.img {
  width: calc(100% / 4 - 30px * 3 / 4)
}

#pageEsports .prevent_sgt-img>.img {
  width: 50%;
}

#pageEsports .prevent_sgt-img button:hover {
  filter: brightness(1.2);
}


/* Modal */

.modal--wrapper {
  align-items: center;
  background: rgba(0, 0, 0, 0.6);
  display: none;
  height: 100vh;
  justify-content: center;
  left: 0;
  position: fixed;
  top: 0;
  width: 100vw;
  z-index: 3;
}

.modal {
  position: relative;
  width: 704px;
}

.modal__close-btn {
  position: absolute;
  right: -55px;
  top: -80px;
  width: 55px;
  background-color: transparent;
}

.modal img {
  display: none;
}

.modal__close-btn img {
  display: block !important;
}

@media print,
screen and (min-width: 751px) and (max-width: 1280px) {
  #pageEsports .question .container {
    padding: 5.234375% 0 6.40625%;
    width: 62.5%;
  }

  #pageEsports .question_item {
    padding-left: 1.5625vw;
  }

  #pageEsports .question_item::before {
    width: 1.25vw;
    height: 1.25vw;
    top: 0.546875vw;
  }

  #pageEsports .question_txt,
  #pageEsports .question_item {
    font-size: 1.40625vw;
  }

  #pageEsports .question_txt {
    margin-top: 0.546875%;
  }

  #pageEsports .injuries_ttl {
    font-size: 2.8125vw;
    padding: 2.34375% 0 3.125%;
  }

  #pageEsports .injuries_ttl::before {
    width: 100%;
    height: 2.03125vw;
  }

  #pageEsports .injuries>.container {
    padding: 8.59375% 0 7.8125%;
  }

  #pageEsports .prevent_ttl {
    font-size: 2.03125vw;
    padding: 1.953125%;
    margin-bottom: 2.1875%;
  }

  #pageEsports .injuries_content {
    width: 84.375%;
  }

  #pageEsports .injuries_content>.box {
    margin-bottom: 7.40740740740741%;
    padding: 3.7037037037037% 6.2037037037037% 6.94444444444444%;
  }

  #pageEsports .injuries_content>.box:last-child {
    padding: 3.7037037037037% 6.2037037037037% 5.74074074074074%;
  }

  #pageEsports .injuries_secondary {
    font-size: 1.875vw;
    margin-bottom: 5.55555555555556%;
    padding-bottom: 0.46296296296296%;
  }

  #pageEsports .lineTtl::before {
    width: 3.75vw;
  }

  #pageEsports .injuries_img {
    margin-bottom: 3.7037037037037%;
  }

  #pageEsports .injuries_img>.img {
    width: calc(100%/ 2 - 6.11111111111111% * 1 /2);
  }

  #pageEsports .injuries .sym_item,
  #pageEsports .injuries_txt {
    font-size: 1.25vw;
  }

  #pageEsports .injuries .sym_item {
    padding-left: 1.38888888888889%;
  }

  #pageEsports .injuries .sym_item::before {
    width: 0.390625vw;
    height: 0.390625vw;
    top: 0.9375vw;
  }

  #pageEsports .point {
    padding-top: 2.34375%;
    padding-bottom: 0.3125%;
  }

  #pageEsports .point_ttl {
    font-size: 2.813vw;
    padding: 2.734vw 0 2.344vw;
  }

  #pageEsports .point .subImage {
    width: 55px
  }

  #pageEsports .point .subImage {
    width: 42.96875vw
  }

  #pageEsports .point .point-02 {
    margin-top: -6.875vw;
  }

  #pageEsports .point .point-03 {
    margin-top: -6.250vw;
  }

  #pageEsports .point .subImage.point--01_subImage {
    bottom: 10vw;
    left: 7.813vw;
  }

  #pageEsports .point .subImage.point--02_subImage {
    right: 7.813vw;
    bottom: 8.281vw;
  }

  #pageEsports .point .subImage.point--03_subImage {
    bottom: 5.469vw;
    left: 7.813vw;
  }

  #pageEsports .point .subImage li {
    width: calc(100% / 2 - 2.34375vw * 1 / 2);
  }

  #pageEsports .prevent_sgt {
    max-width: 84.375vw;
  }

  #pageEsports .prevent_sgt-ttl {
    font-size: 3.438vw;
    margin-bottom: 8.4375%;
    margin-top: 6.250%;
  }

  #pageEsports .prevent_sgt-img.subImg {
    margin-top: 6.797vw;
  }

  #pageEsports .prevent_sgt-img.subImg>.img {
    width: calc(100% / 4 - 2.344vw * 3 / 4)
  }

  .modal {
    width: 55vw;
  }

  .modal__close-btn {
    right: -4.297vw;
    top: -6.250vw;
    width: 4.297vw;
  }
}

@media print,
screen and (max-width: 750px) {
  #pageEsports .is-sp {
    display: block !important;
  }

  #pageEsports .is-pc {
    display: none !important;
  }

  #pageEsports {
    max-width: 100%;
  }

  #pageEsports .question .container {
    width: 100%;
    padding: 8.4vw 4vw 8.4vw;
  }

  #pageEsports .question_txt,
  #pageEsports .question_item {
    font-size: 3.73333333333333vw;
  }

  #pageEsports .question_item::before {
    width: 3.2vw;
    height: 3.333vw;
    top: 1.867vw;
    background-image: url(../images/vantelin/esports/ico_question--sp.png);
  }

  #pageEsports .question_item {
    padding-left: 5.333vw;
  }

  #pageEsports .question_txt {
    font-weight: bold;
    margin-top: 2.133vw;
  }

  #pageEsports .injuries {
    padding: 0;
  }

  #pageEsports .injuries>.container {
    padding: 12.267vw 5.333vw 10vw;
  }

  #pageEsports .injuries>.container::before {
    background-image: url(../images/vantelin/esports/bg_injur--sp.jpg);
  }

  #pageEsports .injuries_content {
    width: 100%;
  }

  #pageEsports .injuries_ttl {
    font-size: 6.4vw;
    padding: 4.4vw 0 6.133vw;
  }

  #pageEsports .injuries_ttl::before {
    width: 100%;
    height: 3.467vw;
    background-image: url(../images/vantelin/esports/dec_injur--sp.png);
  }

  #pageEsports .injuries_content>.box {
    border-radius: 20px;
    margin-bottom: 8.933vw;
    padding: 8.267vw 5.333vw 9.067vw;
  }

  #pageEsports .injuries_content>.box:last-child {
    padding: 8.267vw 3.333vw 10.400vw;
  }

  #pageEsports .injuries_content>.box:last-child .injuries_secondary {
    margin-bottom: 8.400vw;
    padding-bottom: 3.733vw;
  }

  #pageEsports .injuries_content>.box:last-child .injuries_txt,
  #pageEsports .injuries_content>.box:last-child .injuries_img {
    padding: 0 2vw;
  }

  #pageEsports .injuries_secondary {
    font-size: 4.8vw;
    margin-bottom: 8.400vw;
    padding-bottom: 2.667vw;
    line-height: 1.5;
  }

  #pageEsports .lineTtl::before {
    width: 13.333vw;
    height: 2px;
  }

  #pageEsports .injuries_img {
    display: block;
  }

  #pageEsports .injuries_img>.img {
    width: 100%;
    margin-bottom: 4vw;
  }

  #pageEsports .injuries_img>.img:last-child {
    margin-bottom: 0;
  }

  #pageEsports .injuries .sym_item,
  #pageEsports .injuries_txt {
    font-size: 3.73333333333333vw;
    line-height: 1.635;
  }

  #pageEsports .injuries .sym_item,
  #pageEsports .injuries_txt {
    line-height: 1.64;
  }

  #pageEsports .injuries .sym_list {
    margin-top: 0.533vw;
    padding: 0 2vw;
  }

  #pageEsports .injuries .sym_item {
    padding-left: 3.067vw;
  }

  #pageEsports .injuries .sym_item::before {
    width: 0.933vw;
    height: 0.933vw;
    left: 0.667vw;
    top: 2.533vw;
  }

  #pageEsports .point {
    padding-top: 4vw;
    padding-bottom: 0px;
  }

  #pageEsports .point .subImage {
    width: 80vw;
  }

  #pageEsports .point .point-02 {
    margin-top: -6.933vw;
  }

  #pageEsports .point .point-03 {
    margin-top: -7.467vw;
  }

  #pageEsports .prevent_sgt-img.subImg {
    margin-top: 14.400vw;
  }

  #pageEsports .point .subImage.point--01_subImage {
    bottom: 13.333vw;
    left: 11.733vw;
  }

  #pageEsports .point .subImage.point--02_subImage {
    right: 8.400vw;
    bottom: 16vw;
  }

  #pageEsports .point .subImage.point--03_subImage {
    bottom: 18.133vw;
    left: 11.733vw;
  }

  #pageEsports .point_ttl {
    font-size: 5.067vw;
    padding: 7.733vw 0 4.933vw;
    margin-bottom: 4.933vw;
  }

  #pageEsports .prevent_sgt-ttl {
    font-size: 6.4vw;
    line-height: 1.26;
    margin-bottom: 6.4vw;
    margin-top: 10.267vw;
  }

  #pageEsports .prevent_sgt-img::before,
  #pageEsports .prevent_sgt-img::after {
    content: none;
  }

  #pageEsports .prevent_sgt-img {
    border: none;
  }

  #pageEsports .prevent_sgt-img>.img {
    border-top: 1px solid #c93adf;
  }

  #pageEsports .prevent_sgt-img.subImg>.img {
    border: none;
  }

  #pageEsports .prevent_sgt-img>.img {
    width: 100%;
  }

  #pageEsports .prevent_sgt-img>.img:last-child {
    border-bottom: 1px solid #c93adf;
  }

  #pageEsports .scene__otherScene__slider--onsp .slick-prev:before {
    background: url(../images/vantelin/esports/ico_prev.png) no-repeat center center/100% auto;
  }

  #pageEsports .scene__otherScene__slider--onsp .slick-next:before {
    background: url(../images/vantelin/esports/ico_next.png) no-repeat center center/100% auto;
  }

  #pageEsports .scene__otherScene__slider--onsp .slick-prev:before,
  #pageEsports .scene__otherScene__slider--onsp .slick-next:before {
    content: '';
    display: block;
    height: 14.400vw !important;
    width: 8.933vw !important;
    margin: 0 auto;
  }

  #pageEsports .scene__otherScene__slider--onsp li {
    margin: 0 3.6vw;
    width: 58.667vw;
  }

  #pageEsports .scene__otherScene__slider--onsp .slick-next,
  #pageEsports .scene__otherScene__slider--onsp .slick-prev {
    font-size: 0;
    line-height: 0;
    position: absolute;
    height: 19.400vw !important;
    width: 13.933vw !important;
    z-index: 3;
    cursor: pointer;
    color: transparent;
    background: transparent;
    border: none;
    outline: none;
    padding: 0;
    top: calc(50% + 7.2vw);
    max-width: 13.933vw;
  }

  #pageEsports .scene__otherScene__slider--onsp .slick-prev {
    left: 6.667vw;
    transform: translate(0, -50%);
  }

  #pageEsports .scene__otherScene__slider--onsp .slick-next {
    right: 6.667vw;
    transform: translate(0, -50%);
  }

  .modal {
    width: 90.667vw;
  }

  .modal__close-btn {
    right: 0;
    top: -8.667vw;
    width: 7.333vw;
  }
}


/* CSS Banner Covid */

.section_banner_covid {
  text-align: center;
  margin: -60px 0 -80px;
  z-index: 11;
  position: relative;
}

.section_banner_covid a {
  display: inline-block;
}

.section_banner_covid_mys {
  margin-bottom: 80px;
}

@media print,
screen and (max-width: 750px) {
  .section_banner_covid {
    margin: 0;
    padding: 0 5vw;
  }

  .section_banner_covid_mys {
    margin-bottom: 4rem;
  }
}

/* CSS LACB */

.uq_lacb-features__linup--lead {
  padding-top: 60px;
  padding-bottom: 60px;
}

.uq_lacb-features__linup--lead .m_title:after {
  content: none;
}

.uq_mask-notes__linup--section {
  padding-top: 62px;
}

.about_lacb {
  margin-top: 40px;
}

.about_lacb .txt {
  font-size: 1.125rem;
  line-height: 1.75;
  font-weight: 300;
  margin: 40px 0;
}

.two_col--img {
  display: flex;
  justify-content: center;
}

.two_col--img picture {
  width: 50%;
  text-align: center;
}

.notes-list {
  background-color: #fff;
  padding: 20px;
  margin-top: 60px;
}

.notes-list li {
  line-height: 1.7;
  text-indent: -16px;
  padding-left: 16px;
}

.m_linup--ec_lacb {
  width: 310px;
  margin-top: 40px;
}

.m_linup--ec--list ul li a.two-logo {
  height: auto;
  padding: 15px 18px;
}

.m_linup--ec--list ul li a.two-logo p {
  flex-wrap: wrap;
  height: 100%;
}

.m_linup--ec--list ul li a.two-logo img.img01 {
  width: 175px;
}

.m_linup--ec--list ul li a.two-logo img.img02 {
  width: 95px;
  margin-top: 8px;
  margin-left: -14px;
}


@media print,
screen and (max-width: 750px) {
  .m_linup--ec_lacb {
    width: 100%;
    margin-top: 5vw;
  }

  .m_linup--ec--list ul li a.two-logo img.img01 {
    width: 50vw;
  }

  .m_linup--ec--list ul li a.two-logo img.img02 {
    width: 20vw;
    margin-left: -3vw;
  }

  .uq_lacb-features__linup--lead {
    padding-top: 5vw;
    padding-bottom: 5vw;
  }

  .about_lacb {
    margin-top: 4vw;
  }

  .about_lacb .txt {
    margin: 4vw 0;
  }

  .two_col--img {
    flex-wrap: wrap;
  }

  .two_col--img picture {
    width: 100%;
  }

  .two_col--img picture:nth-child(n + 2) {
    margin-top: 20px;
  }

  .uq_mask-notes--section {
    padding-top: 5.16vw;
    padding-bottom: 5.16vw;
  }

  .notes-list {
    margin-top: 5vw;
    padding: 5vw;
  }

  .notes-list li {
    text-indent: -14px;
    padding-left: 14px;
  }

  .uq_mask-notes__linup--section {
    padding-top: 5.16vw;
  }
}
/* ---------------------- */
.uq_top-direction--section{
  padding-top: 220px;
  padding-bottom: 80px;
  background: #fff;
}

.uq_top-direction--inner{
  max-width: 1080px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 40px;
  padding-right: 40px;
  display: flex;
  justify-content: space-between;
  gap: 1.25rem;
}
.direction_item--link {
  max-width: 48%;
  color: #fff;
  background: #156082;
  width: 100%;
  display: flex;
  flex-direction: column;
  padding:2.438rem 2.25rem  1.75rem;
  border-radius: 1.25rem;;
  transition: opacity 0.3s ease-in-out;
  z-index: 1;
}
.direction_item--link:hover {
  opacity: 0.7;
}
.direction_item--link.is-green {
  background: #00B050;
}
.direction_item--title{
  font-size: 1.75rem;
  text-align: center;
  text-decoration: underline;
  margin-bottom: 1.25rem;;
  position: relative;
  z-index: 3;
}
.direction_item--list{
  list-style: disc;
  padding-left: 1.25rem;;
  position: relative;
  z-index: 3;
  flex: 1 1 auto;
}
.direction_item--list li{
  line-height: 1.5;
}
.is-page-mys-en .uq_top-vantelin_support--section{
    margin-top: 0;
    padding-top: 0;
  }
@media (max-width:750px) {
  .uq_top-direction--inner{
    flex-direction: column;
  }
  .direction_item--link {
    max-width: 100%;
  }
  .uq_top-direction--section{
    padding-top: 11rem;
  }
  .is-page-mys-en .uq_top-vantelin_support--section{
    /* margin-top: 15vw; */
    margin-top: 0;
  }
  .is-mys_md .uq_tvantelin-instruction--section,.is-mys_gg .uq_tvantelin-instruction--section{
    margin-top: 4rem;
  }
}
.is-mys_md .uq_mask-lineup__lead--box--txt p.m_linup__ann{
  font-size: 0.75rem;
}

.is-mys_md .uq_tvantelin-instruction--section,.is-mys_gg .uq_tvantelin-instruction--section{
  margin-bottom: 120px;
}
.is-page-mys-en .l_youtube{
  margin-top: 0;
  padding-top: 40px;
  background-color: #fff;
}

.is-full-height body{
  background-color: #fff;
}
.is-full-height .l_wrap{
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 100dvh;
}
.is-full-height .l_main{
  flex-grow: 1;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}
.is-full-height .l_footer{
  background-color: #fff;
  max-height: max-content;
  flex-shrink: 0;
}
.is-regionAll .instruction-list_new_multi .instruction-list__items{
  width: 30%;
}
@media (max-width:750px){
  .is-regionAll .instruction-list_new_multi .instruction-list__items{
    width: 100%;
  }
}
/* --------------- page /mys/en/index2.php style 1 & style 2 ------------------- */
.is-mys-index02 .uq_top-direction--inner{
  gap: 2.5rem;
}
.is-mys-index02 .uq_top-direction--section,.is-mys-index02 .l_main{
  background-color: #fff9ed;
}
.is-mys-index02 .direction_item--link {
  background-color: transparent;
  color: #333333;
  position: relative;

}
.is-mys-index02 .direction_item--link:hover {
  opacity: 1;
}
.is-mys-index02 .direction_item--link .m_btn > span{
  transition: 0.35s;
}
.is-mys-index02 .direction_item--link:hover .m_btn > span {
  color: #fff;
  background-color: #e50113;
  border-color: #e50113;
  background-image: url(../images/common/icon_arrow_white.svg);
  background-position: 95% center;
}
.is-mys-index02 .direction_item--link::before {
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  border-radius: 1.25rem;
  pointer-events: none;
  background: #fff;
  z-index: 0;
  top:0;
  left: 0;
}
.is-mys-index02 .direction_item--link::after{
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  border-radius: 1.25rem;
  background: transparent -webkit-gradient(linear, left top, right top, from(#f5bd41), color-stop(66%, #ef7c31), to(#e50012)) 0% 0% no-repeat padding-box;
  background: transparent linear-gradient(90deg, #f5bd41 0%, #ef7c31 66%, #e50012 100%) 0% 0% no-repeat padding-box;
  bottom: -10px;
  right: -10px;
  z-index: -1;
  pointer-events: none;
}
.is-mys-index02.br .direction_item--link::after{
  content: "";
  position: absolute;
  height: 100%;
  width: 100%;
  border-radius: 1.25rem;
  background: transparent;
  bottom: 0;
  right: 0;
  z-index: -1;
  box-shadow: 0px 0px 10px 0px rgba(185, 185, 185, 0.3);
  pointer-events: none;
}
.is-mys-index02 .direction_item--title{
  font-size: 2.25rem;
  text-align: center;
  margin-bottom: 2.5rem;
  position: relative;
  z-index: 3;
  text-decoration: none;
}
.is-mys-index02 .direction_item--list{
  padding-left: 1.375rem;
  flex: 1 1 auto;
  margin-bottom: 1.8rem;
  list-style: none;
}
.is-mys-index02 .direction_item--list li{
  line-height: 1.75;
  font-size: 1rem;
  position: relative;
}
.is-mys-index02 .direction_item--list li::before{
  border-radius: 50%;
  background-color: #333;
  position: absolute;
  top: 0.5rem;
  left: -1.375rem;
  width: 14px;
  height: 14px;
  content: "";
}
@media (min-width:751px) and (max-width:900px){
  .is-mys-index02 .direction_item--title{
    font-size: 1.875rem;
  }
}
.m_linup--ec--list ul li a p.shop_h-40{
  height: 43px;
}
@media (min-width:751px){
  .is-regionAll .instruction-list_new.instruction-list_new_multi .instruction-list__items--image{
    width: 100%;
    margin: 0 auto;
  }
  .is-regionAll .instruction-list_new .instruction-list__items--image{
    width: 68.5%;
    margin: 0 auto;
  }

}
@media (max-width:750px){
  .is-regionAll .instruction-list_new.instruction-list_new_multi .instruction-list__items--image{
    width: 100%;
  }
  .is-regionAll .instruction-list_new .instruction-list__items--image{
    width: 82%;
    margin: 0 auto;
  }
}
@media (min-width: 751px) {
  .is-idn .m_linup--btn p>a,.is-idn .m_linup--btn p>span {
      padding: 0 40px;
  }
}

@media (min-width:751px){
  .lumbar--section .m_linup--image{
    max-width: 100%;
  }
}

@media (max-width:750px){
  .lumbar--section .m_linup--image{
    max-width: 18rem;
  }
}
 .text_cap-img{
  text-align: center;
  display: inline-block;
  width: 100%;
  margin-top: 1rem;
  line-height: 1.5;
}
.is-noWrap {
  white-space: nowrap;
  display: flex;
  justify-content: center;
 }
/* -------------- jelly drink master --------------*/
@media (min-width:751px){
  .uq_top__lead__lineup--txt.is-jelly{
    width: 47%;
  }
  .is-page-top .l_header-nav {
    padding: 0;
    max-width: 90%;
    width: 1350px;
  }
  .is-page-top .l_header-nav__item {
    margin: 0 !important;
    width: 25%;
  }
  .uq_top-mask--jelly--banner::after{
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 56px;
    z-index: 1;
    background-color: #f8a943;
    pointer-events: none;
  }
  .jelly-page .m_datail_nav__list-area--child__item:nth-child(2){
   justify-content: flex-start;
  }
  .jelly-page .m_datail_nav__name{
    width: 28%;
  }
  .jelly-page .m_datail_nav__list-area--child--child__inner{
    padding: 0;
  }
  .jelly-page .m_datail_nav__list-area--child--child__item>a{
    padding: 8px 0 ;
  }
  .jelly-page .m_datail_nav__list-area--item {
    margin-left: 94px;
  }
  .jelly-page .m_linup--image{
    max-width: 100%;
  }
}
@media (max-width:750px){
  .jelly-page  .m_datail_nav__list-area--child--child__item:first-child{
    border-top: 0;
  }
}
.jelly-page .uq_mask-lineup__lead--box--title h1{
  line-height: 1.3;
}
.uq_top-mask--jelly--text img{
  object-fit: contain;
}
.uq_top-mask--jelly--banner{
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  pointer-events: none;
}
