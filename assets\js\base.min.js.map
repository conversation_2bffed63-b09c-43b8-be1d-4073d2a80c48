{"version": 3, "names": [], "mappings": "", "sources": ["base.js"], "sourcesContent": ["var ENGINE = ENGINE || {};\nvar adjustH = 40;\nvar winW;\nvar headerH;\n\n\n$(function() {\n\t\n});\nENGINE.common = {\n\tinit: function() {\n\t\t// 高さ揃え\n\t\t//$('.js-matchHeight').matchHeight();\n\t},\n\t__vh: function() {\n\t\t// --vhというカスタムプロパティを作成\n\t\tvar vh = window.innerHeight * 0.01;\n\t\tdocument.documentElement.style.setProperty('--vh', vh + 'px');\n\n\t\t// window resize\n\t\tvar $winWidth = $(window).width();\n\t\t$(window).on('resize', function(){\n\t\t\tvar $winWidthResize = $(window).width();\n\t\t\tif($winWidth !== $winWidthResize) {\n\t\t\t\tvh = window.innerHeight * 0.01;\n\t\t\t\tdocument.documentElement.style.setProperty('--vh', vh + 'px');\n\t\t\t}\n\t\t});\n\t},\n\t_lord_resize: function() {\n\t\t\n\t\t$(window).on('load resize', function() {\n\t\t\twinW = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;\n\t\t\theaderH = $('.l_header').outerHeight();\n\t\t});\n\t}\n};\n\nENGINE.scroll = {\n\tinit: function() {\n\t\tvar hasHash = location.hash;\n\t\t\n\t\tif(hasHash) {\n\t\t\t$('body, html').stop().scrollTop(0);\n\t\t\tsetTimeout(function(){\n\t\t\t\tvar target = $(hasHash);\n\t\t\t\tvar position = target.offset().top - adjustH;\n\t\t\t\t$('body,html').stop().animate({scrollTop: position}, 500, 'swing');\n\t\t\t}, 200);\n\t\t}\n\t\t$('.js-scroll a[href^=\"#\"], a.js-scroll[href^=\"#\"]').on('click', function() {\n\t\t\tif(typeof $('.js-accoBtnHeader') !== 'undefined' ) {\n\t\t\t\tvar $subNav = $('.js-accoBtnHeader');\n\t\t\t\t$subNav.each(function() {\n\t\t\t\t\tif ($(this).hasClass('is-accoOpen')) {\n\t\t\t\t\t\t$(this).next().slideToggle(300);\n\t\t\t\t\t}\n\t\t\t\t\t$(this).removeClass('is-accoOpen');\n\t\t\t\t\t\n\t\t\t\t});\n\t\t\t}\n\t\t\tif(winW < 750) {\n\t\t\t\tif ($('.l_header').hasClass('is-navOpen')) {\n\t\t\t\t\tENGINE.header._spNav();\n\t\t\t\t\t$('.l_header-nav-wrap').fadeToggle(200);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (typeof $($(this).attr('href')).offset().top !== 'undefined') {\n\t\t\t\t$('html, body').animate({\n\t\t\t\t\tscrollTop: $($(this).attr('href')).offset().top - adjustH\n\t\t\t\t}, 500, 'swing');\n\t\t\t}\n\t\t\treturn false;\n\t\t});\n\t}\n};\nENGINE.header = {\n\t_spNav: function() {\n\t\t//--- ヘッダー\n\t\tif ($('.l_header').hasClass('is-navOpen')) {\n\t\t\t$('.l_header').removeClass('is-navOpen');\n\t\t\t$('.l_main').removeClass('is-navOpen');\n\t\t\t$('body').removeClass('is-fixed');\n\t\t\t$('.m_pagetop').slideToggle(0);\n\t\t\t//--- ヘッダーアニメーション\n\t\t\t$('.js-headerIn').removeClass('is-show');\n\t\t\t$('.js-headerIn li').removeClass('is-show');\n\t\t\t\n\t\n\t\t} else {\n\t\t\t$('.l_header').addClass('is-navOpen');\n\t\t\t$('.l_main').addClass('is-navOpen');\n\t\t\t$('body').addClass('is-fixed');\n\t\t\t$('.m_pagetop').slideToggle(0);\n\t\t\t\n\t\t\t//--- ヘッダーアニメーション\n\t\t\tvar $this = $('.js-headerIn');\n\t\t\t\n\t\t\t$this.delay(100).queue(function(){\n\t\t\t\t$this.addClass('is-show').dequeue();;\n\t\n\t\t\t\t$this.find('.js-headerIn__item').each(function(idx) {\n\t\t\t\t\tvar $child = $(this);\n\t\t\t\t\t$child.delay(200 * (idx + 1)).queue(function() {\n\t\t\t\t\t\t$child.addClass('is-show').dequeue();\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t});\n\t\t}\n\t},\n\t_spSubNav: function() {\n\t\t//--- ヘッダー\n\t\tif ($('.m_datail_nav').hasClass('is-navOpen')) {\n\t\t\t$('.m_datail_nav').removeClass('is-navOpen');\n\t\t\t//$('.l_main').removeClass('is-navOpen');\n\t\t\t//$('body').removeClass('is-fixed');\n\t\t\t$('.m_pagetop').slideToggle(0);\n\t\t\t//--- ヘッダーアニメーション\n\t\t\t//$('.js-headerIn').removeClass('is-show');\n\t\t\t//$('.js-headerIn li').removeClass('is-show');\n\t\t\t\n\t\n\t\t} else {\n\t\t\t$('.m_datail_nav').addClass('is-navOpen');\n\t\t\t//$('.l_main').addClass('is-navOpen');\n\t\t\t//$('body').addClass('is-fixed');\n\t\t\t$('.m_pagetop').slideToggle(0);\n\t\t}\n\t},\n\t_blackOnclick: function() {\n\t\t//SPナビ以外の場所をクリックした場合、ナビを閉じる\n\t\tvar $navWrap = $('.l_header-nav-wrap');\n\t\t$(document).on('click touchstart', function(event) {\n\t\t\tif (!$(event.target).closest('.l_header').length) {\n\t\t\t\tif ($('.l_header').hasClass('is-navOpen')) {\n\t\t\t\t\tENGINE.header._spNav();\n\t\t\t\t\t$navWrap.fadeToggle(200);\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t}\n};\nENGINE.NaviBtn = {\n\tinit: function() {\n\t\t// ナビボタン\n\t\tvar $navBtn = $('.js-spNavBtn');\n\t\tvar $navWrap = $('.l_header-nav-wrap');\n\t\t$navBtn.on('click', function() {\n\t\t\tENGINE.header._spNav();\n\t\t\t$navWrap.fadeToggle(200);\n\t\t});\n\t},\n\tsubNav: function() {\n\t\t// 各商品のサブナビボタン \n\t\tvar $navBtn = $('.js-SubNavBtn');\n\t\tvar $navWrap = $('.m_datail_nav__list');\n\t\tvar $datail_nav =$('#datail_nav');\n\n\t\t$navBtn.on('click', function() {\n\t\t\tENGINE.header._spSubNav();\n\t\t\t$navWrap.fadeToggle(200);\n\t\t\tif ($datail_nav.hasClass('is-navOpen') && $datail_nav.hasClass('is_fixed')) {\n\t\t\t\t$('body').addClass('is-fixed');\n\t\t\t}else {\n\t\t\t\t$('body').removeClass('is-fixed');\n\t\t\t}\n\t\t});\n\t}\n};\n\n// ヘッダーのグローバルナビのホバー\nENGINE.NaviGlobalHover = {\n\tinit: function() {\n\t\t$('.js_global').hover(\n\t\t\tfunction() {\n\t\t\t\tif(winW > 750) {\n\t\t\t\t\tif ($('.nav__item').hasClass('is-haslongChild')) {\n\t\t\t\t\t}else {\n\t\t\t\t\t\t$('.l_header-nav_global-wrap').stop(true).addClass('is-navOpen');\n\t\t\t\t\t\t$('.l_main').stop(true).addClass('is-navOpen');\n\t\t\t\t\t\t\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tfunction () {\n\t\t\t\tif(winW > 750) {\n\t\t\t\t\tif ($('.nav__item').hasClass('is-haslongChild')) {\n\t\t\t\t\t}else {\n\t\t\t\t\t\t$('.l_header-nav_global-wrap').removeClass('is-navOpen');\n\t\t\t\t\t\t$('.l_main').removeClass('is-navOpen');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t);\n\t\t\n\t}\n};\n\n// vantelinのサブメニュースクロール\nENGINE.DatailNav = {\n\tinit: function() {\n\n\t\tvar $navWrap;\n\t\t$navWrap = $('.m_datail_nav__list');\n\t\t\n\t\tvar $header = $('.l_header');\n\t\tvar __targetSubNavigation;\n\t\tvar subNavigation = $('#datail_nav');\n\t\tvar __width;\n\t\tvar $winWidth = $(window).width();\n\t\t\n\t\t$(window).on('load', function() {\n\t\t\t__targetSubNavigation = (subNavigation.get(0))?subNavigation.offset().top:false;\n\t\t\t__width = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;\n\t\t});\n\n\t\t$(window).on('resize', function() {\n\t\t\t__targetSubNavigation = (subNavigation.get(0))?subNavigation.parent().offset().top:false;\n\t\t\t__width = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;\n\t\t\tvar $winWidthResize = $(window).width();\n\t\t\t/// SPからPCへ切り替えたた際のNavの表示・非表示\n\t\t\tif($winWidth !== $winWidthResize) {\n\t\t\t\tif (__width > 750) {\n\t\t\t\t\tif (subNavigation.hasClass('is-navOpen')) {\n\t\t\t\t\t}else {\n\t\t\t\t\t\t$navWrap.css(\"display\",\"block\");\n\t\t\t\t\t}\n\t\t\t\t}else {\n\t\t\t\t\t$navWrap.css(\"display\",\"none\");\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\n\n\t\t\n\t\t\n\n\t\t$(window).on(\"scroll\", function() {\n\t\t\t\n\t\t\tvar $currentPos = $(window).scrollTop();\n\t\t\tif(__targetSubNavigation) {\n\t\t\t\tif($currentPos > __targetSubNavigation) {\n\t\t\t\t\t//console.log($currentPos);\n\t\t\t\t\tsubNavigation.addClass('is_fixed');\n\t\t\t\t\t$header.addClass('is-none');\n\t\t\t\t\tif (subNavigation.hasClass('is-navOpen') ) {\n\t\t\t\t\t\t$('body').addClass('is-fixed');\n\t\t\t\t\t}else {\n\t\t\t\t\t\t$('body').removeClass('is-fixed');\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t} else{\n\t\t\t\t\tsubNavigation.removeClass('is_fixed');\n\t\t\t\t\t$header.removeClass('is-none');\n\t\t\t\t\t$('body').removeClass('is-fixed');\n\t\t\t\t}\n\t\t\t}\n\t\t\tif($currentPos < 60) {\n\t\t\t\t$header.addClass('is-break');\n\t\t\t} else {\n\t\t\t\t$header.removeClass('is-break');\n\t\t\t}\n\t\t\t\n\t\t});\n\n\t\t$('.m_datail_nav__list-area--item.is-hasChild').hover(\n\t\t\tfunction() {\n\t\t\t\tif(__width > 750) {\n\t\t\t\t\tif ($('.nav__item').hasClass('is-haslongChild')) {\n\t\t\t\t\t}else {\n\t\t\t\t\t\t$(this).stop(true).addClass('is-navSubOpen');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tfunction () {\n\t\t\t\tif(__width > 750) {\n\t\t\t\t\tif ($('.nav__item').hasClass('is-haslongChild')) {\n\t\t\t\t\t}else {\n\t\t\t\t\t\t$(this).removeClass('is-navSubOpen');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t);\n\n\t\t$('.js-accoBtnHeader').on('click', function() {\n\t\t\tif(__width <= 750) {\n\t\t\t\tENGINE.accoSP.init($(this));\n\t\t\t}\n\t\t});\n\t\t\n\t}\n};\nENGINE.accoBtnSpCloce = {\n\tinit: function() {\n\t\tvar $winWidth = $(window).width();\n\t\tvar __width;\n\t\tvar $accoBtn = $('.m_linup--accodion.is-accoBtn')\n\t\t$(window).on('load', function() {\n\t\t\t__width = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;\n\t\t\tif (__width <= 750) {\n\t\t\t\t$accoBtn.find('.js-accoBtn').each(function() {\n\t\t\t\t\t$(this).removeClass('is-accoOpen');\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\t\t$(window).on('resize', function() {\n\t\t\tvar $winWidthResize = $(window).width();\n\t\t\t__width = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;\n\t\t\tif($winWidth !== $winWidthResize) {\n\t\t\t\tif (__width <= 750) {\n\t\t\t\t\t$accoBtn.find('.js-accoBtn').each(function() {\n\t\t\t\t\t\t$(this).removeClass('is-accoOpen');\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t\t\n\t}\n};\nENGINE.accoBtn = {\n\tinit: function() {\n\t\t$('.js-accoBtn').on('click', function() {\n\t\t\tENGINE.accoSP.init($(this));\n\t});\n\t}\n};\n// マクス用にSPアコーディオン。PCはアコーディオンなし\nENGINE.accoBtnSp = {\n\tinit: function() {\n\t\t$('.js-accoBtnSp').on('click', function() {\n\t\t\tif(winW < 750) {\n\t\t\t\tENGINE.accoSP.init($(this));\n\t\t\t}\n\t\t});\n\t}\n};\nENGINE.accoSP = {\n\tinit: function($this) {\n\t\tif (typeof $this !== 'undefined') {\n\t\t\tif ($this.hasClass('is-accoOpen')) {\n\t\t\t\t$this.removeClass('is-accoOpen');\n\t\t\t} else {\n\t\t\t\t$this.addClass('is-accoOpen');\n\t\t\t}\n\t\t\t$this.next().slideToggle(300);\n\t\t}\n\t\t\n\t}\n};\nENGINE.readmore = {\n\tinit: function() {\n\t\tvar $this;\n\t\tvar $open_txt = 'Read More';\n\t\tvar $close_txt = 'Read More';\n\t\t$('#read_more').on('click', function() {\n\t\t\t$this = $(this);\n\t\t\tif (typeof $this !== 'undefined') {\n\t\t\t\tif ($this.hasClass('is-accoOpen')) {\n\t\t\t\t\t$this.removeClass('is-accoOpen');\n\t\t\t\t\t$open_txt = $this.data('open');\n\t\t\t\t\t$this.find('span').text($open_txt);\n\t\t\t\t} else {\n\t\t\t\t\t$this.addClass('is-accoOpen');\n\t\t\t\t\t$close_txt = $this.data('close');\n\t\t\t\t\t$this.find('span').text($close_txt);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t$this.prev().slideToggle(300);\n\t\t\t}\n\t});\n\t}\n};\nENGINE.DatailNavOnClick = {\n\tinit: function() {\n\t\tvar $navWrap = $('.m_datail_nav__list');\n\t\t$('.m_datail_nav__list-area--item a').on('click', function() {\n\t\t\tif(winW < 750) {\n\t\t\t\tENGINE.header._spSubNav();\n\t\t\t\t$navWrap.fadeToggle(200);\n\t\t\t} \n\t\t\t$('.m_datail_nav__list-area--item').removeClass('is-navSubOpen');\n\t\t});\n\t}\n};\nENGINE.ContactonClick = {\n\tinit: function() {\n\t\t$('.js_contact').on('copy contextmenu', function(){\n\t\t\treturn false; \n\t\t});\n\t}\n};\n\nENGINE.slide = {\n\tinit: function() {\n\t\t// ナビボタン\n\t\t$('.js-slideIn').on('inview', function() {\n\t\t\tvar $this = $(this);\n\t\t\t$this.delay(230).queue(function(){\n\t\t\t\t$this.addClass('is-show');\n\t\n\t\t\t\t$this.find('.js-slideIn__item').each(function(idx) {\n\t\t\t\t\tvar $child = $(this);\n\t\t\t\t\t$child.delay(230 * (idx + 1)).queue(function() {\n\t\t\t\t\t\t$child.addClass('is-show');\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t});\n\t\t});\n\t\t$('.js-orderIn').on('inview', function() {\n\t\t\tvar item = $(this).children();\n\t\t\tvar delay = 600;\n\t\t\titem.each(function(i){\n\t\t\t\tvar $this = $(this);\n\t\t\t\t$this.delay(i *delay).queue(function(next) {\n\t\t\t\t\t$this.addClass('is-show');\n\t\t\t\t\tnext();\n\t\t\t\t});\n\t\t\t});\n\t\t});\n\t\t$('.is-anime').on('inview', function() {\n\t\t\tvar $this = $(this);\n\t\t\t$this.delay(230).queue(function(){\n\t\t\t\t$this.addClass('is-show');\n\t\n\t\t\t\t$this.find('.is-anime__Rightin').each(function(idx) {\n\t\t\t\t\tvar $child = $(this);\n\t\t\t\t\t$child.delay(230 * (idx + 1)).queue(function() {\n\t\t\t\t\t\t$child.addClass('is-show');\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t\t$this.find('.is-anime__Upin').each(function(idx) {\n\t\t\t\t\tvar $child = $(this);\n\t\t\t\t\t$child.delay(230 * (idx + 1)).queue(function() {\n\t\t\t\t\t\t$child.addClass('is-show');\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t\t$this.find('.is-anime__Leftin').each(function(idx) {\n\t\t\t\t\tvar $child = $(this);\n\t\t\t\t\t$child.delay(230 * (idx + 1)).queue(function() {\n\t\t\t\t\t\t$child.addClass('is-show');\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t\t$this.find('.is-anime__Downin').each(function(idx) {\n\t\t\t\t\tvar $child = $(this);\n\t\t\t\t\t$child.delay(230 * (idx + 1)).queue(function() {\n\t\t\t\t\t\t$child.addClass('is-show');\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t\t$this.find('.is-anime__item').each(function(idx) {\n\t\t\t\t\tvar $child = $(this);\n\t\t\t\t\t$child.delay(230 * (idx + 1)).queue(function() {\n\t\t\t\t\t\t$child.addClass('is-show');\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t});\n\t\t});\n\t}\n};\nENGINE.mask = {\n\tinit: function() {\n\t\tspeed = 1000;\n\t\teasing = \"easeInOutCubic\";\n\t\t// タグ生成\n\t\t$mask = $(\".m_mask\");\n\t\t$mask.wrap(\"<div class='m_mask-wrap'>\");\n\t\t$mask.append(\"<div class='m_mask-bg'></div>\");\n\t\t$('.is-mask').on('inview', function() {\n\t\t\tvar $this = $(this);\n\t\t\t$this.find('.m_mask').each(function(idx) {\n\t\t\t\tvar $child = $(this);\n\t\t\t\t$child.delay(230 * (idx + 1)).queue(function() {\n\t\t\t\t\t$child.addClass('is-show');\n\t\t\t\t\t$child.find(\".m_mask-bg\").addClass('is-show');\n\t\t\t\t});\n\t\t\t});\n\t\t});\n\t}\n};\nENGINE.pageTop = {\n\tinit: function() {\n\t\tvar $footer_pagetop = $('#footer_pagetop');\n\n\t\t$(window).on('scroll', function() {\n\t\t\tvar now = $(this).scrollTop();\n\n\t\t\tvar $mvHeight = $('#mv').innerHeight();\n\t\t\tvar $footerHeight = $('#footer').innerHeight();\n\t\t\tif (typeof $mvHeight !== 'undefined' ) {\n\t\t\t\tif (($mvHeight / 3 ) < now) {\n\t\t\t\t\t$footer_pagetop.addClass('is-fadeIn');\n\t\t\t\t} else {\n\t\t\t\t\t$footer_pagetop.removeClass('is-fadeIn');\n\t\t\t\t}\n\t\t\t}else {\n\t\t\t\t$footer_pagetop.addClass('is-fadeIn');\n\t\t\t}\n\t\t\t\n\t\t});\n\t}\n};\n\n\n/////////////////////////////////////////////////////////////\n// 各種処理実行（初期処理呼び出し）\nwindow.addEventListener('DOMContentLoaded', function() {\n\n\t\n\tENGINE.common._lord_resize();\n\t// vhの高さをCSSに渡す\n\tENGINE.common.__vh();\n\n\t// ページトップ\n\tENGINE.pageTop.init();\n\t\n\t// アンカースクロール\n\tENGINE.scroll.init();\n\n\tENGINE.header._blackOnclick();\n\t// subナビ\n\tENGINE.DatailNav.init();\n\n\tENGINE.NaviGlobalHover.init();\n\tENGINE.DatailNavOnClick.init();\n\t// 高さ揃えなど\n\tENGINE.NaviBtn.init();\n\n\tENGINE.NaviBtn.subNav();\n\tENGINE.accoBtn.init();\n\tENGINE.accoBtnSp.init();\n\t// SP用　アコーディオン\n\tENGINE.accoSP.init();\n\t\n\t// リードmore\n\tENGINE.readmore.init();\n\n\t// SPのアコーディオン閉じる\n\tENGINE.accoBtnSpCloce.init();\n\n\t// スライドインアニメーション\n\tENGINE.slide.init();\n\n\t// マスクアニメーション\n\tENGINE.mask.init();\n\n\t// 右クリック禁止\n\tENGINE.ContactonClick.init();\n\t\n});"], "file": "base.min.js"}