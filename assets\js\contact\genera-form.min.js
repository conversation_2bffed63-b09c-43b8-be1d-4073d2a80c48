// メールフォーム用ライブラリが読み込まれている場合のみ処理
var Mm = Mm || {};
if (Mm.MailForm) {

	(function($) {

		var Obj = {}

		/**
		 * 初期化処理
		 *
		 */
		Obj.init = function() {

			// メールフォームの初期化（EFO）
			Mm.MailForm.init('#js-form-input', {
				func: {
					// validate実行前 or 実行後の関数実行
					// お名前
					name: {
						before: function() {
							// 一部機種依存文字の置換
							Obj.convertJIS('name');
						}
					},
					name_kana: {
						before: function() {
							// 一部機種依存文字の置換
							Obj.convertJIS('name_kana');
						}
					},
					email: {
						before: function() {
							// 全角→半角置換
							Obj.convertZen2Han('email');
						}
					},
					email_conf: {
						before: function() {
							// 全角→半角置換
							Obj.convertZen2Han('email_conf');
						}
					}
				},
				cls: {
					error: 'error',
					success: 'success'
				},
				submitDisable: false,
				errorShowAnimation: false,
				hintShowAnimation: false,
				isUnloadCheck: true,
				unloadMessage: 'お問い合わせが完了しておりません。\nお問い合わせを中止し、ページを移動しますか？'
			});

			// プライバシーポリシーcheckbox
			// Obj.privacyInit();


			// 名前の自動カタカナ入力
      $.fn.autoKana('#js-linkage-name', '#js-linkage-name_kana', {
				katakana : false  //true：カタカナ、false：ひらがな（デフォルト）
      });

		};


		/**
		 * チェックボタンの制御
		 *
		 */
		Obj.privacyInit = function(){

			var $checkBtn = $('#js-privacy');
			var $wrapper = $checkBtn.parent();
			var $submit  = $('.js-submit');

			function judgePrivacy() {
				if ($checkBtn.prop('checked')) {
					$wrapper.addClass('is_active');
					$submit.prop('disabled', false).addClass('is_release');
				} else {
					$wrapper.removeClass('is_active');
					$submit.prop('disabled', true).removeClass('is_release');
				}
			}

			if ($checkBtn.length > 0) {
				$checkBtn.on('click', judgePrivacy);
				judgePrivacy();
			}
		}

		/**
		 * 半角カタカナを全角に変換
		 *
		 */
		Obj.convertHankana2Zenkana = function(fieldName) {
			if (Mm.MailForm.form[fieldName]) {
				Mm.MailForm.form[fieldName].value = Mm.Validation.convertHankana2Zenkana(Mm.MailForm.form[fieldName].value);
			}
		};
		/**
		 * 全角文字を半角に変換
		 *
		 */
		Obj.convertZen2Han = function(fieldName) {
			if (Mm.MailForm.form[fieldName]) {
				Mm.MailForm.form[fieldName].value = Mm.Validation.convertZen2Han(Mm.MailForm.form[fieldName].value);
			}
		};
		/**
		 * 特殊文字の一部を変換
		 *
		 */
		Obj.convertJIS = function(fieldName) {
			if (Mm.MailForm.form[fieldName]) {
				Mm.MailForm.form[fieldName].value = Mm.Validation.convertJIS(Mm.MailForm.form[fieldName].value);
			}
		};

		window.Mm.MailForm.project = Obj;

	})(jQuery);


	// PROCESS
	jQuery(document).ready(function() {
		Mm.MailForm.project.init();
	});


} else {
	alert('必要なライブラリが読み込まれていません。');
}


/* Common */
/**
 * Debug
 *
 */
function log() {
	if(typeof console == "undefined") return;
	var args = jQuery.makeArray(arguments);
	console.log.apply(console, args);
}

//# sourceMappingURL=genera-form.min.js.map
