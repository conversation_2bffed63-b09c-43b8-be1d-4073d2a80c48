{"version": 3, "names": [], "mappings": "", "sources": ["contact/mm.controller.js"], "sourcesContent": ["var Mm = Mm || {};\n\n(function($) {\n\n\tvar Obj = {};\n\n\t/* 設定用変数\n\t ------------------------------------------------------------------------------------------- */\n\t// チェック開始のイベント\n\tObj.VALIDATE_EVENT = 'change';\n\t// Obj.VALIDATE_EVENT = 'keyup';\n\n\n\t/* 各種変数\n\t ------------------------------------------------------------------------------------------- */\n\t// チェック対象のformオブジェクト（jQueryオブジェクトではないので注意）\n\tObj.form  = false;\n\n\t// チェックルールの定義を格納する変数\n\t// ※ php側（Entry.php）で設定されているチェックルールを使用する\n\tObj.validates = false;\n\tObj.hasError  = false;\n\n\tObj.isValidOnce = false;\n\n\t// フォームからの離脱チェックをするかどうか\n\tObj.isUnloadCheck = true;\n\n\t// 各フォームフィールドを格納する変数\n\t// ※ 入力フィールドのname属性に関して\n\t//    input[type=radio]以外、同名のものはセットされていないことを想定\n\tObj.submits     = []; // 指定されたform内にあるsubmitボタンを格納する配列\n\tObj.fieldGroups = {}; // 指定されたform内にあるフィールドをグループ化したものを格納する連想配列（radioボタン対応）\n\n\tObj.options = {\n\t\tsubmitDisable      : true,  // submitボタンのdisabledの制御をするか\n\t\terrorMove          : true,  // submit時にエラーがある最初の項目位置へ遷移するか\n\t\terrorMoveFunc      : null,  // submit時にエラーがある最初の項目位置へ移動するための関数\n\t\terrorShowAnimation : false, // エラー表示時にアニメーション表示するか\n\t\thintShowAnimation  : false, // ヒント表示時にアニメーション表示するか\n\t\tcls: {\n\t\t\terror: '_error',        // チェックError時に付与するclass名\n\t\t\tsuccess: '_success'     // チェックSuccess時に付与するclass名\n\t\t},\n\t\tisUnloadCheck : false,      // 離脱時にメッセージを表示するか\n\t\tunloadMessage : ''          // 離脱時のメッセージ\n\t};\n\n\t/**\n\t * 初期設定\n\t *\n\t */\n\tObj.init = function(formName, params) {\n\n\t\tvar $form = $(formName);\n\n\t\tObj.form  = $form.get(0);\n\n\t\tif (params) {\n\t\t\tObj.options = $.extend(Obj.options, params);\n\t\t}\n\n\t\t// 設定のチェック - START\n\t\t// formオブジェクトが取得できない場合\n\t\tif ($form.length !== 1) {\n\t\t\twindow.alert('exception occurred. \\n設定されたフォームが存在しません。');\n\t\t\treturn false;\n\t\t}\n\n\t\t// validationのルールが取得できない場合\n\t\tif (!Obj.validates) {\n\t\t\twindow.alert('exception occurred. \\n入力チェック設定が取得できませんでした。');\n\t\t\treturn false;\n\t\t}\n\t\t// 設定のチェック - END\n\n\t\t// ページを離れる前に確認メッセージを表示\n\t\tObj.isUnloadCheck = Obj.options.isUnloadCheck;\n\t\t$(window).on('beforeunload', function() {\n\t\t\t// フラグが立っている場合、エラーメッセージを表示\n\t\t\tif (Obj.isUnloadCheck) {\n\t\t\t\treturn Obj.options.unloadMessage;\n\t\t\t}\n\t\t});\n\n\t\t// formオブジェクトの初期化\n\t\tObj.setup(Obj.form);\n\n\t\t// 初期チェックの実行\n\t\tObj.validate(false);\n\n\t};\n\n\t/**\n\t * 各フォームフィールドの分類とイベント設定\n\t *\n\t */\n\tObj.setup = function(form) {\n\n\t\tvar elemType  = '';\n\t\tvar elemName  = '';\n\t\tvar fieldType = '';\n\n\t\t// フォーム内の入力フィールドの分類を行う\n\t\tfor(var i=0; i<form.elements.length; i++) {\n\n\t\t\telemType  = (form.elements[i].nodeName + '').toLowerCase();\n\t\t\telemName  = form.elements[i].name;\n\t\t\tfieldType = '';\n\n\t\t\t// 取得したフォーム内の入力フィールドの種類による分岐\n\t\t\tswitch(elemType) {\n\t\t\t\t// <input>タグの場合\n\t\t\t\tcase 'input':\n\t\t\t\t\tfieldType = (form.elements[i].type + '').toLowerCase();\n\t\t\t\t\tswitch(fieldType) {\n\t\t\t\t\t\tcase 'submit':\n\t\t\t\t\t\tcase 'image':\n\t\t\t\t\t\t\t// submit or imageの場合、両方ともsubmitボタン扱い\n\t\t\t\t\t\t\tfieldType = 'submit';\n\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\tcase 'radio':\n\t\t\t\t\t\tcase 'checkbox':\n\t\t\t\t\t\tcase 'hidden':\n\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\t// type=text、以外に指定なし、email、phoneなどもtextと同じ扱いとする\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\tfieldType = 'text';\n\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\n\t\t\t\t// <textarea>タグの場合\n\t\t\t\tcase 'textarea':\n\t\t\t\t\tfieldType = elemType;\n\t\t\t\t\tbreak;\n\n\t\t\t\t// <select>タグの場合\n\t\t\t\tcase 'select':\n\t\t\t\t\tfieldType = elemType;\n\t\t\t\t\tbreak;\n\n\t\t\t\t// <button>タグの場合（type=\"submit\"なら、input:submit / input:imageと同じ扱い）\n\t\t\t\tcase 'button':\n\t\t\t\t\tfieldType = (form.elements[i].type + '').toLowerCase();\n\n\t\t\t\t\tswitch(fieldType) {\n\t\t\t\t\t\tcase 'reset':\n\t\t\t\t\t\tcase 'button':\n\t\t\t\t\t\t\t// reset or button の場合は処理なし\n\t\t\t\t\t\t\tbreak;\n\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t// submit or default(指定なし) の場合、両方ともsubmitボタン扱い\n\t\t\t\t\t\t\tfieldType = 'submit';\n\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\n\t\t\t\t\tbreak;\n\t\t\t\tdefault:\n\n\t\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\t// fieldTypeに値がセットされている場合\n\t\t\tif (fieldType) {\n\t\t\t\t// checkboxの場合\n\t\t\t\tif (fieldType === 'checkbox') {\n\t\t\t\t\t// elemNameに\"[]\"がある場合（複数選択）\n\t\t\t\t\tif (elemName.indexOf('[]') >= 0) {\n\t\t\t\t\t\tfieldType = 'checkbox-multi';\n\t\t\t\t\t}\n\t\t\t\t}\n\n\n\t\t\t\t// submitボタンの場合\n\t\t\t\tif (fieldType === 'submit') {\n\t\t\t\t\tObj.initSubmit(form.elements[i]);\n\n\t\t\t\t// hiddenフィールドの場合\n\t\t\t\t// } else if (fieldType !== 'hidden') {\n\t\t\t\t} else {\n\t\t\t\t\tObj.initElem(form.elements[i], fieldType);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t};\n\n\n\t/**\n\t * 入力フィールドの初期化\n\t *\n\t */\n\tObj.initElem = function(elem, type) {\n\t\tvar $elem    = $(elem);\n\t\tvar elemName = $elem.attr('name');\n\n\t\tvar targetID = $elem.data('target-id');\n\t\tvar errorID  = 'data-error-' + targetID;\n\t\tvar hintID   = 'data-hint-' + targetID;\n\t\tvar resultID = 'data-result-' + targetID;\n\n\t\t// フィールドグループをセット\n\t\t// name属性が同じものは１つのフィールドタイプになっていることが前提\n\t\tif (!Obj.fieldGroups[elemName]) {\n\t\t\tObj.fieldGroups[elemName] = {\n\t\t\t\t'type' : type,\n\t\t\t\t'errorID' : errorID,\n\t\t\t\t'resultID' : resultID,\n\t\t\t\t'hasError' : false\n\t\t\t};\n\t\t}\n\n\t\t// active時のhint表示制御\n\t\tvar $hintBox = false;\n\t\tif (hintID) {\n\t\t\t$hintBox = $('#' + hintID);\n\t\t}\n\n\t\t// focus（active）時のイベント\n\t\t$elem.on('focus', function() {\n\t\t\t// ヒントの表示\n\t\t\t_showHint();\n\t\t});\n\n\t\t// click（active）時のイベント\n\t\t$elem.on('click', function() {\n\t\t\t// ヒントの表示\n\t\t\t_showHint();\n\t\t});\n\n\t\t// blur（非active）時のイベント\n\t\t$elem.on('blur', function() {\n\t\t\t// ヒントの非表示\n\t\t\t_hideHint();\n\t\t});\n\n\t\t// inputのテキスト入力フィールド（type=text,email,phoneなど）やradio、checkboxの場合、\n\t\t// enterキーでフォームが送信されるのを抑制する\n\t\tif ((type == 'text') || (type == 'radio') || (type == 'checkbox') || (type == 'select')) {\n\t\t\t$elem.on('keypress', function(e) {\n\t\t\t\tif ((e.which && e.which === 13) || (e.keyCode && e.keyCode === 13)) {\n\t\t\t\t\treturn false;\n\t\t\t\t} else {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\n\t\t// change 時のイベント\n\t\t$elem.on(Obj.VALIDATE_EVENT, function() {\n\n\t\t\t// 拡張機能（引数optionで渡された項目ごとのvalidate実行前関数の実行）\n\t\t\tif (Obj.options['func'] && Obj.options['func'][elemName] && Obj.options['func'][elemName]['before']) {\n\t\t\t\tif (typeof(Obj.options['func'][elemName]['before']) === 'function') {\n\t\t\t\t\tObj.options['func'][elemName]['before']();\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// validateの実行\n\t\t\tObj.validate(true, elemName);\n\n\t\t\t// 拡張機能（引数optionで渡された項目ごとのvalidate実行後関数の実行）\n\t\t\tif (Obj.options['func'] && Obj.options['func'][elemName] && Obj.options['func'][elemName]['after']) {\n\t\t\t\tif (typeof(Obj.options['func'][elemName]['after']) === 'function') {\n\t\t\t\t\tObj.options['func'][elemName]['after']();\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\n\t\t/**\n\t\t * ヒントの表示\n\t\t *\n\t\t */\n\t\tfunction _showHint() {\n\t\t\tif ($hintBox && ($hintBox.length > 0) && !$hintBox.hasClass('open')) {\n\t\t\t\tif (Obj.options.hintShowAnimation) {\n\t\t\t\t\t$hintBox.slideDown(50).addClass('open');\n\t\t\t\t} else {\n\t\t\t\t\t$hintBox.show().addClass('open');\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * ヒントの非表示\n\t\t *\n\t\t */\n\t\tfunction _hideHint() {\n\t\t\tif ($hintBox && ($hintBox.length > 0)) {\n\t\t\t\t$hintBox.hide().removeClass('open');\n\t\t\t}\n\t\t}\n\n\t};\n\n\n\t/**\n\t * submitボタンの初期化\n\t *\n\t */\n\tObj.initSubmit = function(elem) {\n\t\tvar $elem = $(elem);\n\t\tvar elemName = $elem.attr('name');\n\n\t\tObj.submits.push({\n\t\t\t'name': elemName,\n\t\t\t'elem': $elem\n\t\t});\n\n\t\tObj.form.onsubmit = function() {\n\t\t\tObj.validate(true);\n\n\t\t\t// Errorがある場合の自動スクロール\n\t\t\tif (Obj.hasError && Obj.options.errorMove) {\n\n\t\t\t\tfor(var fieldName in Obj.fieldGroups) {\n\t\t\t\t\tif (Obj.fieldGroups[fieldName].hasError) {\n\n\t\t\t\t\t\tif (Obj.options.errorMoveFunc && (typeof(Obj.options.errorMoveFunc) == 'function')) {\n\t\t\t\t\t\t\tObj.options.errorMoveFunc(fieldName, Obj.fieldGroups[fieldName]);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tvar $target = $('#' + Obj.fieldGroups[fieldName].errorID);\n\t\t\t\t\t\t\tif ($target.length) {\n\t\t\t\t\t\t\t\t$('html,body').animate({scrollTop: ($target.parent().offset().top - 20)}, 200);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\nconsole.log('-- debug --');\nconsole.log(Obj.fieldGroups);\n\n\t\t\t// エラーがない場合、離脱チェックフラグをOFFにする\n\t\t\tif (!Obj.hasError) {\n\t\t\t\tObj.isUnloadCheck = false;\n\t\t\t}\n\n\t\t\treturn Obj.hasError?false:true;\n\t\t};\n\n\t\tif (Obj.options.submitDisable) {\n\t\t\t$elem.attr('disabled', 'disabled');\n\t\t}\n\t};\n\n\n\t/**\n\t * 入力チェックの実行\n\t * ※全項目対象\n\t * ※エラーの表示／非表示制御\n\t * ※チェック結果の表示／非表示制御\n\t */\n\tObj.validate = function(showError, elemName) {\n\n\t\tObj.hasError  = false;\n\n\t\t// field loop\n\t\tfor(var fieldName in Obj.fieldGroups) {\n\n\t\t\t// 入力チェックの実行\n\t\t\tObj.validateAt(showError, fieldName);\n\n\t\t\t// activeになっている項目より後の項目は、現在のエラー状態から変更しないため、フラグを下ろす\n\t\t\t// 拡張機能（一度、チェックOKになった場合は、全チェックを行う）\n\t\t\t// if (fieldName === elemName) {\n\t\t\tif (!Obj.isValidOnce && (fieldName === elemName)) {\n\t\t\t\tshowError = false;\n\t\t\t}\n\t\t}\n\n\t\t// submitボタンの表示制御（エラーありの場合disabledを設定する）\n\t\tObj.judgeSubmit();\n\n\t\t// 拡張機能（一度、チェックOKになった場合、フラグを立てる）\n\t\t// ※ フォーム内の選択肢により、チェックが切り替わる場合、このフラグをみて再チェックする必要あり\n\t\t//    （必須→任意のような切り替えがあった場合、エラーメッセージ・フラグ解除がされないため）\n\t\tif (!Obj.hasError) {\n\t\t\tObj.isValidOnce = true;\n\t\t}\n\n\t};\n\n\t/**\n\t * submitボタンの有効／無効切り替え\n\t *\n\t */\n\tObj.judgeSubmit = function() {\n\n\n\t\tif (Obj.options.submitDisable) {\n\t\t\t// submitボタンの表示制御（エラーありの場合disabledを設定する）\n\t\t\tvar disabled = Obj.hasError?'disabled':null;\n\t\t\tfor(var i=0, imax=Obj.submits.length; i<imax; i++) {\n\t\t\t\tObj.submits[i].elem.attr('disabled', disabled);\n\t\t\t}\n\t\t}\n\n\t};\n\n\t/**\n\t * １項目の入力チェック\n\t *\n\t */\n\tObj.validateAt = function(showError, fieldName) {\n\n\t\tvar hasError = true;\n\n\t\t// checkbox（multiの場合の処理）\n\t\tvar checkFieldName = fieldName;\n\t\tif (Obj.fieldGroups[checkFieldName].type === 'checkbox-multi') {\n\t\t\tcheckFieldName = checkFieldName.replace(/\\[|\\]/g, \"\");\n\t\t}\n\n\t\t// ルールが存在している場合\n\t\tif (Obj.validates[checkFieldName]) {\n\t\t\tvar field = Obj.form[fieldName];\n\t\t\tvar rules = Obj.validates[checkFieldName];\n\n\n\t\t\t// 各フィールドの入力値・選択値の取得\n\t\t\tvar fieldValue = Obj.getValue(Obj.fieldGroups[fieldName].type, field);\n\n\t\t\t// ルールの数分LOOP\n\t\t\tvar errors = [];\n\t\t\tvar isInvalid = false;\n\t\t\tvar hasError = false;\n\t\t\tfor(var i=0,imax=rules.length; i<imax; i++) {\n\t\t\t\t// チェックNGの場合、trueがかえってくる\n\t\t\t\tisInvalid = Mm.Validation.check(rules[i].rule, fieldValue, fieldName, rules[i].option, Obj.form);\n\n\t\t\t\t// チェックエラーの場合\n\t\t\t\tif (isInvalid) {\n\t\t\t\t\t// エラーメッセージ格納用配列にメッセージをセット\n\t\t\t\t\terrors.push(rules[i].message);\n\t\t\t\t\t// フラグを立てる\n\t\t\t\t\thasError = true;\n\t\t\t\t\t// グローバル変数のフラグを立てる\n\t\t\t\t\tObj.hasError = true;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// チェック結果による制御\n\t\t\t// errorの表示用オブジェクトの取得\n\t\t\t// ※error表示用のboxがある場合、かつエラー表示対象の場合\n\t\t\t// ※エラー表示対象かどうかは、activeになっている項目以前の項目かどうか\n\t\t\tvar errorID = Obj.fieldGroups[fieldName].errorID;\n\t\t\tvar $error  = $('#' + errorID);\n\n\t\t\t// チェック結果表示用オブジェクトの取得\n\t\t\tvar resultID = Obj.fieldGroups[fieldName].resultID;\n\t\t\tvar $result = $('#' + resultID);\n\n\t\t\t// エラーありの場合\n\t\t\tif (hasError) {\n\t\t\t\tObj.fieldGroups[fieldName].hasError = true;\n\n\t\t\t\t// エラー表示\n\t\t\t\tif (($error.length > 0) && showError) {\n\t\t\t\t\t$error.html(errors.join('<br>'));\n\n\t\t\t\t\tif (Obj.options.errorShowAnimation) {\n\t\t\t\t\t\t$error.slideDown(50);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t$error.show();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// チェック結果をNGに変更\n\t\t\t\tif (($result.length > 0) && showError) {\n\t\t\t\t\t$result.addClass(Obj.options.cls.error).removeClass(Obj.options.cls.success).show();\n\t\t\t\t}\n\n\t\t\t// エラーなしの場合\n\t\t\t} else {\n\t\t\t\tObj.fieldGroups[fieldName].hasError = false;\n\n\t\t\t\t// エラー非表示\n\t\t\t\tif (($error.length > 0) && showError) {\n\t\t\t\t\t$error.hide();\n\t\t\t\t}\n\t\t\t\t// チェック結果をOKに変更\n\t\t\t\tif (($result.length > 0) && showError) {\n\t\t\t\t\t$result.addClass(Obj.options.cls.success).removeClass(Obj.options.cls.error).show();\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn hasError;\n\t};\n\n\tObj.getValue = function(type, field) {\n\n\t\tvar result = null;\n\n\t\t// selectbox\n\t\tif (type == 'select') {\n\t\t\tvar options = field.options;\n\t\t\tif(options.length){\n\t\t\t\tfor(var j=0, jmax=options.length; j<jmax; j++){\n\t\t\t\t\tif(options[j].selected) {\n\t\t\t\t\t\tresult = options[j].value;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t// checkbox or radio\n\t\t} else if ((type == 'checkbox') || (type == 'radio')) {\n\t\t\tif (field.length) {\n\t\t\t\tfor(var j=0, jmax=field.length; j<jmax; j++){\n\t\t\t\t\tif(field[j].checked) {\n\t\t\t\t\t\tresult = field[j].value;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (field.checked) {\n\t\t\t\t\tresult = field.value;\n\t\t\t\t}\n\t\t\t}\n\n\t\t// checkbox(multi)\n\t\t} else if ((type == 'checkbox-multi')) {\n\t\t\tif (field.length) {\n\t\t\t\tresult = [];\n\t\t\t\tfor(var j=0, jmax=field.length; j<jmax; j++){\n\t\t\t\t\tif(field[j].checked) {\n\t\t\t\t\t\tresult.push(field[j].value);\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\n\t\t} else {\n\t\t\tresult = field.value;\n\t\t}\n\n\t\treturn result;\n\t}\n\n\tMm.MailForm = Obj;\n\n})(jQuery);\n\n"], "file": "mm.controller.min.js"}