<?php
header("Content-Type:text/html;charset=utf-8"); 
mb_language("uni") ;
mb_internal_encoding("utf-8");
?>
<?php define('SITE_ROOT', substr( $_SERVER["SCRIPT_FILENAME"], 0, - strlen( $_SERVER["SCRIPT_NAME"] ))); ?>
<?php
///// フォーム関連処理 - START /////
/* Step0. 設定ファイルの読み込み
 --------------------------------------------- */
require_once(realpath(dirname(__FILE__) . '/_app/') . '/config.php');

/* Step1. 初期処理
 --------------------------------------------- */
// Sessionの初期化
session_name(APP_SESSION_NAME);
session_start();

/* Step2. 主処理（アクション別の処理）
 --------------------------------------------- */
$entry = new Entry($_MAIL_VARS);

// 送信処理
if (isset($_POST['b_send']) || isset($_POST['b_send_x'])) {

	// データの初期化
	$entry->setData($_POST);

	// 入力チェック
	if ($entry->validates()) {

		// メール本文用の入力項目配列
		$mail_fields = $entry->getMailFields();

		// メール設定の取得
		$mail_config = $_MAIL_CONFIG[APP_ENV];
		$mail_vars   = $mail_config['server'];



		// ユーザー／管理者別のメール内容の取得
		$user_mail_vars  = false;
		$admin_mail_vars = false;

		// ユーザーの選択肢により、メール情報を切り替える場合の処理
		if (($_MAIL_CONFIG_EXTEND === true) && $entry->getValue($_MAIL_CONFIG_EXTEND_FIELD)) {
			// メール内容を切り替える項目値の取得
			$tmp_mail_type = $entry->getValue($_MAIL_CONFIG_EXTEND_FIELD);

			// ユーザー宛メール設定の取得
			$user_mail_vars   = $_MAIL_CONTENT[$tmp_mail_type]['user'];
			// 管理者宛メール設定の取得
			$admin_mail_vars  = $_MAIL_CONTENT[$tmp_mail_type]['admin'];

		// 振り分けなしの場合
		} else {
			// ユーザー宛メール設定の取得
			$user_mail_vars  = $_MAIL_CONTENT['user'];
			// 管理者宛メール設定の取得
			$admin_mail_vars = $_MAIL_CONTENT['admin'];

		}

		/* ユーザ宛メール送信処理
		 ----------------------------------------- */
		$user_mail = new MailSender($mail_vars);

		// Fromメールアドレス
		$user_mail_from_address = array($user_mail_vars['from'], $user_mail_vars['from_name']);
		// Toメールアドレス
		$user_mail_to_address = array(
			array($entry->getValue('email'), $entry->getValue('name'))
		);

		// 件名
		$user_mail_subject = $user_mail_vars['subject'];
		// 本文
		$user_mail_body = $user_mail->getMailContent(APP_TMPL_PATH . '/mail/user.txt', $mail_fields);

		// 送信処理
		if ($error = $user_mail->send($user_mail_from_address, $user_mail_to_address, $user_mail_subject, $user_mail_body)) {
			Log::error('USER MAIL COULD NOT SENT.', __METHOD__);
			Log::error($error);
		}

		/* 管理者宛メール送信処理
		 ----------------------------------------- */
		$admin_mail = new MailSender($mail_vars);

		// Fromメールアドレス
		$admin_mail_from_address = array($admin_mail_vars['from'], $admin_mail_vars['from_name']);
		// Toメールアドレス
		$admin_mail_to_address = $admin_mail_vars['to'];
		// 件名
		$admin_mail_subject = $admin_mail_vars['subject'];
		// 本文
		$admin_mail_body = $admin_mail->getMailContent(APP_TMPL_PATH . '/mail/admin.txt', $mail_fields);

		// 送信処理
		if ($error = $admin_mail->send($admin_mail_from_address, $admin_mail_to_address, $admin_mail_subject, $admin_mail_body)) {
			Log::error('ADMIN MAIL COULD NOT SENT.', __METHOD__);
			Log::error($error);
		}

		// Sessionのクリア
		$_SESSION[APP_SESSION_KEY] = null;
		unset($_SESSION[APP_SESSION_KEY]);

		// 完了画面へリダイレクト
		@header('Location: ' . APP_URL_ENTRY_COMPLETE);
		exit;

	} else {
		// Sessionのクリア
		$_SESSION[APP_SESSION_KEY] = null;
		unset($_SESSION[APP_SESSION_KEY]);

		// FormUtil::log('Invalid Access or Exception.');

		// 入力画面へリダイレクト
		@header('Location: ' . APP_URL_ENTRY_INPUT);
		exit;

	}


// 戻る処理
} else if (isset($_POST['b_back']) || isset($_POST['b_back_x'])) {
	// データの初期化
	$entry->setData($_POST);

	// Sessionに入力データを登録
	$_SESSION[APP_SESSION_KEY] = $entry->getData();
	$_SESSION[APP_SESSION_KEY]['b_back'] = true;

	// 入力画面へリダイレクト
	@header('Location: ' . APP_URL_ENTRY_INPUT);
	exit;


// Sessionデータが存在する場合（確認画面初期化）
} else if (isset($_SESSION[APP_SESSION_KEY])) {
	// データの初期化
	$entry->setData($_SESSION[APP_SESSION_KEY]);


// Sessionデータが存在しない場合(不正アクセス)
} else {
	// 入力画面へリダイレクト
	@header('Location: ' . APP_URL_ENTRY_EXCEPTION);
	exit;

}
///// フォーム関連処理 - END /////

// [START] ページごとに設定
$_PAGETITLE = 'Contact Us  |  Kowa Self Care International - Kowa Company, Ltd.';
$_DESCRIPTION = 'This page shows how to contact Kowa Company, Ltd.';
$_GLOBAL_LANG = 'hkg';
// [END] ページごとに設定
?>
<!DOCTYPE html>
<html lang="en">
<head>
<?php include (SITE_ROOT . '/_include/common/head_common.php');?>
</head>

<body class="is-contact">
<div class="l_wrap" id="pageTop">
	<?php include (SITE_ROOT . '/'.$_GLOBAL_LANG.'/_include/common/header.php');?>

	<main class="l_main">
		
		<section class="uq_contact--section">
			<div class="uq_contact--bg">
				<div class="uq_contact__inner">
					<div class="uq_contact--head">
						<div class="uq_contact--title">
							<h1>聯絡我們</h1>
						</div>
						<div class="uq_contact-nav">
							<ol class="uq_contact-nav__list">
								<li class="uq_contact-nav__item">輸入</li>
								<li class="uq_contact-nav__item is-act">確認</li>
								<li class="uq_contact-nav__item">送出</li>
							</ol>
						</div>
					</div>
				</div>
			</div>

			<div class="uq_contact--form">
				<div class="uq_contact__inner">
					<form name="frm" method="post" id="js-form-input" novalidate>

					<dl class="uq_contact-col">
						<dt class="uq_contact-col__label">姓名</dt>
						<dd class="uq_contact-col__body">
							<p class="form-input"><?php echo $entry->getValue('name'); ?></p>
						</dd>
					</dl>
					<dl class="uq_contact-col">
						<dt class="uq_contact-col__label">電子郵件</dt>
						<dd class="uq_contact-col__body">
							<p class="form-input"><?php echo $entry->getValue('email'); ?></p>
						</dd>
					</dl>
					<dl class="uq_contact-col">
						<dt class="uq_contact-col__label">組織名稱</dt>
						<dd class="uq_contact-col__body">
							<p class="form-input"><?php echo $entry->getValue('company'); ?></p>
						</dd>
					</dl>
					<dl class="uq_contact-col">
						<dt class="uq_contact-col__label">咨询内容</dt>
						<dd class="uq_contact-col__body">
							<p class="form-input"><?php echo nl2br($entry->getValue('contents')); ?></p>
						</dd>
					</dl>

					<div class="uq_contact-btn">
						<div class="uq_contact-btn_inner">
							<ul class="uq_contact-btn-col">
								<li><p class="uq_contact-btn-col_btn is-cancell"><input type="submit" name="b_back" value="取消"></p></li>
								<li><p class="uq_contact-btn-col_btn"><input type="submit" name="b_send" value="送出"></p></li>
							</ul>
						</div>
					</div>

					<?php echo $entry->showHidden(); ?>
				</form>
				</div>
			</div>
			
		</section>
	</main>

	
	<?php include (SITE_ROOT . '/'.$_GLOBAL_LANG.'/_include/common/footer.php');?>
</div>
<!-- //.l_wrap -->

<?php include (SITE_ROOT . '/_include/common/script.php');?>


</body>
</html>