<?php
mb_internal_encoding("UTF-8");
require_once(dirname(dirname(__FILE__)) . '/libs/phpmailer/PHPMailerAutoload.php');

class MailSender {

	protected $settings = false;
	protected $mail = false;

	/* メール用関数
	 --------------------------------------------*/
	public function __construct($settings) {
		$this->settings = $settings;

		// Create a new PHPMailer instance
		// $this->mail = new PHPMailer(true);
		$this->mail = new PHPMailer();
		if (array_key_exists('is_smtp', $this->settings) && $this->settings['is_smtp'] && $this->settings['smtp']) {
			$this->mail->isSMTP(); // Tell PHPMailer to use SMTP
			// $mail->SMTPDebug = 2; //Enable SMTP debugging
			// $mail->Debugoutput = 'html'; //Ask for HTML-friendly debug output

			$this->mail->Host = $this->settings['smtp']['host']; //Set the hostname of the mail server
			$this->mail->Port = $this->settings['smtp']['port']; // Set the SMTP port number - 587 for authenticated TLS, a.k.a. RFC4409 SMTP submission
			$this->mail->SMTPSecure = $this->settings['smtp']['secure']; //Set the encryption system to use - ssl (deprecated) or tls
			$this->mail->SMTPAuth   = $this->settings['smtp']['auth']; //Whether to use SMTP authentication

			// Account
			$this->mail->Username = $this->settings['smtp']['username'];
			$this->mail->Password = $this->settings['smtp']['password'];

		}

		if (array_key_exists('is_html', $this->settings) && $this->settings['is_html']) {
			$this->settings['is_html'] = true;
			$this->mail->isHTML(true);
		} else {
			$this->settings['is_html'] = false;
			$this->mail->isHTML(false);
		}

		// ISO-2022-JP の場合
		//$this->mail->Encoding = "7bit";
		//$this->mail->CharSet  = 'ISO-2022-JP';

		// UTF-8 の場合
		// $mail->CharSet = 'UTF-8';
		$this->mail->CharSet = 'UTF-8';
	}

	/**
	 *
	 *
	 */
	public function send($from_address, $to_address_list, $subject, $body) {
		$error = false;

		// 送信者
		// $mail->setFrom($from_email, 'sender');
		list($from_email, $from_name) = $from_address;
		$this->mail->From   = $from_email;
		$this->mail->Sender = $from_email;
		// $this->mail->FromName = mb_convert_encoding($from_name, "JIS", "UTF-8");
		// $this->mail->FromName = $from_name;
		if ($from_name) {
			$this->mail->FromName = $from_name;
		} else {
			$this->mail->FromName = null;
		}

		if (is_array($to_address_list)) {
			foreach($to_address_list as $to_address) {
				if (is_array($to_address)) {
					list($to_mail, $to_name) = $to_address;
					$this->mail->addAddress($to_mail, $to_name);
				} else {
					$to_mail = $to_address;
					$this->mail->addAddress($to_mail);
				}

			}
		}

		// 件名
		$this->mail->Subject = $subject;
		// 本文
		$this->mail->Body    = $body;
		//$this->mail->CharSet = 'UTF-8';
		// メール送信送信処理
		if (!$this->mail->send()) {
		    $error = $this->mail->ErrorInfo;
		}

		return $error;
	}


	/**
	 * メール本文の取得
	 *
	 */
	public function getMailContent($template_path, $fields) {
		$template = file_get_contents($template_path);
		$result   = $template;

		foreach($fields as $key => $val) {
			if (is_string($val)) {
				$result = str_replace('$$'.$key.'$$', $val, $result);
			}
		}

		return $result;
	}


}
