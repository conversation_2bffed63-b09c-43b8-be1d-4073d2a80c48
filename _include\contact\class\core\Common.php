<?php

class Common {

	/**
	 * HTTPSかどうかの判断（AWS対応）
	 *
	 * @return bool
	 */
	public function is_ssl() {
		// Apache
		if ( isset($_SERVER['HTTPS']) === true ) {
			return ( $_SERVER['HTTPS'] === 'on' or $_SERVER['HTTPS'] === '1' );
		// IIS
		} else if ( isset($_SERVER['SSL']) === true ) {
			return ( $_SERVER['SSL'] === 'on' );

		// Reverse proxy
		} else if ( isset($_SERVER['HTTP_X_FORWARDED_PROTO']) === true ) {
			return ( strtolower($_SERVER['HTTP_X_FORWARDED_PROTO']) === 'https' );

		// Reverse proxy
		} elseif ( isset($_SERVER['HTTP_X_FORWARDED_PORT']) === true ) {
			return ( $_SERVER['HTTP_X_FORWARDED_PORT'] === '443' );

		// All
		} elseif ( isset($_SERVER['SERVER_PORT']) === true ) {
			return ( $_SERVER['SERVER_PORT'] === '443' );
		}

		return false;
	}

	/**
	 * Debug用 print_r 短縮版
	 *
	 */
	public static function pr($obj) {
		if ($obj) {
			echo '<pre style="margin: 1.0em 0; padding: 1.0em; background-color: #eee; border-color: #ccc;">';
			print_r($obj);
			echo '</pre>';
		}
	}

	/**
	* 指定されたパスのXMLを取得し、配列に変換する
	*
	*/
	public static function get_xml($src) {
		$content = self::get_contents($src);

		if ($content !== false) {
			$xml = str_replace(0xefbbbf, '', $content);

			// $obj = simplexml_load_string($xml);
			$obj = simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA);

			return $obj;
		} else {
			return false;
		}
	}

	/**
	* 指定されたパスのXMLを取得し、配列に変換する
	*
	*/
	public static function get_xml2array($src){

		$obj = self::get_xml($src);

		if ($obj !== false) {

			// return json_decode(json_encode($obj), true);
			return self::parse_xml($obj);

		}

		return false;
	}

	/**
	 *
	 *
	 */
	public static function parse_xml(SimpleXMLElement $element) {
        $namespace = $element->getDocNamespaces(true);
        $namespace[NULL] = NULL;

        $result = array();

        $children   = array();
        $attributes = array();
        $name       = strtolower((string)$element->getName());

        $value = trim((string)$element);
        if( strlen($value) <= 0 ) {
            $value = NULL;
        }


        // get info for all namespaces
        if(is_object($element)) {

            foreach( $namespace as $ns=>$nsUrl ) {
                // atributes
                $elementAttributes = $element->attributes($ns, true);
                foreach( $elementAttributes as $attributeName => $attributeValue ) {
                    $attribName = strtolower(trim((string)$attributeName));
                    $attribVal = trim((string)$attributeValue);
                    if (!empty($ns)) {
                        $attribName = $ns . ':' . $attribName;
                    }
                    $attributes[$attribName] = $attribVal;
                }

                // children
                $elementChildren = $element->children($ns, true);
                foreach( $elementChildren as $childName=>$child ) {
                    $childName = strtolower((string)$childName);
                    if( !empty($ns) ) {
                        $childName = $ns.':'.$childName;
                    }
                    // $children[$childName][] = self::parse_xml($child);

					$result[$childName][] = self::parse_xml($child);
                }
            }

        }

        $result['@value'] = $value;
        $result['@attributes'] = $attributes;

		return $result;

    }

	/**
	 * file_get_contentsのエラーハンドリング版
	 *
	 */
	public static function get_contents($request_url) {

	    $context = stream_context_create(array(
	      'http' => array('ignore_errors' => true)
	    ));
	    $code = file_get_contents($request_url, false, $context);


		$is_success = strpos($http_response_header[0], '200');

		if ($is_success === false) {
			// Exception処理
			// throw new Exception('exception');
			return false;
	    }

	    return $code;
	}


	/**
	 * Trim
	 *
	 */
	public static function mb_trim($str) {
		static $chars = "[\\x0-\x20\x7f\xc2\xa0\xe3\x80\x80]";
		return preg_replace("/\A{$chars}++|{$chars}++\z/u", '', $str);
	}

	/**
	 * 通常配列か連想配列かのチェック
	 *
	 * @return 通常配列の場合true
	 */
	public static function is_normal_array(array $arr) {
		return array_values($arr) === $arr;
	}

	/**
	 * 有効な通常配列かのチェック
	 *
	 * @return 有効な場合true
	 */
	public static function is_valid_normal_array($arr) {

		return (is_array($arr) && self::is_normal_array($arr) && (count($arr) > 0))?true:false;
	}

	/**
	 * 有効な連想配列かのチェック
	 *
	 * @return 有効な場合true
	 */
	public static function is_valid_associative_array($arr) {
		return (is_array($arr) && !self::is_normal_array($arr))?true:false;
	}

	/**
	 * 有効な日付形式の場合、date型に変換
	 *
	 * @return 有効な場合date、無効な場合false
	 */
	public static function convert_ymd($str, $format='Y/m/d') {

		$date = strtotime($str);
		if ($date !== false) {
			return date($format, $date);
		}

		return '';
	}

}

