<?php

class Validate {

	// フィールド
	protected $_field = array();

	// チェックルール
	protected $_validates = array();

	// チェックエラー
	protected $_error = array();

	// バリデーション結果
	protected $is_valid = true;


	/**
	 * default constructor
	 *
	 */
	public function __construct() {
	}

	/**
	 * validates
	 *
	 */
	public function validates($do_set_error=true) {
		$this->is_valid = true;

		// foreach($this->_validates as $key => $check_list) {
		foreach($this->_validates as $key => $check_list) {
			$fld_name  = $key;
			$fld_value = $this->_field[$fld_name];

			// check数分LOOP
			foreach($check_list as $check) {
				$option = isset($check['option'])?$check['option']:null;
				if ($this->{$check['rule']}($fld_value, $fld_name, $option)) {
					$this->is_valid = false;
					if ($do_set_error) {
						$this->_error[$fld_name] = $check['message'];
					}

					break;
				}
			}
		}

		return $this->is_valid;
	}


	/**
	 * validates
	 *
	 */
	public function validate_parts($parts_key, $do_set_error=true) {
		$this->is_valid = true;

		if (array_key_exists($parts_key, $this->_validates)) {
						foreach($this->_validates[$parts_key] as $key => $check_list) {
				$fld_name  = $key;
				$fld_value = $this->_field[$fld_name];

				// check数分LOOP
				foreach($check_list as $check) {
					$option = isset($check['option'])?$check['option']:null;
					if ($this->{$check['rule']}($fld_value, $fld_name, $option)) {
						$this->is_valid = false;
						if ($do_set_error) {
							$this->_error[$fld_name] = $check['message'];
						}

						break;
					}
				}
			}
		}

		return $this->is_valid;
	}



	/**
	 * POSTデータのセット
	 *
	 */
	public function setData($data, $exclude_fields=array()) {

		$has_data = false;

		foreach($this->_field as $key => $val) {
			if (array_key_exists($key, $data)) {
				if (!in_array($key, $exclude_fields)) {
					// if (!is_array($val)) {
					if (is_string($data[$key])) {
						// 半角カタカナはすべて全角カタカナに置換
						$this->_field[$key] = mb_convert_kana($data[$key], "KV", "UTF-8");
						// ㈱を(株)に
						$this->_field[$key] = str_replace(array('㈱','㈲','㈹'), array('(株)','(有)','(代)'), $this->_field[$key]);


						// 電話番号（phone）、ファックス番号（fax）、郵便番号（zipcode）、メールアドレス（mail）は半角文字に変換
						if ((strpos('phone', $key) !== false) || (strpos('fax', $key) !== false) || (strpos('zipcode', $key) !== false) || (strpos('mail', $key) !== false)) {
							$this->_field[$key] = mb_convert_kana($this->_field[$key], 'as', "UTF-8");
						}

						$has_data = true;
					} else {
						$this->_field[$key] = $data[$key];
						$has_data = true;
					}
				} else {
					$this->_field[$key] = $data[$key];
					$has_data = true;
				}
			}
		}

		return $has_data;
	}


	/**
	 * POSTデータの取得
	 *
	 */
	public function getData() {
		return $this->_field;
	}


	/**
	 * 値の取得
	 *
	 */
	public function getValue($key, $b_escape=true) {
		if (isset($this->_field[$key]) && !empty($this->_field[$key])) {
			// 配列の場合あり
			if (is_array($this->_field[$key])) {
				return $this->_field[$key];
			} else {
				if ($b_escape)
					return htmlspecialchars($this->_field[$key]);
				else
					return $this->_field[$key];
			}
		} else {
			return '';
		}
	}

	/**
	 *
	 *
	 */
	public function setValue($key, $val) {
		$this->_field[$key] = $this->space_trim($val);
	}


	/* error関数
	 --------------------------------------------*/
	/**
	 *
	 *
	 */
	public function hasError() {
		return $this->is_valid?false:true;
	}

	/**
	 *
	 *
	 */
	public function hasErrorAt($key) {
		if (isset($this->_error[$key]) && !empty($this->_error[$key])) {
			return true;
		}

		return false;
	}

	/**
	 *
	 *
	 */
	public function getError($key, $b_wrap=true, $add_cls=null, $attributes=false) {
		if (isset($this->_error[$key]) && !empty($this->_error[$key])) {
			if ($b_wrap) {
				// Attributeの取得
				$code_attr = '';
				if ($attributes && is_array($attributes)) {
					foreach($attributes as $attr_key => $attr_val) {
						$code_attr .= ' ' . $attr_key . '="' . $attr_val . '"';
					}
				}

				if (isset($add_cls) && !empty($add_cls))
					return '<p class="error '.$add_cls.'">'.nl2br(htmlspecialchars($this->_error[$key])).'</p>';
				else
					return '<p class="error">'.nl2br(htmlspecialchars($this->_error[$key])).'</p>';
			} else {

				return $this->_error[$key];
			}
		}

		return '';
	}

	/**
	 *
	 *
	 */
	public function showHidden($exclude=array()) {
		$code = $this->getHidden($exclude);

		echo $code;
	}


	/**
	 *
	 *
	 */
	public function getHidden($exclude=array()) {
		$code = '';
		foreach($this->_field as $field_name => $field_value) {
			if (array_key_exists($field_name, $exclude))
				continue;

			$value = $this->getValue($field_name);
			if (is_array($value)) {
				foreach($value as $idx => $value_at) {
					$code_value_at = htmlspecialchars($value_at);
					$code .=<<<CODE
<input type="hidden" name="${field_name}[]" value="${code_value_at}">
CODE;
				}

			} else {
				$code .=<<<CODE
<input type="hidden" name="${field_name}" value="${value}">
CODE;
			}

		}

		return $code;
	}


	/* check関数
	 * チェックNGの場合、true（エラーあり）
	 * チェックOKの場合、false
	 --------------------------------------------*/
	/**
	 * 未入力チェック
	 *
	 */
	protected function is_empty($val, $key=null, $option=null) {

		if (empty($val))
			return true;

		return false;
	}

	/**
	 * 未入力チェック
	 * ※いずれかの項目に値がセットされているかのcheck（すべて未入力の場合エラー）
	 *
	 */
	protected function is_empty_all($val, $key=null, $option=null) {

		if ($option && isset($option['key'])) {
			$check_field_key = $option['key'];
			// option['key']が配列の場合
			if (is_array($check_field_key)) {

				foreach($check_field_key as $unit_field_key) {
					if (!empty($this->_field[$unit_field_key])) {
						return false;
					}
				}

			// option['key']が配列以外の場合
			} else {
				if (!empty($this->_field[$check_field_key]))
					return false;
			}
		}

		if (!empty($val))
			return false;

		return true;
	}

	/**
	 * 未入力チェック
	 * ※すべての項目に値がセットされているかのcheck（１つでも未入力がある場合エラー）
	 *
	 */
	protected function is_empty_any($val, $key=null, $option=null) {

		if ($option && isset($option['key'])) {
			$check_field_key = $option['key'];
			// option['key']が配列の場合
			if (is_array($check_field_key)) {

				foreach($check_field_key as $unit_field_key) {
					if (empty($this->_field[$unit_field_key])) {
						return true;
					}
				}

			// option['key']が配列以外の場合
			} else {
				if (empty($this->_field[$check_field_key]))
					return true;
			}
		}

		if (empty($val))
			return true;

		return false;
	}


	/**
	 * 未入力チェック
	 * ※相関チェック／指定されたフィールドの値が、指定された値の場合のみ必須
	 *
	 */
	protected function is_empty_selected($val, $key=null, $option=null) {

		if ($option && isset($option['key']) &&  isset($option['value'])) {
			$check_field_key   = $option['key'];
			$check_field_value = $option['value'];

			if ($this->_field[$check_field_key] != $check_field_value) {
				return false;
			}
		}

		if (empty($val)) {
			return true;
		}

		return false;
	}

	/**
	 * 全角カタカナチェック
	 *
	 */
	protected function is_not_katakana($val, $key=null, $option=null){
		if ($this->is_empty($val))
			return false;

		if (preg_match("/^[ァ-ヶー・　 ]+$/u", $val))
			return false;
		else
			return true;
	}

	/**
	 * 全角ひらがなチェック
	 *
	 */
	protected function is_not_hiragana($val, $key=null, $option=null){
		if ($this->is_empty($val))
			return false;

		if (preg_match("/^[ぁ-んー・　 ]+$/u", $val))
			return false;
		else
			return true;
	}

	/**
	 * 電話番号形式チェック
	 *
	 */
	protected function is_invalid_phone($val, $key=null, $option=null){
		if ($this->is_empty($val))
			return false;

		// if (preg_match('/^[0-9]+[-]?[0-9]+[-]?[0-9]+$/', $val))
		if (preg_match('/^(\+)?([0-9]+[-]?){2,}[0-9]+$/', $val))
			return false;
		else
			return true;
	}

	/**
	 * 電話番号形式チェック（上桁と中桁と下桁で分かれている場合）
	 * ※option['key'] = array('中桁のフィールドキー', '下桁のフィールドキー')の形式で渡されること
	 *
	 */
	protected function is_invalid_phone_separate($val, $key=null, $option=null){

		$phone = $val;

		if ($option && isset($option['key'])) {
			foreach($option['key'] as $field_key) {
				$phone .= '-' . $this->_field[$field_key];
			}
		}

		return $this->is_invalid_phone($phone);
	}

	/**
	 * 郵便番号形式チェック
	 *
	 */
	protected function is_invalid_zipcode($val, $key=null, $option=null){
		if ($this->is_empty($val))
			return false;

		if (preg_match('/^([0-9]+[-]?)[0-9]+$/', $val))
			return false;
		else
			return true;
	}

	/**
	 * 郵便番号形式チェック（上桁と下桁で分かれている場合）
	 * ※option['key'] = '下桁のフィールドキー'の形式で渡されること
	 *
	 */
	protected function is_invalid_zipcode_separate($val, $key=null, $option=null){

		$zipcode = $val . '-';

		if ($option && isset($option['key'])) {
			$field_key = $option['key'];
			$zipcode .= $this->_field[$field_key];
		}

		return $this->is_invalid_zipcode($zipcode);

	}

	/**
	 * 数字チェック
	 *
	 */
	protected function is_invalid_numeric($val, $key=null, $option=null) {
		if ($this->is_empty($val))
			return false;

		if (preg_match('/^[0-9]+$/', $val))
			return false;
		else
			return true;
	}

	/**
	 * 正の整数チェック
	 *
	 */
	protected function is_invalid_positive_integer($val, $key=null, $option=null) {
		if ($this->is_empty($val))
			return false;

		if (preg_match('/^[1-9][0-9]*$/', $val))
			return false;
		else
			return true;
	}

	/**
	 * 英字チェック
	 *
	 */
	protected function is_invalid_alphabet($val, $key=null, $option=null) {
		if ($this->is_empty($val))
			return false;

		if (preg_match('/^[a-zA-Z]+$/', $val))
			return false;
		else
			return true;
	}

	/**
	 * 英字記号チェック
	 *
	 */
	protected function is_invalid_alphabet_ext($val, $key=null, $option=null) {
		if ($this->is_empty($val))
			return false;

		if (preg_match('/^[a-zA-Z!-~ ]+$/', $val))
			return false;
		else
			return true;
	}

	/**
	 * 英数字チェック
	 *
	 */
	protected function is_invalid_alphanum($val, $key=null, $option=null) {
		if ($this->is_empty($val))
			return false;

		if (preg_match('/^[a-zA-Z0-9]+$/', $val))
			return false;
		else
			return true;
	}

	/**
	 * 英数字記号チェック
	 *
	 */
	protected function is_invalid_alphanum_ext($val, $key=null, $option=null) {
		if ($this->is_empty($val))
			return false;

		if (preg_match('/^[a-zA-Z0-9!-~ ]+$/', $val))
			return false;
		else
			return true;
	}

	/**
	 * メールアドレス形式チェック
	 *
	 */
	protected function is_invalid_mailaddress($val, $key=null, $option=null) {
		if ($this->is_empty($val))
			return false;

		if (preg_match('/^[A-Za-z0-9]+[\w\.\+-]+@[\w\.-]{2,}\.\w{2,}$/', $val))
			return false;
		else
			return true;
	}

	/**
	 * 同値チェック
	 * ※option['key'] = '比較対象のフィールド名'の形式で渡されること
	 *
	 */
	protected function is_not_equal($val, $key=null, $option=null) {
		if ($option && isset($option['key'])) {
			$conf_field_key = $option['key'];
			if ($val !== $this->_field[$conf_field_key])
				return true;
		}

		return false;
	}

	/**
	 * 環境依存文字・旧漢字などJISに変換できない文字チェック
	 * 機種依存文字を含む場合、falseを返す（エラー）
	 *
	 */
	protected function is_invalid_jis($val, $key=null, $option=null) {

		if ($this->is_empty($val))
			return false;

		// 対象外の文字列を除外
		$val = str_replace(array('～', 'ー', '－', '∥', '￠', '￡', '￢'), '', $val);
		$val2 = mb_convert_encoding($val, 'iso-2022-jp', 'utf8');
		$val2 = mb_convert_encoding($val2, 'utf8', 'iso-2022-jp');

		return ($val != $val2);
	}

	/**
	 * YYYY-MM-DD形式かチェック
	 *
	 */
	protected function is_invalid_ymd($val, $key=null, $option=null) {

		if ($this->is_empty($val)) {
			return false;
		}
		// 区切り文字の統一（"/" → "-"）
		$tmp = str_replace('/', '-', $val);
		$tmp = explode('-', $tmp);

		// ３分割できない場合はエラー
		if (count($tmp) != 3) {
			return true;

		// 各要素のいずれかが数字形式じゃない場合エラー
		} else if ($this->is_invalid_numeric($tmp[0]) || $this->is_invalid_numeric($tmp[1]) || $this->is_invalid_numeric($tmp[2])) {
			return true;

		}

		$tmp = array_map('intval', $tmp);
		return !checkdate($tmp[1], $tmp[2], $tmp[0]);
	}

	/**
	 * 指定されたフィールド（年・月・日が別のフィールド）が日付形式かのチェック
	 * ※option['y'] = '年のフィールド名'、option['m'] = '月のフィールド名'、option['d'] = '日のフィールド名'、の形式で渡されることが前提
	 *
	 */
	protected function is_invalid_ymd_array($val, $key=null, $option=null) {

		if ($option && isset($option['y']) && isset($option['m']) && isset($option['d'])) {
			$y_field_key = $option['y'];
			$m_field_key = $option['m'];
			$d_field_key = $option['d'];

			// セットされている値が数字かどうかのチェック
			if (!$this->is_invalid_numeric($this->_field[$y_field_key]) && !$this->is_invalid_numeric($this->_field[$m_field_key]) && !$this->is_invalid_numeric($this->_field[$d_field_key])) {
				$y = intval($this->_field[$y_field_key]);
				$m = intval($this->_field[$m_field_key]);
				$d = intval($this->_field[$d_field_key]);

				return !checkdate($m, $d, $y);

			// セットされている値が数字じゃない場合、エラー
			} else {
				return true;

			}
		}

		return true;

	}

	/**
	 * 年月の日付形式
	 *
	 */
	protected function is_invalid_ym($val, $key=null, $option=null) {

		if ($option && isset($option['y']) && isset($option['m'])) {
			// 年
			$y_key = $option['y'];
			$y = $this->_field[$y_key];
			// 月
			$m_key = $option['m'];
			$m = $this->_field[$m_key];

			// 年・月のいずれかに入力がある場合はチェックする
			if (!$this->is_empty($y . $m)) {
				if (!$this->is_invalid_numeric($y) && !$this->is_invalid_numeric($m)) {
					return !checkdate(intval($m), 1, intval($y));
				} else {
					return true;
				}
			}
		}

		return false;
	}

	/**
	 * 最大文字数チェック
	 *
	 */
	protected function is_over_max_length($val, $key=null, $option=null) {

		if ($this->is_empty($val)) {
			return false;
		}

		$length = 0;
		if ($option && isset($option['length'])) {
			$length = $option['length'];
		}

		return (mb_strlen($this->remove_nl($val), 'utf8') > $length);
	}

	/**
	 * 最大文字数チェック（全角を1文字、半角を0.5文字として換算）
	 *
	 */
	protected function is_over_max_length_custom($val, $key=null, $option=null) {

		if ($this->is_empty($val)) {
			return false;
		}

		$length = 0;
		if ($option && isset($option['length'])) {
			$length = $option['length'];
		}

		$conv_length = mb_strwidth($this->remove_nl($val),'UTF-8') / 2;

		return ($conv_length > $length);
	}

	/**
	 * 最大数チェック
	 *
	 */
	protected function is_over_max_num($val, $key=null, $option=null) {

		if ($this->is_empty($val)) {
			return false;
		}

		if ($this->is_invalid_numeric($val)) {
			return false;
		}

		$max = 0;
		if ($option && isset($option['max'])) {
			$max = $option['max'];
		}

		return (intval($val) > $max);
	}

	/**
	 * 最大バイト数チェック
	 *
	 */
	protected function is_over_max_byte($val, $key=null, $option=null) {

		if ($this->is_empty($val)) {
			return false;
		}

		$length = 0;
		if ($option && isset($option['length'])) {
			$length = $option['length'];
		}

		return (strlen($this->remove_nl($val)) > $length);
	}

	/**
	 * 文字数チェック
	 *
	 */
	protected function is_not_equal_length($val, $key=null, $option=null) {

		if ($this->is_empty($val)) {
			return false;
		}

		$length = 0;
		if ($option && isset($option['length'])) {
			$length = $option['length'];
		}

		return (mb_strlen($this->remove_nl($val), 'utf8') != $length);
	}

	/**
	 * 正規表現に合致するかのチェック
	 *
	 */
	protected function is_invalid_pattern($val, $key=null, $option=null) {

		if ($this->is_empty($val)) {
			return false;
		}

		if ($option && isset($option['pattern'])) {

			$flg = isset($option['flg'])?$option['flg']:'';

			if (preg_match('/' . $option['pattern'] . '/' . $flg, $val)) {
				return false;
			} else {
				return true;
			}
		} else {
			return false;
		}

	}

	// 拡張チェック ///////////////////////////////////
	/**
	 * 生年月日のいずれかが未入力
	 *
	 */
	protected function is_birth_empty($val, $key=null, $option=null) {

		if ($this->is_empty($this->_field['birth_y']) || $this->is_empty($this->_field['birth_m']) || $this->is_empty($this->_field['birth_d'])) {
			return true;
		}

		return false;
	}

	/**
	 * 生年月日の日付形式
	 *
	 */
	protected function is_birth_date($val, $key=null, $option=null) {
		if ($this->is_invalid_ymd($this->_field['birth_y'] . '-' . $this->_field['birth_m'] . '-' . $this->_field['birth_d'])) {
			return true;
		}

		return false;
	}

	/**
	 * 指定された配列に要素が存在するかのチェック
	 *
	 */
	protected function is_invalid_select($val, $key=null, $option=null) {
		if (!$this->is_empty($val) && $option && isset($option['list']) && is_array($option['list'])) {
			return !array_key_exists($val, $option['list']);

		}

		return false;
	}

	/**
	 * チェックボックスがチェックされているかどうか
	 *
	 */
	protected function is_not_checked($val, $key=null, $option=null) {

		if (empty($val)) {
			return true;

		} else if (is_array($val)) {
			if (count($val) === 0) {
				return true;
			}
		}

		return false;
	}

	/**
	 * Trim
	 *
	 */
	protected function space_trim($str) {
		$str = preg_replace('/(^\s+)|(\s+$)/us', '', $str);

		return $str;
	}

	/**
	 * 改行削除
	 *
	 */
	protected function remove_nl($str='') {
		return preg_replace('/(?:\n|\r|\r\n)/', '', $str);
	}


}
