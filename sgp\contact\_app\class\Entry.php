<?php
/**
 *
 *
 */

class Entry extends Validate {

	/* 定数（フォーム選択項目）
	 ---------------------------------------------------------------------------------------------- */
	protected $_CONFIG = false;

	/* メンバ変数
	 ---------------------------------------------------------------------------------------------- */
	protected $_field = array(
		'name'                   => '',
		'email'                  => '',
		'email_conf'             => '',
		'company'                => '',
		'contents'               => '',
		'privacy'                => '',
	);


	protected $_validates = array(
		'name' => array(
			array('rule' => 'is_empty', 'message' => 'Please enter your name.'),
			array('rule' => 'is_invalid_jis', 'message' => 'Please enter your name correctly.'),
			array('rule' => 'is_over_max_length', 'message' => 'Up to 50 characters.', 'option' => array('length' => 50)),
		),
		'email' => array(
			array('rule' => 'is_empty', 'message' => 'Please enter your email address.'),
			array('rule' => 'is_invalid_mailaddress', 'message' => 'Please enter your email address correctly.'),
			array('rule' => 'is_over_max_length', 'message' => 'Up to 256 characters.', 'option' => array('length' => 255)),
		),
		'email_conf' => array(
			array('rule' => 'is_empty', 'message' => 'Please enter your email address(Confirmation).'),
			array('rule' => 'is_invalid_mailaddress', 'message' => 'Please enter your email address(Confirmation) correctly.'),
			array('rule' => 'is_over_max_length', 'message' => 'Up to 256 characters.', 'option' => array('length' => 255)),
			array('rule' => 'is_not_equal', 'message' => 'Email address must equal. Please confirm your email address.', 'option' => array('key' => 'email')),
		),
		'contents' => array(
			array('rule' => 'is_empty', 'message' => 'Please enter inquiry.'),
			array('rule' => 'is_invalid_jis', 'message' => 'Please enter inquiry correctly.'),
			array('rule' => 'is_over_max_length', 'message' => 'Up to 1,000 characters.', 'option' => array('length' => 1000)),
		),
		/*'company' => array(
			array('rule' => 'is_invalid_jis', 'message' => 'Please enter your conpany name correctly.'),
			array('rule' => 'is_over_max_length', 'message' => 'Up to 50 characters.', 'option' => array('length' => 50)),
		),*/


		'privacy' => array(
			array('rule' => 'is_empty', 'message' => 'Please select the Agree check box to agree the Privacy Policy.'),
		),
	);

	protected $_message = false;


	/**
	 * default constructor
	 *
	 */
	function __construct($_CONFIG=false) {

		// 定数をセット
		if ($_CONFIG && is_array($_CONFIG)) {
			$this->_CONFIG = array();
			foreach($_CONFIG as $id => $data) {
				$this->_CONFIG[$id] = $data;
			}
		}
	}


	/**
	 * POSTデータのセット(override)
	 *
	 */
	public function setData($data, $exclude_fields=array()) {
		parent::setData($data, $exclude_fields);



		/* 項目の半角変換
		 ----------------------------------------- */

		/* 確認ページ用表示項目の取得
		 ----------------------------------------- */

	}

	/**
	 * メール送信用項目の設定
	 *
	 */
	public function getMailFields() {
		$field = array();

		// 入力項目をコピー
		foreach($this->_field as $field_key => $field_value) {
			$field[$field_key] = $this->getValue($field_key);
		}

		// 確認項目の取得


		// 送信日時・アクセス情報
		$field['sys_timestamp'] = date('Y/m/d H:i:s');
		$field['user_agent']    = isset($_SERVER['HTTP_USER_AGENT'])?$_SERVER['HTTP_USER_AGENT']:'';
		$field['remote_addr']   = isset($_SERVER['REMOTE_ADDR'])?$_SERVER['REMOTE_ADDR']:'';
		$field['remote_host']   = isset($_SERVER['REMOTE_HOST'])?$_SERVER['REMOTE_HOST']:'';

		return $field;
	}

	/**
	 * 定義された配列の取得
	 *
	 */
	public function getDefineList($list_key) {
		$list = false;

		if (array_key_exists($list_key, $this->_CONFIG)) {
			$list = $this->_CONFIG[$list_key];
		}

		return $list;

	}


	/* For JavaScript
	 --------------------------------------------------------- */
	/**
	 * JavaScript用にチェックツールを出力する
	 *
	 */
	public function getValidates4JS() {
		return json_encode($this->_validates);
	}


	/* Extend Helper
	 --------------------------------------------------------- */
	/**
	 * selectboxの生成（引数に配列のキーを渡すバージョン）
	 *
	 */
	public function selectboxByListId($field_name, $list_key, $show_default=false, $default_label='-', $attributes=false) {

		$list = $this->getDefineList($list_key);

		return htmlHelper::selectbox($field_name, $this->_field, $list, $show_default, $default_label, $attributes);

	}

	/**
	 * radioの生成（引数に配列のキーを渡すバージョン）
	 *
	 */
	public function radiobuttonByListId($field_name, $list_key, $wrap=false, $attributes=false) {

		$list = $this->getDefineList($list_key);

		return HtmlHelper::radiobutton($field_name, $this->_field, $list, $wrap, $attributes);

	}

	/**
	 * checkboxの生成（引数に配列のキーを渡すバージョン）
	 *
	 */
	public function checkboxByListId($field_name, $list_key, $attributes=false) {

		$list = $this->getDefineList($list_key);

		return HtmlHelper::checkbox($field_name, $this->_field, $list, true, 'li', $attributes);

	}


	/**
	 * checkboxの生成（プライバシーポリシー）
	 *
	 */
	public function checkbox4privacy($field_name, $label, $attributes=false) {

		return HtmlHelper::checkboxAt($field_name, $this->_field, 'on', $label, false, $attributes);

	}


	/**
	 * 確認ページ用 : 選択されているリストの値を取得（selectbox, radiobutton用／引数にリストのキーを渡すバージョン）
	 *
	 */
	public function getSelectedValuesByListId($field_name, $list_key, $b_escape=true, $default=false) {

		$list = $this->getDefineList($list_key);

		$code = HtmlHelper::getSelectedValues($field_name, $this->_field, $list, $default);

		if ($b_escape) {
			$code = htmlspecialchars($code);
		}

		return $code;

	}

	/**
	 * 確認ページ用 : 選択されているリストの値を取得（checkbox用／引数にリストのキーを渡すバージョン）
	 *
	 */
	public function getSelectedValues4MultiByListId($field_name, $list_key, $b_escape=true, $token="／", $default=false) {

		$list = $this->getDefineList($list_key);

		$selected = HtmlHelper::getSelectedValues4Multi($field_name, $this->_field, $list);

		$code = '';
		if ($selected && (count($selected) > 0)) {
			$code = implode($token, $selected);
		} else if ($default) {
			$code = $default;
		}

		if ($b_escape) {
			$code = htmlspecialchars($code);
		}

		return $code;
	}


	/**
	 * エラー表示用のHTMLタグを生成する
	 *
	 */
	public function getErrorField($field_name) {
		$code = '';
		$attribute = '';
		if (!$this->hasErrorAt($field_name)) {
			$attribute = ' style="display: none;"';
		}

		$code = '<p class="form-alert" id="data-error-' . $field_name . '"' . $attribute . '>' . $this->getError($field_name, false) . '</p>';

		return $code;
	}


	/* Extend Validation
	 ---------------------------------------------------------------------------------------------- */
	/**
	 * 指定された配列に要素が存在するかのチェック（拡張版）
	 *
	 */
	protected function is_invalid_select_inlist($val, $key=null, $option=null) {
		if (isset($option) && isset($option['list_key'])) {
			// 定義されている配列の取得
			$list = $this->getDefineList($option['list_key']);

			if ($list) {
				return $this->is_invalid_select($val, $key, array('list' => $list));
			} else {
				return false;
			}
		}
	}

	/**
	 * 指定された配列に要素が存在するかのチェック（拡張版 / 値が配列の場合）
	 *
	 */
	protected function is_invalid_checked_inlist($val, $key=null, $option=null) {
		if (isset($option) && isset($option['list_key'])) {
			// 定義されている配列の取得
			$list = $this->getDefineList($option['list_key']);

			if ($list) {
				if (is_array($val)) {
					foreach($val as $idx => $val_at) {
						if (!array_key_exists($val_at, $list)) {
							return true;
						}
					}
				}
				return false;

			} else {
				return false;
			}
		}
	}


	/* Private
	 --------------------------------------------------------- */




}
