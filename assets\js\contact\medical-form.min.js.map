{"version": 3, "names": [], "mappings": "", "sources": ["contact/medical-form.js"], "sourcesContent": ["// メールフォーム用ライブラリが読み込まれている場合のみ処理\nvar Mm = Mm || {};\nif (Mm.MailForm) {\n\n\t(function($) {\n\n\t\tvar Obj = {}\n\n\t\t/**\n\t\t * 初期化処理\n\t\t *\n\t\t */\n\t\tObj.init = function() {\n\n\t\t\t// メールフォームの初期化（EFO）\n\t\t\tMm.MailForm.init('#js-form-input', {\n\t\t\t\tfunc: {\n\t\t\t\t\t// validate実行前 or 実行後の関数実行\n\t\t\t\t\t// お名前\n\t\t\t\t\tname: {\n\t\t\t\t\t\tbefore: function() {\n\t\t\t\t\t\t\t// 一部機種依存文字の置換\n\t\t\t\t\t\t\tObj.convertJIS('name');\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\tname_kana: {\n\t\t\t\t\t\tbefore: function() {\n\t\t\t\t\t\t\t// 一部機種依存文字の置換\n\t\t\t\t\t\t\tObj.convertJIS('name_kana');\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\temail: {\n\t\t\t\t\t\tbefore: function() {\n\t\t\t\t\t\t\t// 全角→半角置換\n\t\t\t\t\t\t\tObj.convertZen2Han('email');\n\t\t\t\t\t\t}\n\t\t\t\t\t},\n\t\t\t\t\temail_conf: {\n\t\t\t\t\t\tbefore: function() {\n\t\t\t\t\t\t\t// 全角→半角置換\n\t\t\t\t\t\t\tObj.convertZen2Han('email_conf');\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tcls: {\n\t\t\t\t\terror: 'error',\n\t\t\t\t\tsuccess: 'success'\n\t\t\t\t},\n\t\t\t\tsubmitDisable: false,\n\t\t\t\terrorShowAnimation: false,\n\t\t\t\thintShowAnimation: false,\n\t\t\t\tisUnloadCheck: true,\n\t\t\t\tunloadMessage: 'お問い合わせが完了しておりません。\\nお問い合わせを中止し、ページを移動しますか？'\n\t\t\t});\n\n\t\t\t// プライバシーポリシーcheckbox\n\t\t\t// Obj.privacyInit();\n\n\n\t\t\t// 名前の自動カタカナ入力\n      $.fn.autoKana('#js-linkage-name', '#js-linkage-name_kana', {\n\t\t\t\tkatakana : false  //true：カタカナ、false：ひらがな（デフォルト）\n      });\n\n      //\n      Obj.jobObserver();\n\n\t\t};\n\n\n\t\t/**\n\t\t *\n\t\t *\n\t\t */\n\t\tObj.jobObserver = function() {\n\t\t\tvar $job = $('#js-form-input input[name=\"job\"]');\n\t\t\tvar $job_other  = $('#js-form-input input[name=\"job_other\"]');\n\t\t\tvar $department = $('#js-form-input input[name=\"department\"]');\n\t\t\tvar $departmentWrap = $('.js-department');\n\n\t\t\t$job.on('change', function() {\n\t\t\t\t_judge();\n\t\t\t});\n\t\t\t_judge();\n\n\t\t\tfunction _judge() {\n\n\t\t\t\tvar $checked = $('#js-form-input input[name=\"job\"]:checked');\n\t\t\t\tvar checked  = ($checked && ($checked.length == 1))?$checked.val():'';\n\nconsole.log('job chenge : ' + checked);\n\t\t\t\tif (checked == 'doctor') {\n\t\t\t\t\t$departmentWrap.slideDown(100);\n\t\t\t\t} else {\n\t\t\t\t\t$department.val('');\n\t\t\t\t\t$departmentWrap.slideUp(100);\n\t\t\t\t}\n\n\t\t\t\tif (checked == 'other') {\n\t\t\t\t\t$job_other.prop('disabled', false);\n\t\t\t\t} else {\n\t\t\t\t\t$job_other.val('');\n\t\t\t\t\t$job_other.prop('disabled', true);\n\t\t\t\t}\n\t\t\t}\n\n\t\t};\n\n\n\t\t/**\n\t\t * チェックボタンの制御\n\t\t *\n\t\t */\n\t\tObj.privacyInit = function(){\n\n\t\t\tvar $checkBtn = $('#js-privacy');\n\t\t\tvar $wrapper = $checkBtn.parent();\n\t\t\tvar $submit  = $('.js-submit');\n\n\t\t\tfunction judgePrivacy() {\n\t\t\t\tif ($checkBtn.prop('checked')) {\n\t\t\t\t\t$wrapper.addClass('is_active');\n\t\t\t\t\t$submit.prop('disabled', false).addClass('is_release');\n\t\t\t\t} else {\n\t\t\t\t\t$wrapper.removeClass('is_active');\n\t\t\t\t\t$submit.prop('disabled', true).removeClass('is_release');\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif ($checkBtn.length > 0) {\n\t\t\t\t$checkBtn.on('click', judgePrivacy);\n\t\t\t\tjudgePrivacy();\n\t\t\t}\n\t\t}\n\n\t\t/**\n\t\t * 半角カタカナを全角に変換\n\t\t *\n\t\t */\n\t\tObj.convertHankana2Zenkana = function(fieldName) {\n\t\t\tif (Mm.MailForm.form[fieldName]) {\n\t\t\t\tMm.MailForm.form[fieldName].value = Mm.Validation.convertHankana2Zenkana(Mm.MailForm.form[fieldName].value);\n\t\t\t}\n\t\t};\n\t\t/**\n\t\t * 全角文字を半角に変換\n\t\t *\n\t\t */\n\t\tObj.convertZen2Han = function(fieldName) {\n\t\t\tif (Mm.MailForm.form[fieldName]) {\n\t\t\t\tMm.MailForm.form[fieldName].value = Mm.Validation.convertZen2Han(Mm.MailForm.form[fieldName].value);\n\t\t\t}\n\t\t};\n\t\t/**\n\t\t * 特殊文字の一部を変換\n\t\t *\n\t\t */\n\t\tObj.convertJIS = function(fieldName) {\n\t\t\tif (Mm.MailForm.form[fieldName]) {\n\t\t\t\tMm.MailForm.form[fieldName].value = Mm.Validation.convertJIS(Mm.MailForm.form[fieldName].value);\n\t\t\t}\n\t\t};\n\n\t\twindow.Mm.MailForm.project = Obj;\n\n\t})(jQuery);\n\n\n\t// PROCESS\n\tjQuery(document).ready(function() {\n\t\tMm.MailForm.project.init();\n\t});\n\n\n} else {\n\talert('必要なライブラリが読み込まれていません。');\n}\n\n\n/* Common */\n/**\n * Debug\n *\n */\nfunction log() {\n\tif(typeof console == \"undefined\") return;\n\tvar args = jQuery.makeArray(arguments);\n\tconsole.log.apply(console, args);\n}\n"], "file": "medical-form.min.js"}