<?php define('SITE_ROOT', substr( $_SERVER["SCRIPT_FILENAME"], 0, - strlen( $_SERVER["SCRIPT_NAME"] ))); ?>
<?php
///// フォーム関連処理 - START /////
/* Step0. 設定ファイルの読み込み
 --------------------------------------------- */
require_once(realpath(dirname(__FILE__) . '/_app/') . '/config.php');

/* Step1. 初期処理
 --------------------------------------------- */
// Sessionの初期化
session_name(APP_SESSION_NAME);
session_start();

// 強制SSLリダイレクト処理
// （本番環境、かつ、SSLアクセスじゃない場合）
if (defined('FORCE_SSL') && (FORCE_SSL === true)) {
	if ((ENV === 'publish') && !Common::is_ssl()) {
	  // SSLつきのURLへリダイレクト
	  @header('Location: ' . APP_URL_ENTRY_INPUT . '?' . $_SERVER['QUERY_STRING']);
	  exit;
	}
}


/* Step2. 主処理（アクション別の処理）
 --------------------------------------------- */
$entry = new Entry($_MAIL_VARS);

// 確認処理
if (isset($_POST['b_confirm']) || isset($_POST['b_confirm_x'])) {
  // データの初期化
  $entry->setData($_POST);

  // 入力チェック
  if ($entry->validates()) {

    // Sessionに入力データを登録
    $_SESSION[APP_SESSION_KEY] = $entry->getData();

    // 確認画面へリダイレクト
    @header('Location: ' . APP_URL_ENTRY_COMFIRM);
    exit;

  }


// 戻る処理（Sessionデータ内のキーに"b_back"が存在している
} else if (isset($_SESSION[APP_SESSION_KEY]) && isset($_SESSION[APP_SESSION_KEY]['b_back'])) {
  // データの初期化
  $entry->setData($_SESSION[APP_SESSION_KEY]);

  // Sessionのクリア
  $_SESSION[APP_SESSION_KEY] = null;
  unset($_SESSION[APP_SESSION_KEY]);

// 初期表示
} else {
  // 必要に応じて処理を記述

  // Sessionのクリア
  $_SESSION[APP_SESSION_KEY] = null;
  unset($_SESSION[APP_SESSION_KEY]);

}
///// フォーム関連処理 - END /////

// [START] ページごとに設定
$_PAGETITLE = 'Contact Us  |  Kowa Self Care International - Kowa Company, Ltd.';
$_DESCRIPTION = 'This page shows how to contact Kowa Company, Ltd.';
$_GLOBAL_LANG = 'hkg';
// [END] ページごとに設定]
?>
<!DOCTYPE html>
<html lang="en">
<head>
<?php include (SITE_ROOT . '/_include/common/head_common.php');?>
</head>

<body class="is-contact">
<div class="l_wrap" id="pageTop">
	<?php include (SITE_ROOT . '/'.$_GLOBAL_LANG.'/_include/common/header.php');?>

	<main class="l_main">
		
		<section class="uq_contact--section">
			<div class="uq_contact--bg">
				<div class="uq_contact__inner">
					<div class="uq_contact--head">
						<div class="uq_contact--title">
							<h1>聯絡我們</h1>
						</div>
						<div class="uq_contact-nav">
							<ol class="uq_contact-nav__list">
								<li class="uq_contact-nav__item is-act">輸入</li>
								<li class="uq_contact-nav__item">確認</li>
								<li class="uq_contact-nav__item">送出</li>
							</ol>
						</div>
					</div>
				</div>
			</div>

			<div class="uq_contact--form">
				<div class="uq_contact__inner">
					<form name="frm" method="post" id="js-form-input" novalidate>

						<dl class="uq_contact-col">
							<dt class="uq_contact-col__label">姓名<span class="uq_contact-required">必填</span></dt>
							<dd class="uq_contact-col__body">
								<p class="uq_contact-input"><input type="text" name="name" value="<?php echo $entry->getValue('name'); ?>" maxlength="50" data-target-id="name" id="js-linkage-name" placeholder=""></p>
								<?php echo $entry->getErrorField('name'); ?>
							</dd>
						</dl>
						<dl class="uq_contact-col">
							<dt class="uq_contact-col__label">電子郵件<span class="uq_contact-required">必填</span></dt>
							<dd class="uq_contact-col__body">
								<p class="uq_contact-input"><input type="email" name="email" value="<?php echo $entry->getValue('email'); ?>" maxlength="255" data-target-id="email" placeholder=""></p>
								<?php echo $entry->getErrorField('email'); ?>
								
							</dd>
						</dl>
						<dl class="uq_contact-col">
							<dt class="uq_contact-col__label">確認信<span class="uq_contact-required">必填</span></dt>
							<dd class="uq_contact-col__body">
								<p class="uq_contact-input is-conf"><input type="email" name="email_conf" value="<?php echo $entry->getValue('email_conf'); ?>" maxlength="255" data-target-id="email_conf" placeholder=""></p>
								<?php echo $entry->getErrorField('email_conf'); ?>
							</dd>
						</dl>
						<dl class="uq_contact-col">
							<dt class="uq_contact-col__label">組織名稱<span class="uq_contact-optional">選填</span></dt>
							<dd class="uq_contact-col__body">
								<p class="uq_contact-input"><input type="text" name="company" value="<?php echo $entry->getValue('company'); ?>" maxlength="50" data-target-id="company" placeholder=""></p>
								<?php echo $entry->getErrorField('company'); ?>
							</dd>
						</dl>
						<dl class="uq_contact-col">
							<dt class="uq_contact-col__label is-label_top">您的查詢<span class="uq_contact-required">必填</span></dt>
							<dd class="uq_contact-col__body">
								<p class="uq_contact-input is-textarea">
									<textarea rows="10" id="contents" name="contents" placeholder="" data-target-id="contents"><?php echo $entry->getValue('contents'); ?></textarea>
								</p>
								<?php echo $entry->getErrorField('contents'); ?>
							</dd>
						</dl>

						<dl class="uq_contact-col is-bgW">
							<dt class="uq_contact-col__label is-label_top">隱私權政策<span class="uq_contact-required">必填</span></dt>
							<dd class="uq_contact-col__body">
								<div class="uq_contact-terms">
									<?php include (SITE_ROOT . '/_include/parts/contact/privacy_HKG.php');?>
								</div>
								<p class="uq_contact-input">
									<input type="checkbox" id="privacy" name="privacy" value="1" data-target-id="privacy" <?php if ($entry->getValue('privacy') == '1'): ?> checked="checked"<?php endif; ?>>
									<label for="privacy" class="privacy-check">同意</label>
								</p>
								<?php echo $entry->getErrorField('privacy'); ?>
							</dd>
						</dl>
						
						<div class="uq_contact-btn">
							<div class="uq_contact-btn_inner">
								<ul class="uq_contact-btn-col">
									<li><p class="uq_contact-btn-col_btn is-cancell"><a href="javascript:history.back();">取消</a></p></li>
									<li><p class="uq_contact-btn-col_btn"><input type="submit" name="b_confirm" value="確認"></p></li>
								</ul>
							</div>
						</div>
					</form>
				</div>
			</div>
			
		</section>
	</main>

	
	<?php include (SITE_ROOT . '/'.$_GLOBAL_LANG.'/_include/common/footer.php');?>
</div>
<!-- //.l_wrap -->

<?php include (SITE_ROOT . '/_include/common/script.php');?>
<script src="/assets/js/contact/mm.util.min.js"></script>
<script src="/assets/js/contact/mm.controller.min.js"></script>
<script src="/assets/js/contact/mm.validation.min.js"></script>
<script src="/hkg/contact/api/mail-form-js.php"></script>
<script src="/assets/js/contact/genera-form.min.js"></script>

</body>
</html>