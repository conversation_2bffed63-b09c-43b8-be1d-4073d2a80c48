<?php

class Token {

	private static $name = "\0__TOKENS__\0";
	private static $max = 10;

	/**
	 * Tokenの取得
	 * 
	 */
	public static function generate() {
		self::initialize();

		$token = md5(uniqid(rand(), true));

		$_SESSION[self::$name] = $token;

		return $token;

	}

	/**
	 * Tokenチェック
	 * 
	 */
	public static function flush($token, $throw = false) {
		self::initialize();

		// 引数で渡されたTOKEN（post変数）と、Sessionに保持しているSessionを比較しチェック
		if (isset($_SESSION[self::$name]) && ($_SESSION[self::$name] === $token)) {
			// Sessionに保存しているTokenの破棄
			unset($_SESSION[self::$name]);

			return true;

		}

		// Check NGの場合
		if ($throw) {
			throw new RuntimeException('Invalid Access.');
		} else {
			return false;
		}
	}

	/**
	 * Tokenチェック
	 * 
	 */
	public static function validate($token, $throw = false) {
		self::initialize();

		// 引数で渡されたTOKEN（post変数）と、Sessionに保持しているSessionを比較しチェック
		if (isset($_SESSION[self::$name]) && ($_SESSION[self::$name] === $token)) {
			return true;

		}

		// Check NGの場合
		if ($throw) {
			throw new RuntimeException('Invalid Access.');
		} else {
			return false;
		}
	}

	/**
	 *
	 *
	 */
	private static function initialize() {
		if (!isset($_SESSION)) {
			throw new BadMethodCallException('セッションが開始されていません');
		}
	}

}