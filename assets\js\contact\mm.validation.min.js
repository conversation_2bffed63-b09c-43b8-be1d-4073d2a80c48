var Mm = Mm || {};

(function() {
	var Obj = {};

	Obj.check = function(funcName, value, fld_name, option, form) {
		var result = false;

		var func = this[funcName];

		// 用意されているチェックのみを実施
		if (func && (typeof(func) === 'function')) {
			result = func(value, fld_name, option, form);
		}

		return result;
	};

	/* check関数
	 * チェックNGの場合、true（エラーあり）
	 * チェックOKの場合、false
	 --------------------------------------------*/
	/**
	 * 未入力チェック
	 *
	 */
	Obj.is_empty = function(str) {
		if (str) {
			if (Obj.trim(str) !== '') {
				return false;
			}
		}

		return true;
	};

	/**
	 * 未入力チェック
	 * ※いずれかの項目に値がセットされているかのcheck（すべて未入力の場合エラー）
	 *
	 */
	Obj.is_empty_all = function(str, fld_name, option, form) {

		var fieldName, tmpStr;

		// optionに指定されたフィールドのチェック
		// optionに判定対象のフィールドがセットされているか
		// ※要素名 'key' にセットされている
		if (option && option['key']) {
			// 配列で指定されている場合
			if (Obj.isArray(option['key'])) {

				for(var i=0,imax=option['key'].length; i<imax; i++) {
					fieldName = option['key'][i];
					tmpStr    = form[fieldName].value;

					if (!Obj.is_empty(tmpStr)) {
						return false;
					}
				}

			// １つのみの場合
			} else if (typeof(option['key']) === 'string') {

				fieldName = option['key'];
				tmpStr    = form[fieldName].value;

				if (!Obj.is_empty(tmpStr)) {
					return false;
				}
			}

		}

		// 該当フィールドのチェック
		if (!Obj.is_empty(str)) {
			return false;
		}

		return true;
	};

	/**
	 * 未入力チェック
	 * ※すべての項目に値がセットされているかのcheck（１つでも未入力がある場合エラー）
	 *
	 */
	Obj.is_empty_any = function(str, fld_name, option, form) {

		var fieldName, tmpStr;

		// optionに指定されたフィールドのチェック
		// optionに判定対象のフィールドがセットされているか
		// ※要素名 'key' にセットされている
		if (option && option['key']) {
			// 配列で指定されている場合
			if (Obj.isArray(option['key'])) {

				for(var i=0,imax=option['key'].length; i<imax; i++) {
					fieldName = option['key'][i];
					tmpStr    = form[fieldName].value;

					if (Obj.is_empty(tmpStr)) {
						return true;
					}
				}

			// １つのみの場合
			} else if (typeof(option['key']) === 'string') {

				fieldName = option['key'];
				tmpStr    = form[fieldName].value;

				if (Obj.is_empty(tmpStr)) {
					return true;
				}
			}

		}

		// 該当フィールドのチェック
		if (Obj.is_empty(str)) {
			return true;
		}

		return false;
	};


	/**
	 * 未入力チェック
	 * ※相関チェック／指定されたフィールドの値が、指定された値の場合のみ必須
	 *
	 */
	Obj.is_empty_selected = function(str, fld_name, option, form) {

		var fieldName, fieldValue, tmpStr;

		// optionに指定されたフィールドのチェック
		// optionに判定対象のフィールドがセットされているか
		// ※要素名 'key' にセットされている
		if (option && option['key'] && option['value']) {

			fieldName  = option['key'];
			fieldValue = option['value'];

			tmpStr    = form[fieldName].value;

			if (tmpStr != fieldValue) {
				return false;
			}

		}

		// 該当フィールドのチェック
		if (Obj.is_empty(str)) {
			return true;
		}

		return false;
	};


	/**
	 * 全角カタカナチェック
	 *
	 */
	Obj.is_not_katakana = function(str) {
		// Unicode : 0x30a0 ～ 0x30ff
		// if (Obj.is_empty(str) || str.match(/^[\u30A0-\u30FF]+$/)) {
		if (Obj.is_empty(str) || (Obj.convertHankana2Zenkana(str) + '').match(/^[ァ-ヶー・　 ]+$/)) {
			return false;
		}

		return true;
	};

	/**
	 * 全角ひらがなチェック
	 *
	 */
	Obj.is_not_hiragana = function(str) {
		// Unicode : 0x3040 ～ 0x309f
		// if (Obj.is_empty(str) || str.match(/^[\u3040-\u309f]+$/)) {
		if (Obj.is_empty(str) || str.match(/^[ぁ-んー・　 ]+$/)) {
			return false;
		}

		return true;
	};

	/**
	 * 電話番号形式チェック
	 *
	 */
	Obj.is_invalid_phone = function(str) {
		if (Obj.is_empty(str) || Obj.convertZen2Han(str).match(/^(\+)?([0-9]+[-]?){2,}[0-9]+$/)) {
			return false;
		}

		return true;
	};

	/**
	 * 電話番号形式チェック（上桁と中桁と下桁で分かれている場合）
	 * ※option['key'] = array('中桁のフィールドキー', '下桁のフィールドキー')の形式で渡されること
	 *
	 */
	Obj.is_invalid_phone_separate = function(str, fld_name, option, form) {

		var fieldName;
		var phoneno = Obj.convertZen2Han(str);

		if (option && option['key'] && Obj.isArray(option['key'])) {
			for(var i=0,imax=option['key'].length; i<imax; i++) {
				fieldName = option['key'][i];
				phoneno += '-' + Obj.convertZen2Han(form[fieldName].value);
			}
		}

		return Obj.is_valid_phone(phoneno);
	};

	/**
	 * 郵便番号形式チェック
	 *
	 */
	Obj.is_invalid_zipcode = function(str) {

		if (Obj.is_empty(str) || Obj.convertZen2Han(str).match(/^([0-9]+[-]?)[0-9]+$/)) {
			return false;
		}

		return true;
	};

	/**
	 * 郵便番号形式チェック（上桁と下桁で分かれている場合）
	 * ※option['key'] = '下桁のフィールドキー'の形式で渡されること
	 *
	 */
	Obj.is_invalid_zipcode_separate = function(str, fld_name, option, form) {

		var fieldName;
		var zipcode = Obj.convertZen2Han(str);

		if (!zipcode) {
			return false;
		}

		if (option && option['key'] && !Obj.isArray(option['key'])) {
			fieldName = option['key'];
			zipcode += '-' + Obj.convertZen2Han(form[fieldName].value);
		}

		return Obj.is_invalid_zipcode(zipcode);
	};

	/**
	 * 数字チェック
	 *
	 */
	Obj.is_invalid_numeric = function(str) {
		if (Obj.is_empty(str) || Obj.convertZen2Han(str+'').match(/^[0-9]+$/)) {
			return false;
		}

		return true;
	};

	/**
	 * 正の整数チェック
	 *
	 */
	Obj.is_invalid_positive_integer = function(str) {
		if (Obj.is_empty(str) || Obj.convertZen2Han(str+'').match(/^[1-9][0-9]*$/)) {
			return false;
		}

		return true;
	};

	/**
	 * 英字チェック
	 *
	 */
	Obj.is_invalid_alphabet = function(str) {
		if (Obj.is_empty(str) || Obj.convertZen2Han(str+'').match(/^[a-zA-Z]+$/)) {
			return false;
		}

		return true;
	};

	/**
	 * 英字記号チェック
	 *
	 */
	Obj.is_invalid_alphabet_ext = function(str) {
		if (Obj.is_empty(str) || Obj.convertZen2Han(str+'').match(/^[a-zA-Z!-~ ]+$/)) {
			return false;
		}

		return true;
	};

	/**
	 * 英数字チェック
	 *
	 */
	Obj.is_invalid_alphanum = function(str) {
		if (Obj.is_empty(str) || Obj.convertZen2Han(str+'').match(/^[a-zA-Z0-9]+$/)) {
			return false;
		}

		return true;
	};

	/**
	 * 英数字チェック
	 *
	 */
	Obj.is_invalid_alphanum = function(str) {
		if (Obj.is_empty(str) || Obj.convertZen2Han(str+'').match(/^[a-zA-Z0-9]+$/)) {
			return false;
		}

		return true;
	};

	/**
	 * 英数字記号チェック
	 *
	 */
	Obj.is_invalid_alphanum_ext = function(str) {
		if (Obj.is_empty(str) || Obj.convertZen2Han(str+'').match(/^[a-zA-Z0-9!-~ ]+$/)) {
			return false;
		}

		return true;
	};

	/**
	 * メールアドレス形式チェック
	 *
	 */
	Obj.is_invalid_mailaddress = function(str) {

		if (Obj.is_empty(str) || Obj.convertZen2Han(str).match(/^[A-Za-z0-9]+[\w\.\+-]+@[\w\.-]{2,}\.\w{2,}$/)) {
			return false;
		}

		return true;
	};

	/**
	 * 同値チェック
	 * ※option['key'] = '比較対象のフィールド名'の形式で渡されること
	 *
	 */
	Obj.is_not_equal = function(str, fld_name, option, form) {

		var fieldName;
		var str2 = '';

		if (option && option['key'] && !Obj.isArray(option['key'])) {
			fieldName = option['key'];
			str2 = form[fieldName].value;
		}

		if (Obj.convertZen2Han(str) === Obj.convertZen2Han(str2)) {
			return false;
		}

		return true;
	};

	/**
	 * 環境依存文字・旧漢字などJISに変換できない文字チェック
	 * 機種依存文字を含む場合、falseを返す（エラー）
	 *
	 * ※※ !!! 簡易チェックのみ !!! ※※※
	 *
	 */
	Obj.is_invalid_jis = function(str) {

		var search_txt = "[①②③④⑤⑥⑦⑧⑨⑩⑪⑫⑬⑭⑮⑯⑰⑱⑲⑳ⅠⅡⅢⅣⅤⅥⅦⅧⅨⅩ㍉㌔㌢㍍㌘㌧㌃㌶㍑㍗㌍㌦㌣㌫㍊㌻㎜㎝㎞㎎㎏㏄㎡㍻〝〟№㏍℡㊤㊥㊦㊧㊨㈱㈲㈹㍾㍽㍼]";

		if (!Obj.is_empty(str) && str.match(search_txt)) {
			return true;
		}

		return false;
	};

	/**
	 * YYYY-MM-DD形式かチェック
	 *
	 */
	Obj.is_invalid_ymd = function(str) {

		if (Obj.is_empty(str)) {
			return false;
		}

		// 正規表現による書式チェック
		str = str.replace(/\//g, "-");
		match = str.match(/^(\d{4})\-(\d{1,2})\-(\d{1,2})$/);

		if(!match) {
			return true;
		}

		var year  = parseInt(match[1], 10);
		var month = parseInt(match[2], 10) - 1; // Javascriptは、0-11で表現
		var day   = parseInt(match[3], 10);

		// 月,日の妥当性チェック
		if(month >= 0 && month <= 11 && day >= 1 && day <= 31) {
			var date = new Date(year, month, day);
			if(isNaN(date)) {
				return true;
			} else if( (date.getFullYear() !== year) || (date.getMonth() !== month) || (date.getDate() !== day) ) {
				return true;
			}
		} else {
			return true;
		}

		return false;

	};

	/**
	 * 指定されたフィールド（年・月・日が別のフィールド）が日付形式かのチェック
	 * ※option['y'] = '年のフィールド名'、option['m'] = '月のフィールド名'、option['d'] = '日のフィールド名'、の形式で渡されることが前提
	 *
	 */
	Obj.is_invalid_ymd_array = function(str, fld_name, option, form) {

		var fieldNameY, fieldNameM, fieldNameD;

		if (option && option['y'] && option['m'] && option['d'] ) {

			fieldNameY = option['y'];
			fieldNameM = option['m'];
			fieldNameD = option['d'];
// console.log(form[fieldNameY].value + '-' + form[fieldNameM].value + '-' + form[fieldNameD].value);
			if (Obj.is_empty(form[fieldNameY].value) || Obj.is_empty(form[fieldNameM].value) || Obj.is_empty(form[fieldNameD].value)) {
				return false;
			}

			return Obj.is_invalid_ymd(form[fieldNameY].value + '-' + form[fieldNameM].value + '-' + form[fieldNameD].value);
		}

		return false;

	};

	/**
	 * 指定されたフィールド（年・月が別のフィールド）が年月形式かのチェック
	 * ※option['y'] = '年のフィールド名'、option['m'] = '月のフィールド名'、の形式で渡されることが前提
	 *
	 */
	Obj.is_invalid_ym = function(str, fld_name, option, form) {

		var fieldNameY, fieldNameM, fieldNameD;

		if (option && option['y'] && option['m']) {

			fieldNameY = option['y'];
			fieldNameM = option['m'];

			if (!Obj.is_empty(form[fieldNameY].value + form[fieldNameM].value)) {
				return Obj.is_invalid_ymd(form[fieldNameY].value + '-' + form[fieldNameM].value + '-01');
			} else {
				return false;
			}
		}

		return false;

	};

	/**
	 * 最大文字数チェック
	 *
	 */
	Obj.is_over_max_length = function(str, fld_name, option) {

		if (!Obj.is_empty(str) && option && option['length'] && !Obj.is_invalid_numeric(option['length'])) {
			var length = parseInt(option['length'], 10);
			if (str.length > length) {
				return true;
			}
		}

		return false;
	};

	/**
	 * 最大文字数チェック（全角を1文字、半角を0.5文字として換算）
	 *
	 */
/**
	Obj.is_over_max_length_custom = function(str, option) {
		if (!Obj.is_empty(str) && option && option['length'] && !Obj.is_invalid_numeric(option['length'])) {

		}

		return false;
	};
*/

	/**
	 * 最大数チェック
	 *
	 */
	Obj.is_over_max_num = function(str, fld_name, option) {
		if (!Obj.is_empty(str) && option && option['max'] && !Obj.is_invalid_numeric(option['max'])) {
			var max = parseInt(option['max'], 10);
			var num = parseInt(str, 10);

			if (num > max) {
				return true;
			}
		}

		return false;
	};

	/**
	 * 最大バイト数チェック
	 *
	 */
	Obj.is_over_max_byte = function(str, fld_name, option) {
		if (!Obj.is_empty(str) && option && option['length'] && !Obj.is_invalid_numeric(option['length'])) {
			var length = parseInt(option['length'], 10);

			// encodeURIでパーセントエンコード
			str = encodeURI(str);
			// 全角文字が%XXのようになるので、それを半角1文字に変換した文字列を作成し、バイト数を数える
			str = str.replace(/%[0-9A-F]{2}/g, '*');

			if (str.length > length) {
				return false;
			}

		}

		return false;
	};

	/**
	 * 文字数チェック
	 *
	 */
	Obj.is_not_equal_length = function(str, fld_name, option) {
		if (!Obj.is_empty(str) && option && option['length'] && !Obj.is_invalid_numeric(option['length'])) {
			var length = parseInt(option['length'], 10);
			if (str.length !== length) {
				return true;
			}

		}

		return false;
	};

	/**
	 * 正規表現に合致するかのチェック
	 *
	 */
	Obj.is_invalid_pattern = function(str, fld_name, option) {

		if (Obj.is_empty(str)) {
			return false;
		}

		if (option && option['pattern'] && !Obj.isArray(option['pattern'])) {
			var pattern = option['pattern'];
			var flg = option['flg']?option['flg']:'';
			var regexp  = new RegExp(pattern, flg);

			if (str.match(regexp)) {
				return false;
			} else {
				return true;
			}

		} else {
			return false;

		}

	}

	// 拡張チェック ///////////////////////////////////
	/**
	 * チェックボックスが選択されているかのチェック
	 *
	 */
	Obj.is_not_checked = function(check, fld_name, option) {

		if (Obj.isArray(check)) {
			if (check.length > 0) {
				return false;
			}
		} else if (Obj.trim(check)) {
			return false;
		}

		return true;
	}


	/* utility関数
	 --------------------------------------------*/
	/**
	 * trim関数
	 *
	 */
	Obj.trim = function(str) {
		return (str + '').replace(/(^[\s　]+)|([\s　]+$)/g, "");
	};

	/**
	 * 配列かどうかのチェック
	 *
	 */
	Obj.isArray = function(arr) {
	    return Object.prototype.toString.call(arr) === '[object Array]';
	};

	/**
	 * 半角カタカナを全角カタカナに変換
	 *
	 */
	Obj.convertHankana2Zenkana = function (str) {
		if (Obj.is_empty(str)) {
			return str;
		}

		var kanaMap = {
			'ｶﾞ': 'ガ', 'ｷﾞ': 'ギ', 'ｸﾞ': 'グ', 'ｹﾞ': 'ゲ', 'ｺﾞ': 'ゴ',
			'ｻﾞ': 'ザ', 'ｼﾞ': 'ジ', 'ｽﾞ': 'ズ', 'ｾﾞ': 'ゼ', 'ｿﾞ': 'ゾ',
			'ﾀﾞ': 'ダ', 'ﾁﾞ': 'ヂ', 'ﾂﾞ': 'ヅ', 'ﾃﾞ': 'デ', 'ﾄﾞ': 'ド',
			'ﾊﾞ': 'バ', 'ﾋﾞ': 'ビ', 'ﾌﾞ': 'ブ', 'ﾍﾞ': 'ベ', 'ﾎﾞ': 'ボ',
			'ﾊﾟ': 'パ', 'ﾋﾟ': 'ピ', 'ﾌﾟ': 'プ', 'ﾍﾟ': 'ペ', 'ﾎﾟ': 'ポ',
			'ｳﾞ': 'ヴ', 'ﾜﾞ': 'ヷ', 'ｦﾞ': 'ヺ',
			'ｱ': 'ア', 'ｲ': 'イ', 'ｳ': 'ウ', 'ｴ': 'エ', 'ｵ': 'オ',
			'ｶ': 'カ', 'ｷ': 'キ', 'ｸ': 'ク', 'ｹ': 'ケ', 'ｺ': 'コ',
			'ｻ': 'サ', 'ｼ': 'シ', 'ｽ': 'ス', 'ｾ': 'セ', 'ｿ': 'ソ',
			'ﾀ': 'タ', 'ﾁ': 'チ', 'ﾂ': 'ツ', 'ﾃ': 'テ', 'ﾄ': 'ト',
			'ﾅ': 'ナ', 'ﾆ': 'ニ', 'ﾇ': 'ヌ', 'ﾈ': 'ネ', 'ﾉ': 'ノ',
			'ﾊ': 'ハ', 'ﾋ': 'ヒ', 'ﾌ': 'フ', 'ﾍ': 'ヘ', 'ﾎ': 'ホ',
			'ﾏ': 'マ', 'ﾐ': 'ミ', 'ﾑ': 'ム', 'ﾒ': 'メ', 'ﾓ': 'モ',
			'ﾔ': 'ヤ', 'ﾕ': 'ユ', 'ﾖ': 'ヨ',
			'ﾗ': 'ラ', 'ﾘ': 'リ', 'ﾙ': 'ル', 'ﾚ': 'レ', 'ﾛ': 'ロ',
			'ﾜ': 'ワ', 'ｦ': 'ヲ', 'ﾝ': 'ン',
			'ｧ': 'ァ', 'ｨ': 'ィ', 'ｩ': 'ゥ', 'ｪ': 'ェ', 'ｫ': 'ォ',
			'ｯ': 'ッ', 'ｬ': 'ャ', 'ｭ': 'ュ', 'ｮ': 'ョ',
			'｡': '。', '､': '、', 'ｰ': 'ー', '｢': '「', '｣': '」', '･': '・'
		};

		var reg = new RegExp('(' + Object.keys(kanaMap).join('|') + ')', 'g');

		return str.replace(reg, function (match) {
				return kanaMap[match];
			})
			.replace(/ﾞ/g, '゛')
			.replace(/ﾟ/g, '゜');
	};

	/**
	 * 全角から半角に置き換え
	 *
	 * 全角チルダ、全角波ダッシュ共に半角チルダに変換
	 * 全角ハイフン、全角ダッシュ、全角マイナス記号は半角ハイフンに変換
	 * 長音符は半角ハイフンに含めない（住所の地名等に使用される為）
	 *
	 * 今は良いがUnicode 8.0で波ダッシュの形が変わるみたいなので、波ダッシュを変換に
	 * 含めるべきかどうかは検討が必要
	 *
	 */
	Obj.convertZen2Han = function(str) {
		// 英字変換
		str = str.replace(/[Ａ-Ｚａ-ｚ]/g, function (s) {
				return String.fromCharCode(s.charCodeAt(0) - 65248);
			});

		// 数字変換
		str = str.replace(/[０-９]/g, function (s) {
				return String.fromCharCode(s.charCodeAt(0) - 65248);
			});

		// 記号変換
		var reg = /[！＂＃＄％＆＇（）＊＋，－．／：；＜＝＞？＠［＼］＾＿｀｛｜｝]/g;
		str = str.replace(reg, function (s) {
				return String.fromCharCode(s.charCodeAt(0) - 65248);
			}).replace(/[‐－―−]/g, '-');

		// チルダ変換
		str = str.replace(/[～〜]/g, '~');
		// スペース変換
		str = str.replace(/　/g, ' ');

		return str;
	};

	/**
	 * 半角カタカナを全角カタカナに変換
	 *
	 */
	Obj.convertJIS = function (str) {
		if (Obj.is_empty(str)) {
			return str;
		}

		var jisMap = {
			'㈱': '(株)', '㈲': '(有)', '㈹': '(代)'
		};

		var reg = new RegExp('(' + Object.keys(jisMap).join('|') + ')', 'g');

		return str.replace(reg, function (match) {
				return jisMap[match];
			});
	};

	Mm.Validation = Obj;
})();


//# sourceMappingURL=mm.validation.min.js.map
