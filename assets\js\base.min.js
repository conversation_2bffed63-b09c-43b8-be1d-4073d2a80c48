! function (e, t) {
    "use strict";
    "object" == typeof module && "object" == typeof module.exports ? module.exports = e.document ? t(e, !0) : function (e) {
        if (!e.document) throw new Error("jQuery requires a window with a document");
        return t(e)
    } : t(e)
}("undefined" != typeof window ? window : this, function (T, e) {
    "use strict";

    function v(e) {
        return null != e && e === e.window
    }
    var t = [],
        C = T.document,
        i = Object.getPrototypeOf,
        a = t.slice,
        g = t.concat,
        l = t.push,
        o = t.indexOf,
        n = {},
        r = n.toString,
        m = n.hasOwnProperty,
        s = m.toString,
        c = s.call(Object),
        y = {},
        w = function (e) {
            return "function" == typeof e && "number" != typeof e.nodeType
        },
        d = {
            type: !0,
            src: !0,
            nonce: !0,
            noModule: !0
        };

    function b(e, t, n) {
        var i, o, r = (n = n || C).createElement("script");
        if (r.text = e, t)
            for (i in d)(o = t[i] || t.getAttribute && t.getAttribute(i)) && r.setAttribute(i, o);
        n.head.appendChild(r).parentNode.removeChild(r)
    }

    function x(e) {
        return null == e ? e + "" : "object" == typeof e || "function" == typeof e ? n[r.call(e)] || "object" : typeof e
    }
    var u = "3.4.1",
        S = function (e, t) {
            return new S.fn.init(e, t)
        },
        p = /^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;

    function f(e) {
        var t = !!e && "length" in e && e.length,
            n = x(e);
        return !w(e) && !v(e) && ("array" === n || 0 === t || "number" == typeof t && 0 < t && t - 1 in e)
    }
    S.fn = S.prototype = {
        jquery: u,
        constructor: S,
        length: 0,
        toArray: function () {
            return a.call(this)
        },
        get: function (e) {
            return null == e ? a.call(this) : e < 0 ? this[e + this.length] : this[e]
        },
        pushStack: function (e) {
            var t = S.merge(this.constructor(), e);
            return t.prevObject = this, t
        },
        each: function (e) {
            return S.each(this, e)
        },
        map: function (n) {
            return this.pushStack(S.map(this, function (e, t) {
                return n.call(e, t, e)
            }))
        },
        slice: function () {
            return this.pushStack(a.apply(this, arguments))
        },
        first: function () {
            return this.eq(0)
        },
        last: function () {
            return this.eq(-1)
        },
        eq: function (e) {
            var t = this.length,
                n = +e + (e < 0 ? t : 0);
            return this.pushStack(0 <= n && n < t ? [this[n]] : [])
        },
        end: function () {
            return this.prevObject || this.constructor()
        },
        push: l,
        sort: t.sort,
        splice: t.splice
    }, S.extend = S.fn.extend = function () {
        var e, t, n, i, o, r, s = arguments[0] || {},
            a = 1,
            l = arguments.length,
            c = !1;
        for ("boolean" == typeof s && (c = s, s = arguments[a] || {}, a++), "object" == typeof s || w(s) || (s = {}), a === l && (s = this, a--); a < l; a++)
            if (null != (e = arguments[a]))
                for (t in e) i = e[t], "__proto__" !== t && s !== i && (c && i && (S.isPlainObject(i) || (o = Array.isArray(i))) ? (n = s[t], r = o && !Array.isArray(n) ? [] : o || S.isPlainObject(n) ? n : {}, o = !1, s[t] = S.extend(c, r, i)) : void 0 !== i && (s[t] = i));
        return s
    }, S.extend({
        expando: "jQuery" + (u + Math.random()).replace(/\D/g, ""),
        isReady: !0,
        error: function (e) {
            throw new Error(e)
        },
        noop: function () {},
        isPlainObject: function (e) {
            var t, n;
            return !(!e || "[object Object]" !== r.call(e) || (t = i(e)) && ("function" != typeof (n = m.call(t, "constructor") && t.constructor) || s.call(n) !== c))
        },
        isEmptyObject: function (e) {
            var t;
            for (t in e) return !1;
            return !0
        },
        globalEval: function (e, t) {
            b(e, {
                nonce: t && t.nonce
            })
        },
        each: function (e, t) {
            var n, i = 0;
            if (f(e))
                for (n = e.length; i < n && !1 !== t.call(e[i], i, e[i]); i++);
            else
                for (i in e)
                    if (!1 === t.call(e[i], i, e[i])) break;
            return e
        },
        trim: function (e) {
            return null == e ? "" : (e + "").replace(p, "")
        },
        makeArray: function (e, t) {
            var n = t || [];
            return null != e && (f(Object(e)) ? S.merge(n, "string" == typeof e ? [e] : e) : l.call(n, e)), n
        },
        inArray: function (e, t, n) {
            return null == t ? -1 : o.call(t, e, n)
        },
        merge: function (e, t) {
            for (var n = +t.length, i = 0, o = e.length; i < n; i++) e[o++] = t[i];
            return e.length = o, e
        },
        grep: function (e, t, n) {
            for (var i = [], o = 0, r = e.length, s = !n; o < r; o++) !t(e[o], o) != s && i.push(e[o]);
            return i
        },
        map: function (e, t, n) {
            var i, o, r = 0,
                s = [];
            if (f(e))
                for (i = e.length; r < i; r++) null != (o = t(e[r], r, n)) && s.push(o);
            else
                for (r in e) null != (o = t(e[r], r, n)) && s.push(o);
            return g.apply([], s)
        },
        guid: 1,
        support: y
    }), "function" == typeof Symbol && (S.fn[Symbol.iterator] = t[Symbol.iterator]), S.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "), function (e, t) {
        n["[object " + t + "]"] = t.toLowerCase()
    });
    var h = function (n) {
        function u(e, t, n) {
            var i = "0x" + t - 65536;
            return i != i || n ? t : i < 0 ? String.fromCharCode(65536 + i) : String.fromCharCode(i >> 10 | 55296, 1023 & i | 56320)
        }

        function o() {
            k()
        }
        var e, f, b, r, s, h, p, v, x, l, c, k, T, a, C, g, d, m, y, S = "sizzle" + 1 * new Date,
            w = n.document,
            $ = 0,
            i = 0,
            E = le(),
            N = le(),
            A = le(),
            j = le(),
            O = function (e, t) {
                return e === t && (c = !0), 0
            },
            H = {}.hasOwnProperty,
            t = [],
            D = t.pop,
            I = t.push,
            _ = t.push,
            L = t.slice,
            q = function (e, t) {
                for (var n = 0, i = e.length; n < i; n++)
                    if (e[n] === t) return n;
                return -1
            },
            P = "checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",
            M = "[\\x20\\t\\r\\n\\f]",
            W = "(?:\\\\.|[\\w-]|[^\0-\\xa0])+",
            z = "\\[" + M + "*(" + W + ")(?:" + M + "*([*^$|!~]?=)" + M + "*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|(" + W + "))|)" + M + "*\\]",
            R = ":(" + W + ")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|" + z + ")*)|.*)\\)|)",
            B = new RegExp(M + "+", "g"),
            F = new RegExp("^" + M + "+|((?:^|[^\\\\])(?:\\\\.)*)" + M + "+$", "g"),
            G = new RegExp("^" + M + "*," + M + "*"),
            U = new RegExp("^" + M + "*([>+~]|" + M + ")" + M + "*"),
            X = new RegExp(M + "|>"),
            Y = new RegExp(R),
            Q = new RegExp("^" + W + "$"),
            V = {
                ID: new RegExp("^#(" + W + ")"),
                CLASS: new RegExp("^\\.(" + W + ")"),
                TAG: new RegExp("^(" + W + "|[*])"),
                ATTR: new RegExp("^" + z),
                PSEUDO: new RegExp("^" + R),
                CHILD: new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\(" + M + "*(even|odd|(([+-]|)(\\d*)n|)" + M + "*(?:([+-]|)" + M + "*(\\d+)|))" + M + "*\\)|)", "i"),
                bool: new RegExp("^(?:" + P + ")$", "i"),
                needsContext: new RegExp("^" + M + "*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\(" + M + "*((?:-\\d)?\\d*)" + M + "*\\)|)(?=[^-]|$)", "i")
            },
            J = /HTML$/i,
            K = /^(?:input|select|textarea|button)$/i,
            Z = /^h\d$/i,
            ee = /^[^{]+\{\s*\[native \w/,
            te = /^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,
            ne = /[+~]/,
            ie = new RegExp("\\\\([\\da-f]{1,6}" + M + "?|(" + M + ")|.)", "ig"),
            oe = /([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,
            re = function (e, t) {
                return t ? "\0" === e ? "�" : e.slice(0, -1) + "\\" + e.charCodeAt(e.length - 1).toString(16) + " " : "\\" + e
            },
            se = be(function (e) {
                return !0 === e.disabled && "fieldset" === e.nodeName.toLowerCase()
            }, {
                dir: "parentNode",
                next: "legend"
            });
        try {
            _.apply(t = L.call(w.childNodes), w.childNodes), t[w.childNodes.length].nodeType
        } catch (e) {
            _ = {
                apply: t.length ? function (e, t) {
                    I.apply(e, L.call(t))
                } : function (e, t) {
                    for (var n = e.length, i = 0; e[n++] = t[i++];);
                    e.length = n - 1
                }
            }
        }

        function ae(e, t, n, i) {
            var o, r, s, a, l, c, d, u = t && t.ownerDocument,
                p = t ? t.nodeType : 9;
            if (n = n || [], "string" != typeof e || !e || 1 !== p && 9 !== p && 11 !== p) return n;
            if (!i && ((t ? t.ownerDocument || t : w) !== T && k(t), t = t || T, C)) {
                if (11 !== p && (l = te.exec(e)))
                    if (o = l[1]) {
                        if (9 === p) {
                            if (!(s = t.getElementById(o))) return n;
                            if (s.id === o) return n.push(s), n
                        } else if (u && (s = u.getElementById(o)) && y(t, s) && s.id === o) return n.push(s), n
                    } else {
                        if (l[2]) return _.apply(n, t.getElementsByTagName(e)), n;
                        if ((o = l[3]) && f.getElementsByClassName && t.getElementsByClassName) return _.apply(n, t.getElementsByClassName(o)), n
                    } if (f.qsa && !j[e + " "] && (!g || !g.test(e)) && (1 !== p || "object" !== t.nodeName.toLowerCase())) {
                    if (d = e, u = t, 1 === p && X.test(e)) {
                        for ((a = t.getAttribute("id")) ? a = a.replace(oe, re) : t.setAttribute("id", a = S), r = (c = h(e)).length; r--;) c[r] = "#" + a + " " + we(c[r]);
                        d = c.join(","), u = ne.test(e) && me(t.parentNode) || t
                    }
                    try {
                        return _.apply(n, u.querySelectorAll(d)), n
                    } catch (t) {
                        j(e, !0)
                    } finally {
                        a === S && t.removeAttribute("id")
                    }
                }
            }
            return v(e.replace(F, "$1"), t, n, i)
        }

        function le() {
            var i = [];
            return function e(t, n) {
                return i.push(t + " ") > b.cacheLength && delete e[i.shift()], e[t + " "] = n
            }
        }

        function ce(e) {
            return e[S] = !0, e
        }

        function de(e) {
            var t = T.createElement("fieldset");
            try {
                return !!e(t)
            } catch (e) {
                return !1
            } finally {
                t.parentNode && t.parentNode.removeChild(t), t = null
            }
        }

        function ue(e, t) {
            for (var n = e.split("|"), i = n.length; i--;) b.attrHandle[n[i]] = t
        }

        function pe(e, t) {
            var n = t && e,
                i = n && 1 === e.nodeType && 1 === t.nodeType && e.sourceIndex - t.sourceIndex;
            if (i) return i;
            if (n)
                for (; n = n.nextSibling;)
                    if (n === t) return -1;
            return e ? 1 : -1
        }

        function fe(t) {
            return function (e) {
                return "input" === e.nodeName.toLowerCase() && e.type === t
            }
        }

        function he(n) {
            return function (e) {
                var t = e.nodeName.toLowerCase();
                return ("input" === t || "button" === t) && e.type === n
            }
        }

        function ve(t) {
            return function (e) {
                return "form" in e ? e.parentNode && !1 === e.disabled ? "label" in e ? "label" in e.parentNode ? e.parentNode.disabled === t : e.disabled === t : e.isDisabled === t || e.isDisabled !== !t && se(e) === t : e.disabled === t : "label" in e && e.disabled === t
            }
        }

        function ge(s) {
            return ce(function (r) {
                return r = +r, ce(function (e, t) {
                    for (var n, i = s([], e.length, r), o = i.length; o--;) e[n = i[o]] && (e[n] = !(t[n] = e[n]))
                })
            })
        }

        function me(e) {
            return e && void 0 !== e.getElementsByTagName && e
        }
        for (e in f = ae.support = {}, s = ae.isXML = function (e) {
                var t = e.namespaceURI,
                    n = (e.ownerDocument || e).documentElement;
                return !J.test(t || n && n.nodeName || "HTML")
            }, k = ae.setDocument = function (e) {
                var t, n, i = e ? e.ownerDocument || e : w;
                return i !== T && 9 === i.nodeType && i.documentElement && (a = (T = i).documentElement, C = !s(T), w !== T && (n = T.defaultView) && n.top !== n && (n.addEventListener ? n.addEventListener("unload", o, !1) : n.attachEvent && n.attachEvent("onunload", o)), f.attributes = de(function (e) {
                    return e.className = "i", !e.getAttribute("className")
                }), f.getElementsByTagName = de(function (e) {
                    return e.appendChild(T.createComment("")), !e.getElementsByTagName("*").length
                }), f.getElementsByClassName = ee.test(T.getElementsByClassName), f.getById = de(function (e) {
                    return a.appendChild(e).id = S, !T.getElementsByName || !T.getElementsByName(S).length
                }), f.getById ? (b.filter.ID = function (e) {
                    var t = e.replace(ie, u);
                    return function (e) {
                        return e.getAttribute("id") === t
                    }
                }, b.find.ID = function (e, t) {
                    if (void 0 !== t.getElementById && C) {
                        var n = t.getElementById(e);
                        return n ? [n] : []
                    }
                }) : (b.filter.ID = function (e) {
                    var n = e.replace(ie, u);
                    return function (e) {
                        var t = void 0 !== e.getAttributeNode && e.getAttributeNode("id");
                        return t && t.value === n
                    }
                }, b.find.ID = function (e, t) {
                    if (void 0 !== t.getElementById && C) {
                        var n, i, o, r = t.getElementById(e);
                        if (r) {
                            if ((n = r.getAttributeNode("id")) && n.value === e) return [r];
                            for (o = t.getElementsByName(e), i = 0; r = o[i++];)
                                if ((n = r.getAttributeNode("id")) && n.value === e) return [r]
                        }
                        return []
                    }
                }), b.find.TAG = f.getElementsByTagName ? function (e, t) {
                    return void 0 !== t.getElementsByTagName ? t.getElementsByTagName(e) : f.qsa ? t.querySelectorAll(e) : void 0
                } : function (e, t) {
                    var n, i = [],
                        o = 0,
                        r = t.getElementsByTagName(e);
                    if ("*" !== e) return r;
                    for (; n = r[o++];) 1 === n.nodeType && i.push(n);
                    return i
                }, b.find.CLASS = f.getElementsByClassName && function (e, t) {
                    if (void 0 !== t.getElementsByClassName && C) return t.getElementsByClassName(e)
                }, d = [], g = [], (f.qsa = ee.test(T.querySelectorAll)) && (de(function (e) {
                    a.appendChild(e).innerHTML = "<a id='" + S + "'></a><select id='" + S + "-\r\\' msallowcapture=''><option selected=''></option></select>", e.querySelectorAll("[msallowcapture^='']").length && g.push("[*^$]=" + M + "*(?:''|\"\")"), e.querySelectorAll("[selected]").length || g.push("\\[" + M + "*(?:value|" + P + ")"), e.querySelectorAll("[id~=" + S + "-]").length || g.push("~="), e.querySelectorAll(":checked").length || g.push(":checked"), e.querySelectorAll("a#" + S + "+*").length || g.push(".#.+[+~]")
                }), de(function (e) {
                    e.innerHTML = "<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";
                    var t = T.createElement("input");
                    t.setAttribute("type", "hidden"), e.appendChild(t).setAttribute("name", "D"), e.querySelectorAll("[name=d]").length && g.push("name" + M + "*[*^$|!~]?="), 2 !== e.querySelectorAll(":enabled").length && g.push(":enabled", ":disabled"), a.appendChild(e).disabled = !0, 2 !== e.querySelectorAll(":disabled").length && g.push(":enabled", ":disabled"), e.querySelectorAll("*,:x"), g.push(",.*:")
                })), (f.matchesSelector = ee.test(m = a.matches || a.webkitMatchesSelector || a.mozMatchesSelector || a.oMatchesSelector || a.msMatchesSelector)) && de(function (e) {
                    f.disconnectedMatch = m.call(e, "*"), m.call(e, "[s!='']:x"), d.push("!=", R)
                }), g = g.length && new RegExp(g.join("|")), d = d.length && new RegExp(d.join("|")), t = ee.test(a.compareDocumentPosition), y = t || ee.test(a.contains) ? function (e, t) {
                    var n = 9 === e.nodeType ? e.documentElement : e,
                        i = t && t.parentNode;
                    return e === i || !(!i || 1 !== i.nodeType || !(n.contains ? n.contains(i) : e.compareDocumentPosition && 16 & e.compareDocumentPosition(i)))
                } : function (e, t) {
                    if (t)
                        for (; t = t.parentNode;)
                            if (t === e) return !0;
                    return !1
                }, O = t ? function (e, t) {
                    if (e === t) return c = !0, 0;
                    var n = !e.compareDocumentPosition - !t.compareDocumentPosition;
                    return n || (1 & (n = (e.ownerDocument || e) === (t.ownerDocument || t) ? e.compareDocumentPosition(t) : 1) || !f.sortDetached && t.compareDocumentPosition(e) === n ? e === T || e.ownerDocument === w && y(w, e) ? -1 : t === T || t.ownerDocument === w && y(w, t) ? 1 : l ? q(l, e) - q(l, t) : 0 : 4 & n ? -1 : 1)
                } : function (e, t) {
                    if (e === t) return c = !0, 0;
                    var n, i = 0,
                        o = e.parentNode,
                        r = t.parentNode,
                        s = [e],
                        a = [t];
                    if (!o || !r) return e === T ? -1 : t === T ? 1 : o ? -1 : r ? 1 : l ? q(l, e) - q(l, t) : 0;
                    if (o === r) return pe(e, t);
                    for (n = e; n = n.parentNode;) s.unshift(n);
                    for (n = t; n = n.parentNode;) a.unshift(n);
                    for (; s[i] === a[i];) i++;
                    return i ? pe(s[i], a[i]) : s[i] === w ? -1 : a[i] === w ? 1 : 0
                }), T
            }, ae.matches = function (e, t) {
                return ae(e, null, null, t)
            }, ae.matchesSelector = function (e, t) {
                if ((e.ownerDocument || e) !== T && k(e), f.matchesSelector && C && !j[t + " "] && (!d || !d.test(t)) && (!g || !g.test(t))) try {
                    var n = m.call(e, t);
                    if (n || f.disconnectedMatch || e.document && 11 !== e.document.nodeType) return n
                } catch (e) {
                    j(t, !0)
                }
                return 0 < ae(t, T, null, [e]).length
            }, ae.contains = function (e, t) {
                return (e.ownerDocument || e) !== T && k(e), y(e, t)
            }, ae.attr = function (e, t) {
                (e.ownerDocument || e) !== T && k(e);
                var n = b.attrHandle[t.toLowerCase()],
                    i = n && H.call(b.attrHandle, t.toLowerCase()) ? n(e, t, !C) : void 0;
                return void 0 !== i ? i : f.attributes || !C ? e.getAttribute(t) : (i = e.getAttributeNode(t)) && i.specified ? i.value : null
            }, ae.escape = function (e) {
                return (e + "").replace(oe, re)
            }, ae.error = function (e) {
                throw new Error("Syntax error, unrecognized expression: " + e)
            }, ae.uniqueSort = function (e) {
                var t, n = [],
                    i = 0,
                    o = 0;
                if (c = !f.detectDuplicates, l = !f.sortStable && e.slice(0), e.sort(O), c) {
                    for (; t = e[o++];) t === e[o] && (i = n.push(o));
                    for (; i--;) e.splice(n[i], 1)
                }
                return l = null, e
            }, r = ae.getText = function (e) {
                var t, n = "",
                    i = 0,
                    o = e.nodeType;
                if (o) {
                    if (1 === o || 9 === o || 11 === o) {
                        if ("string" == typeof e.textContent) return e.textContent;
                        for (e = e.firstChild; e; e = e.nextSibling) n += r(e)
                    } else if (3 === o || 4 === o) return e.nodeValue
                } else
                    for (; t = e[i++];) n += r(t);
                return n
            }, (b = ae.selectors = {
                cacheLength: 50,
                createPseudo: ce,
                match: V,
                attrHandle: {},
                find: {},
                relative: {
                    ">": {
                        dir: "parentNode",
                        first: !0
                    },
                    " ": {
                        dir: "parentNode"
                    },
                    "+": {
                        dir: "previousSibling",
                        first: !0
                    },
                    "~": {
                        dir: "previousSibling"
                    }
                },
                preFilter: {
                    ATTR: function (e) {
                        return e[1] = e[1].replace(ie, u), e[3] = (e[3] || e[4] || e[5] || "").replace(ie, u), "~=" === e[2] && (e[3] = " " + e[3] + " "), e.slice(0, 4)
                    },
                    CHILD: function (e) {
                        return e[1] = e[1].toLowerCase(), "nth" === e[1].slice(0, 3) ? (e[3] || ae.error(e[0]), e[4] = +(e[4] ? e[5] + (e[6] || 1) : 2 * ("even" === e[3] || "odd" === e[3])), e[5] = +(e[7] + e[8] || "odd" === e[3])) : e[3] && ae.error(e[0]), e
                    },
                    PSEUDO: function (e) {
                        var t, n = !e[6] && e[2];
                        return V.CHILD.test(e[0]) ? null : (e[3] ? e[2] = e[4] || e[5] || "" : n && Y.test(n) && (t = h(n, !0)) && (t = n.indexOf(")", n.length - t) - n.length) && (e[0] = e[0].slice(0, t), e[2] = n.slice(0, t)), e.slice(0, 3))
                    }
                },
                filter: {
                    TAG: function (e) {
                        var t = e.replace(ie, u).toLowerCase();
                        return "*" === e ? function () {
                            return !0
                        } : function (e) {
                            return e.nodeName && e.nodeName.toLowerCase() === t
                        }
                    },
                    CLASS: function (e) {
                        var t = E[e + " "];
                        return t || (t = new RegExp("(^|" + M + ")" + e + "(" + M + "|$)")) && E(e, function (e) {
                            return t.test("string" == typeof e.className && e.className || void 0 !== e.getAttribute && e.getAttribute("class") || "")
                        })
                    },
                    ATTR: function (n, i, o) {
                        return function (e) {
                            var t = ae.attr(e, n);
                            return null == t ? "!=" === i : !i || (t += "", "=" === i ? t === o : "!=" === i ? t !== o : "^=" === i ? o && 0 === t.indexOf(o) : "*=" === i ? o && -1 < t.indexOf(o) : "$=" === i ? o && t.slice(-o.length) === o : "~=" === i ? -1 < (" " + t.replace(B, " ") + " ").indexOf(o) : "|=" === i && (t === o || t.slice(0, o.length + 1) === o + "-"))
                        }
                    },
                    CHILD: function (h, e, t, v, g) {
                        var m = "nth" !== h.slice(0, 3),
                            y = "last" !== h.slice(-4),
                            w = "of-type" === e;
                        return 1 === v && 0 === g ? function (e) {
                            return !!e.parentNode
                        } : function (e, t, n) {
                            var i, o, r, s, a, l, c = m != y ? "nextSibling" : "previousSibling",
                                d = e.parentNode,
                                u = w && e.nodeName.toLowerCase(),
                                p = !n && !w,
                                f = !1;
                            if (d) {
                                if (m) {
                                    for (; c;) {
                                        for (s = e; s = s[c];)
                                            if (w ? s.nodeName.toLowerCase() === u : 1 === s.nodeType) return !1;
                                        l = c = "only" === h && !l && "nextSibling"
                                    }
                                    return !0
                                }
                                if (l = [y ? d.firstChild : d.lastChild], y && p) {
                                    for (f = (a = (i = (o = (r = (s = d)[S] || (s[S] = {}))[s.uniqueID] || (r[s.uniqueID] = {}))[h] || [])[0] === $ && i[1]) && i[2], s = a && d.childNodes[a]; s = ++a && s && s[c] || (f = a = 0) || l.pop();)
                                        if (1 === s.nodeType && ++f && s === e) {
                                            o[h] = [$, a, f];
                                            break
                                        }
                                } else if (p && (f = a = (i = (o = (r = (s = e)[S] || (s[S] = {}))[s.uniqueID] || (r[s.uniqueID] = {}))[h] || [])[0] === $ && i[1]), !1 === f)
                                    for (;
                                        (s = ++a && s && s[c] || (f = a = 0) || l.pop()) && ((w ? s.nodeName.toLowerCase() !== u : 1 !== s.nodeType) || !++f || (p && ((o = (r = s[S] || (s[S] = {}))[s.uniqueID] || (r[s.uniqueID] = {}))[h] = [$, f]), s !== e)););
                                return (f -= g) === v || f % v == 0 && 0 <= f / v
                            }
                        }
                    },
                    PSEUDO: function (e, r) {
                        var t, s = b.pseudos[e] || b.setFilters[e.toLowerCase()] || ae.error("unsupported pseudo: " + e);
                        return s[S] ? s(r) : 1 < s.length ? (t = [e, e, "", r], b.setFilters.hasOwnProperty(e.toLowerCase()) ? ce(function (e, t) {
                            for (var n, i = s(e, r), o = i.length; o--;) e[n = q(e, i[o])] = !(t[n] = i[o])
                        }) : function (e) {
                            return s(e, 0, t)
                        }) : s
                    }
                },
                pseudos: {
                    not: ce(function (e) {
                        var i = [],
                            o = [],
                            a = p(e.replace(F, "$1"));
                        return a[S] ? ce(function (e, t, n, i) {
                            for (var o, r = a(e, null, i, []), s = e.length; s--;)(o = r[s]) && (e[s] = !(t[s] = o))
                        }) : function (e, t, n) {
                            return i[0] = e, a(i, null, n, o), i[0] = null, !o.pop()
                        }
                    }),
                    has: ce(function (t) {
                        return function (e) {
                            return 0 < ae(t, e).length
                        }
                    }),
                    contains: ce(function (t) {
                        return t = t.replace(ie, u),
                            function (e) {
                                return -1 < (e.textContent || r(e)).indexOf(t)
                            }
                    }),
                    lang: ce(function (n) {
                        return Q.test(n || "") || ae.error("unsupported lang: " + n), n = n.replace(ie, u).toLowerCase(),
                            function (e) {
                                var t;
                                do {
                                    if (t = C ? e.lang : e.getAttribute("xml:lang") || e.getAttribute("lang")) return (t = t.toLowerCase()) === n || 0 === t.indexOf(n + "-")
                                } while ((e = e.parentNode) && 1 === e.nodeType);
                                return !1
                            }
                    }),
                    target: function (e) {
                        var t = n.location && n.location.hash;
                        return t && t.slice(1) === e.id
                    },
                    root: function (e) {
                        return e === a
                    },
                    focus: function (e) {
                        return e === T.activeElement && (!T.hasFocus || T.hasFocus()) && !!(e.type || e.href || ~e.tabIndex)
                    },
                    enabled: ve(!1),
                    disabled: ve(!0),
                    checked: function (e) {
                        var t = e.nodeName.toLowerCase();
                        return "input" === t && !!e.checked || "option" === t && !!e.selected
                    },
                    selected: function (e) {
                        return e.parentNode && e.parentNode.selectedIndex, !0 === e.selected
                    },
                    empty: function (e) {
                        for (e = e.firstChild; e; e = e.nextSibling)
                            if (e.nodeType < 6) return !1;
                        return !0
                    },
                    parent: function (e) {
                        return !b.pseudos.empty(e)
                    },
                    header: function (e) {
                        return Z.test(e.nodeName)
                    },
                    input: function (e) {
                        return K.test(e.nodeName)
                    },
                    button: function (e) {
                        var t = e.nodeName.toLowerCase();
                        return "input" === t && "button" === e.type || "button" === t
                    },
                    text: function (e) {
                        var t;
                        return "input" === e.nodeName.toLowerCase() && "text" === e.type && (null == (t = e.getAttribute("type")) || "text" === t.toLowerCase())
                    },
                    first: ge(function () {
                        return [0]
                    }),
                    last: ge(function (e, t) {
                        return [t - 1]
                    }),
                    eq: ge(function (e, t, n) {
                        return [n < 0 ? n + t : n]
                    }),
                    even: ge(function (e, t) {
                        for (var n = 0; n < t; n += 2) e.push(n);
                        return e
                    }),
                    odd: ge(function (e, t) {
                        for (var n = 1; n < t; n += 2) e.push(n);
                        return e
                    }),
                    lt: ge(function (e, t, n) {
                        for (var i = n < 0 ? n + t : t < n ? t : n; 0 <= --i;) e.push(i);
                        return e
                    }),
                    gt: ge(function (e, t, n) {
                        for (var i = n < 0 ? n + t : n; ++i < t;) e.push(i);
                        return e
                    })
                }
            }).pseudos.nth = b.pseudos.eq, {
                radio: !0,
                checkbox: !0,
                file: !0,
                password: !0,
                image: !0
            }) b.pseudos[e] = fe(e);
        for (e in {
                submit: !0,
                reset: !0
            }) b.pseudos[e] = he(e);

        function ye() {}

        function we(e) {
            for (var t = 0, n = e.length, i = ""; t < n; t++) i += e[t].value;
            return i
        }

        function be(a, e, t) {
            var l = e.dir,
                c = e.next,
                d = c || l,
                u = t && "parentNode" === d,
                p = i++;
            return e.first ? function (e, t, n) {
                for (; e = e[l];)
                    if (1 === e.nodeType || u) return a(e, t, n);
                return !1
            } : function (e, t, n) {
                var i, o, r, s = [$, p];
                if (n) {
                    for (; e = e[l];)
                        if ((1 === e.nodeType || u) && a(e, t, n)) return !0
                } else
                    for (; e = e[l];)
                        if (1 === e.nodeType || u)
                            if (o = (r = e[S] || (e[S] = {}))[e.uniqueID] || (r[e.uniqueID] = {}), c && c === e.nodeName.toLowerCase()) e = e[l] || e;
                            else {
                                if ((i = o[d]) && i[0] === $ && i[1] === p) return s[2] = i[2];
                                if ((o[d] = s)[2] = a(e, t, n)) return !0
                            } return !1
            }
        }

        function xe(o) {
            return 1 < o.length ? function (e, t, n) {
                for (var i = o.length; i--;)
                    if (!o[i](e, t, n)) return !1;
                return !0
            } : o[0]
        }

        function ke(e, t, n, i, o) {
            for (var r, s = [], a = 0, l = e.length, c = null != t; a < l; a++)(r = e[a]) && (n && !n(r, i, o) || (s.push(r), c && t.push(a)));
            return s
        }

        function Te(f, h, v, g, m, e) {
            return g && !g[S] && (g = Te(g)), m && !m[S] && (m = Te(m, e)), ce(function (e, t, n, i) {
                var o, r, s, a = [],
                    l = [],
                    c = t.length,
                    d = e || function (e, t, n) {
                        for (var i = 0, o = t.length; i < o; i++) ae(e, t[i], n);
                        return n
                    }(h || "*", n.nodeType ? [n] : n, []),
                    u = !f || !e && h ? d : ke(d, a, f, n, i),
                    p = v ? m || (e ? f : c || g) ? [] : t : u;
                if (v && v(u, p, n, i), g)
                    for (o = ke(p, l), g(o, [], n, i), r = o.length; r--;)(s = o[r]) && (p[l[r]] = !(u[l[r]] = s));
                if (e) {
                    if (m || f) {
                        if (m) {
                            for (o = [], r = p.length; r--;)(s = p[r]) && o.push(u[r] = s);
                            m(null, p = [], o, i)
                        }
                        for (r = p.length; r--;)(s = p[r]) && -1 < (o = m ? q(e, s) : a[r]) && (e[o] = !(t[o] = s))
                    }
                } else p = ke(p === t ? p.splice(c, p.length) : p), m ? m(null, t, p, i) : _.apply(t, p)
            })
        }

        function Ce(e) {
            for (var o, t, n, i = e.length, r = b.relative[e[0].type], s = r || b.relative[" "], a = r ? 1 : 0, l = be(function (e) {
                    return e === o
                }, s, !0), c = be(function (e) {
                    return -1 < q(o, e)
                }, s, !0), d = [function (e, t, n) {
                    var i = !r && (n || t !== x) || ((o = t).nodeType ? l(e, t, n) : c(e, t, n));
                    return o = null, i
                }]; a < i; a++)
                if (t = b.relative[e[a].type]) d = [be(xe(d), t)];
                else {
                    if ((t = b.filter[e[a].type].apply(null, e[a].matches))[S]) {
                        for (n = ++a; n < i && !b.relative[e[n].type]; n++);
                        return Te(1 < a && xe(d), 1 < a && we(e.slice(0, a - 1).concat({
                            value: " " === e[a - 2].type ? "*" : ""
                        })).replace(F, "$1"), t, a < n && Ce(e.slice(a, n)), n < i && Ce(e = e.slice(n)), n < i && we(e))
                    }
                    d.push(t)
                } return xe(d)
        }
        return ye.prototype = b.filters = b.pseudos, b.setFilters = new ye, h = ae.tokenize = function (e, t) {
            var n, i, o, r, s, a, l, c = N[e + " "];
            if (c) return t ? 0 : c.slice(0);
            for (s = e, a = [], l = b.preFilter; s;) {
                for (r in n && !(i = G.exec(s)) || (i && (s = s.slice(i[0].length) || s), a.push(o = [])), n = !1, (i = U.exec(s)) && (n = i.shift(), o.push({
                        value: n,
                        type: i[0].replace(F, " ")
                    }), s = s.slice(n.length)), b.filter) !(i = V[r].exec(s)) || l[r] && !(i = l[r](i)) || (n = i.shift(), o.push({
                    value: n,
                    type: r,
                    matches: i
                }), s = s.slice(n.length));
                if (!n) break
            }
            return t ? s.length : s ? ae.error(e) : N(e, a).slice(0)
        }, p = ae.compile = function (e, t) {
            var n, g, m, y, w, i, o = [],
                r = [],
                s = A[e + " "];
            if (!s) {
                for (n = (t = t || h(e)).length; n--;)(s = Ce(t[n]))[S] ? o.push(s) : r.push(s);
                (s = A(e, (g = r, y = 0 < (m = o).length, w = 0 < g.length, i = function (e, t, n, i, o) {
                    var r, s, a, l = 0,
                        c = "0",
                        d = e && [],
                        u = [],
                        p = x,
                        f = e || w && b.find.TAG("*", o),
                        h = $ += null == p ? 1 : Math.random() || .1,
                        v = f.length;
                    for (o && (x = t === T || t || o); c !== v && null != (r = f[c]); c++) {
                        if (w && r) {
                            for (s = 0, t || r.ownerDocument === T || (k(r), n = !C); a = g[s++];)
                                if (a(r, t || T, n)) {
                                    i.push(r);
                                    break
                                } o && ($ = h)
                        }
                        y && ((r = !a && r) && l--, e && d.push(r))
                    }
                    if (l += c, y && c !== l) {
                        for (s = 0; a = m[s++];) a(d, u, t, n);
                        if (e) {
                            if (0 < l)
                                for (; c--;) d[c] || u[c] || (u[c] = D.call(i));
                            u = ke(u)
                        }
                        _.apply(i, u), o && !e && 0 < u.length && 1 < l + m.length && ae.uniqueSort(i)
                    }
                    return o && ($ = h, x = p), d
                }, y ? ce(i) : i))).selector = e
            }
            return s
        }, v = ae.select = function (e, t, n, i) {
            var o, r, s, a, l, c = "function" == typeof e && e,
                d = !i && h(e = c.selector || e);
            if (n = n || [], 1 === d.length) {
                if (2 < (r = d[0] = d[0].slice(0)).length && "ID" === (s = r[0]).type && 9 === t.nodeType && C && b.relative[r[1].type]) {
                    if (!(t = (b.find.ID(s.matches[0].replace(ie, u), t) || [])[0])) return n;
                    c && (t = t.parentNode), e = e.slice(r.shift().value.length)
                }
                for (o = V.needsContext.test(e) ? 0 : r.length; o-- && (s = r[o], !b.relative[a = s.type]);)
                    if ((l = b.find[a]) && (i = l(s.matches[0].replace(ie, u), ne.test(r[0].type) && me(t.parentNode) || t))) {
                        if (r.splice(o, 1), !(e = i.length && we(r))) return _.apply(n, i), n;
                        break
                    }
            }
            return (c || p(e, d))(i, t, !C, n, !t || ne.test(e) && me(t.parentNode) || t), n
        }, f.sortStable = S.split("").sort(O).join("") === S, f.detectDuplicates = !!c, k(), f.sortDetached = de(function (e) {
            return 1 & e.compareDocumentPosition(T.createElement("fieldset"))
        }), de(function (e) {
            return e.innerHTML = "<a href='#'></a>", "#" === e.firstChild.getAttribute("href")
        }) || ue("type|href|height|width", function (e, t, n) {
            if (!n) return e.getAttribute(t, "type" === t.toLowerCase() ? 1 : 2)
        }), f.attributes && de(function (e) {
            return e.innerHTML = "<input/>", e.firstChild.setAttribute("value", ""), "" === e.firstChild.getAttribute("value")
        }) || ue("value", function (e, t, n) {
            if (!n && "input" === e.nodeName.toLowerCase()) return e.defaultValue
        }), de(function (e) {
            return null == e.getAttribute("disabled")
        }) || ue(P, function (e, t, n) {
            var i;
            if (!n) return !0 === e[t] ? t.toLowerCase() : (i = e.getAttributeNode(t)) && i.specified ? i.value : null
        }), ae
    }(T);

    function k(e, t, n) {
        for (var i = [], o = void 0 !== n;
            (e = e[t]) && 9 !== e.nodeType;)
            if (1 === e.nodeType) {
                if (o && S(e).is(n)) break;
                i.push(e)
            } return i
    }

    function $(e, t) {
        for (var n = []; e; e = e.nextSibling) 1 === e.nodeType && e !== t && n.push(e);
        return n
    }
    S.find = h, S.expr = h.selectors, S.expr[":"] = S.expr.pseudos, S.uniqueSort = S.unique = h.uniqueSort, S.text = h.getText, S.isXMLDoc = h.isXML, S.contains = h.contains, S.escapeSelector = h.escape;
    var E = S.expr.match.needsContext;

    function N(e, t) {
        return e.nodeName && e.nodeName.toLowerCase() === t.toLowerCase()
    }
    var A = /^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;

    function j(e, n, i) {
        return w(n) ? S.grep(e, function (e, t) {
            return !!n.call(e, t, e) !== i
        }) : n.nodeType ? S.grep(e, function (e) {
            return e === n !== i
        }) : "string" != typeof n ? S.grep(e, function (e) {
            return -1 < o.call(n, e) !== i
        }) : S.filter(n, e, i)
    }
    S.filter = function (e, t, n) {
        var i = t[0];
        return n && (e = ":not(" + e + ")"), 1 === t.length && 1 === i.nodeType ? S.find.matchesSelector(i, e) ? [i] : [] : S.find.matches(e, S.grep(t, function (e) {
            return 1 === e.nodeType
        }))
    }, S.fn.extend({
        find: function (e) {
            var t, n, i = this.length,
                o = this;
            if ("string" != typeof e) return this.pushStack(S(e).filter(function () {
                for (t = 0; t < i; t++)
                    if (S.contains(o[t], this)) return !0
            }));
            for (n = this.pushStack([]), t = 0; t < i; t++) S.find(e, o[t], n);
            return 1 < i ? S.uniqueSort(n) : n
        },
        filter: function (e) {
            return this.pushStack(j(this, e || [], !1))
        },
        not: function (e) {
            return this.pushStack(j(this, e || [], !0))
        },
        is: function (e) {
            return !!j(this, "string" == typeof e && E.test(e) ? S(e) : e || [], !1).length
        }
    });
    var O, H = /^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;
    (S.fn.init = function (e, t, n) {
        var i, o;
        if (!e) return this;
        if (n = n || O, "string" != typeof e) return e.nodeType ? (this[0] = e, this.length = 1, this) : w(e) ? void 0 !== n.ready ? n.ready(e) : e(S) : S.makeArray(e, this);
        if (!(i = "<" === e[0] && ">" === e[e.length - 1] && 3 <= e.length ? [null, e, null] : H.exec(e)) || !i[1] && t) return !t || t.jquery ? (t || n).find(e) : this.constructor(t).find(e);
        if (i[1]) {
            if (t = t instanceof S ? t[0] : t, S.merge(this, S.parseHTML(i[1], t && t.nodeType ? t.ownerDocument || t : C, !0)), A.test(i[1]) && S.isPlainObject(t))
                for (i in t) w(this[i]) ? this[i](t[i]) : this.attr(i, t[i]);
            return this
        }
        return (o = C.getElementById(i[2])) && (this[0] = o, this.length = 1), this
    }).prototype = S.fn, O = S(C);
    var D = /^(?:parents|prev(?:Until|All))/,
        I = {
            children: !0,
            contents: !0,
            next: !0,
            prev: !0
        };

    function _(e, t) {
        for (;
            (e = e[t]) && 1 !== e.nodeType;);
        return e
    }
    S.fn.extend({
        has: function (e) {
            var t = S(e, this),
                n = t.length;
            return this.filter(function () {
                for (var e = 0; e < n; e++)
                    if (S.contains(this, t[e])) return !0
            })
        },
        closest: function (e, t) {
            var n, i = 0,
                o = this.length,
                r = [],
                s = "string" != typeof e && S(e);
            if (!E.test(e))
                for (; i < o; i++)
                    for (n = this[i]; n && n !== t; n = n.parentNode)
                        if (n.nodeType < 11 && (s ? -1 < s.index(n) : 1 === n.nodeType && S.find.matchesSelector(n, e))) {
                            r.push(n);
                            break
                        } return this.pushStack(1 < r.length ? S.uniqueSort(r) : r)
        },
        index: function (e) {
            return e ? "string" == typeof e ? o.call(S(e), this[0]) : o.call(this, e.jquery ? e[0] : e) : this[0] && this[0].parentNode ? this.first().prevAll().length : -1
        },
        add: function (e, t) {
            return this.pushStack(S.uniqueSort(S.merge(this.get(), S(e, t))))
        },
        addBack: function (e) {
            return this.add(null == e ? this.prevObject : this.prevObject.filter(e))
        }
    }), S.each({
        parent: function (e) {
            var t = e.parentNode;
            return t && 11 !== t.nodeType ? t : null
        },
        parents: function (e) {
            return k(e, "parentNode")
        },
        parentsUntil: function (e, t, n) {
            return k(e, "parentNode", n)
        },
        next: function (e) {
            return _(e, "nextSibling")
        },
        prev: function (e) {
            return _(e, "previousSibling")
        },
        nextAll: function (e) {
            return k(e, "nextSibling")
        },
        prevAll: function (e) {
            return k(e, "previousSibling")
        },
        nextUntil: function (e, t, n) {
            return k(e, "nextSibling", n)
        },
        prevUntil: function (e, t, n) {
            return k(e, "previousSibling", n)
        },
        siblings: function (e) {
            return $((e.parentNode || {}).firstChild, e)
        },
        children: function (e) {
            return $(e.firstChild)
        },
        contents: function (e) {
            return void 0 !== e.contentDocument ? e.contentDocument : (N(e, "template") && (e = e.content || e), S.merge([], e.childNodes))
        }
    }, function (i, o) {
        S.fn[i] = function (e, t) {
            var n = S.map(this, o, e);
            return "Until" !== i.slice(-5) && (t = e), t && "string" == typeof t && (n = S.filter(t, n)), 1 < this.length && (I[i] || S.uniqueSort(n), D.test(i) && n.reverse()), this.pushStack(n)
        }
    });
    var L = /[^\x20\t\r\n\f]+/g;

    function q(e) {
        return e
    }

    function P(e) {
        throw e
    }

    function M(e, t, n, i) {
        var o;
        try {
            e && w(o = e.promise) ? o.call(e).done(t).fail(n) : e && w(o = e.then) ? o.call(e, t, n) : t.apply(void 0, [e].slice(i))
        } catch (e) {
            n.apply(void 0, [e])
        }
    }
    S.Callbacks = function (i) {
        var e, n;

        function o() {
            for (a = a || i.once, s = r = !0; c.length; d = -1)
                for (t = c.shift(); ++d < l.length;) !1 === l[d].apply(t[0], t[1]) && i.stopOnFalse && (d = l.length, t = !1);
            i.memory || (t = !1), r = !1, a && (l = t ? [] : "")
        }
        i = "string" == typeof i ? (e = i, n = {}, S.each(e.match(L) || [], function (e, t) {
            n[t] = !0
        }), n) : S.extend({}, i);
        var r, t, s, a, l = [],
            c = [],
            d = -1,
            u = {
                add: function () {
                    return l && (t && !r && (d = l.length - 1, c.push(t)), function n(e) {
                        S.each(e, function (e, t) {
                            w(t) ? i.unique && u.has(t) || l.push(t) : t && t.length && "string" !== x(t) && n(t)
                        })
                    }(arguments), t && !r && o()), this
                },
                remove: function () {
                    return S.each(arguments, function (e, t) {
                        for (var n; - 1 < (n = S.inArray(t, l, n));) l.splice(n, 1), n <= d && d--
                    }), this
                },
                has: function (e) {
                    return e ? -1 < S.inArray(e, l) : 0 < l.length
                },
                empty: function () {
                    return l = l && [], this
                },
                disable: function () {
                    return a = c = [], l = t = "", this
                },
                disabled: function () {
                    return !l
                },
                lock: function () {
                    return a = c = [], t || r || (l = t = ""), this
                },
                locked: function () {
                    return !!a
                },
                fireWith: function (e, t) {
                    return a || (t = [e, (t = t || []).slice ? t.slice() : t], c.push(t), r || o()), this
                },
                fire: function () {
                    return u.fireWith(this, arguments), this
                },
                fired: function () {
                    return !!s
                }
            };
        return u
    }, S.extend({
        Deferred: function (e) {
            var r = [
                    ["notify", "progress", S.Callbacks("memory"), S.Callbacks("memory"), 2],
                    ["resolve", "done", S.Callbacks("once memory"), S.Callbacks("once memory"), 0, "resolved"],
                    ["reject", "fail", S.Callbacks("once memory"), S.Callbacks("once memory"), 1, "rejected"]
                ],
                o = "pending",
                s = {
                    state: function () {
                        return o
                    },
                    always: function () {
                        return a.done(arguments).fail(arguments), this
                    },
                    catch: function (e) {
                        return s.then(null, e)
                    },
                    pipe: function () {
                        var o = arguments;
                        return S.Deferred(function (i) {
                            S.each(r, function (e, t) {
                                var n = w(o[t[4]]) && o[t[4]];
                                a[t[1]](function () {
                                    var e = n && n.apply(this, arguments);
                                    e && w(e.promise) ? e.promise().progress(i.notify).done(i.resolve).fail(i.reject) : i[t[0] + "With"](this, n ? [e] : arguments)
                                })
                            }), o = null
                        }).promise()
                    },
                    then: function (t, n, i) {
                        var l = 0;

                        function c(o, r, s, a) {
                            return function () {
                                function e() {
                                    var e, t;
                                    if (!(o < l)) {
                                        if ((e = s.apply(n, i)) === r.promise()) throw new TypeError("Thenable self-resolution");
                                        t = e && ("object" == typeof e || "function" == typeof e) && e.then, w(t) ? a ? t.call(e, c(l, r, q, a), c(l, r, P, a)) : (l++, t.call(e, c(l, r, q, a), c(l, r, P, a), c(l, r, q, r.notifyWith))) : (s !== q && (n = void 0, i = [e]), (a || r.resolveWith)(n, i))
                                    }
                                }
                                var n = this,
                                    i = arguments,
                                    t = a ? e : function () {
                                        try {
                                            e()
                                        } catch (e) {
                                            S.Deferred.exceptionHook && S.Deferred.exceptionHook(e, t.stackTrace), l <= o + 1 && (s !== P && (n = void 0, i = [e]), r.rejectWith(n, i))
                                        }
                                    };
                                o ? t() : (S.Deferred.getStackHook && (t.stackTrace = S.Deferred.getStackHook()), T.setTimeout(t))
                            }
                        }
                        return S.Deferred(function (e) {
                            r[0][3].add(c(0, e, w(i) ? i : q, e.notifyWith)), r[1][3].add(c(0, e, w(t) ? t : q)), r[2][3].add(c(0, e, w(n) ? n : P))
                        }).promise()
                    },
                    promise: function (e) {
                        return null != e ? S.extend(e, s) : s
                    }
                },
                a = {};
            return S.each(r, function (e, t) {
                var n = t[2],
                    i = t[5];
                s[t[1]] = n.add, i && n.add(function () {
                    o = i
                }, r[3 - e][2].disable, r[3 - e][3].disable, r[0][2].lock, r[0][3].lock), n.add(t[3].fire), a[t[0]] = function () {
                    return a[t[0] + "With"](this === a ? void 0 : this, arguments), this
                }, a[t[0] + "With"] = n.fireWith
            }), s.promise(a), e && e.call(a, a), a
        },
        when: function (e) {
            function t(t) {
                return function (e) {
                    o[t] = this, r[t] = 1 < arguments.length ? a.call(arguments) : e, --n || s.resolveWith(o, r)
                }
            }
            var n = arguments.length,
                i = n,
                o = Array(i),
                r = a.call(arguments),
                s = S.Deferred();
            if (n <= 1 && (M(e, s.done(t(i)).resolve, s.reject, !n), "pending" === s.state() || w(r[i] && r[i].then))) return s.then();
            for (; i--;) M(r[i], t(i), s.reject);
            return s.promise()
        }
    });
    var W = /^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;
    S.Deferred.exceptionHook = function (e, t) {
        T.console && T.console.warn && e && W.test(e.name) && T.console.warn("jQuery.Deferred exception: " + e.message, e.stack, t)
    }, S.readyException = function (e) {
        T.setTimeout(function () {
            throw e
        })
    };
    var z = S.Deferred();

    function R() {
        C.removeEventListener("DOMContentLoaded", R), T.removeEventListener("load", R), S.ready()
    }
    S.fn.ready = function (e) {
        return z.then(e).catch(function (e) {
            S.readyException(e)
        }), this
    }, S.extend({
        isReady: !1,
        readyWait: 1,
        ready: function (e) {
            (!0 === e ? --S.readyWait : S.isReady) || (S.isReady = !0) !== e && 0 < --S.readyWait || z.resolveWith(C, [S])
        }
    }), S.ready.then = z.then, "complete" === C.readyState || "loading" !== C.readyState && !C.documentElement.doScroll ? T.setTimeout(S.ready) : (C.addEventListener("DOMContentLoaded", R), T.addEventListener("load", R));
    var B = function (e, t, n, i, o, r, s) {
            var a = 0,
                l = e.length,
                c = null == n;
            if ("object" === x(n))
                for (a in o = !0, n) B(e, t, a, n[a], !0, r, s);
            else if (void 0 !== i && (o = !0, w(i) || (s = !0), c && (t = s ? (t.call(e, i), null) : (c = t, function (e, t, n) {
                    return c.call(S(e), n)
                })), t))
                for (; a < l; a++) t(e[a], n, s ? i : i.call(e[a], a, t(e[a], n)));
            return o ? e : c ? t.call(e) : l ? t(e[0], n) : r
        },
        F = /^-ms-/,
        G = /-([a-z])/g;

    function U(e, t) {
        return t.toUpperCase()
    }

    function X(e) {
        return e.replace(F, "ms-").replace(G, U)
    }

    function Y(e) {
        return 1 === e.nodeType || 9 === e.nodeType || !+e.nodeType
    }

    function Q() {
        this.expando = S.expando + Q.uid++
    }
    Q.uid = 1, Q.prototype = {
        cache: function (e) {
            var t = e[this.expando];
            return t || (t = {}, Y(e) && (e.nodeType ? e[this.expando] = t : Object.defineProperty(e, this.expando, {
                value: t,
                configurable: !0
            }))), t
        },
        set: function (e, t, n) {
            var i, o = this.cache(e);
            if ("string" == typeof t) o[X(t)] = n;
            else
                for (i in t) o[X(i)] = t[i];
            return o
        },
        get: function (e, t) {
            return void 0 === t ? this.cache(e) : e[this.expando] && e[this.expando][X(t)]
        },
        access: function (e, t, n) {
            return void 0 === t || t && "string" == typeof t && void 0 === n ? this.get(e, t) : (this.set(e, t, n), void 0 !== n ? n : t)
        },
        remove: function (e, t) {
            var n, i = e[this.expando];
            if (void 0 !== i) {
                if (void 0 !== t) {
                    n = (t = Array.isArray(t) ? t.map(X) : (t = X(t)) in i ? [t] : t.match(L) || []).length;
                    for (; n--;) delete i[t[n]]
                }
                void 0 !== t && !S.isEmptyObject(i) || (e.nodeType ? e[this.expando] = void 0 : delete e[this.expando])
            }
        },
        hasData: function (e) {
            var t = e[this.expando];
            return void 0 !== t && !S.isEmptyObject(t)
        }
    };
    var V = new Q,
        J = new Q,
        K = /^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,
        Z = /[A-Z]/g;

    function ee(e, t, n) {
        var i, o;
        if (void 0 === n && 1 === e.nodeType)
            if (i = "data-" + t.replace(Z, "-$&").toLowerCase(), "string" == typeof (n = e.getAttribute(i))) {
                try {
                    n = "true" === (o = n) || "false" !== o && ("null" === o ? null : o === +o + "" ? +o : K.test(o) ? JSON.parse(o) : o)
                } catch (e) {}
                J.set(e, t, n)
            } else n = void 0;
        return n
    }
    S.extend({
        hasData: function (e) {
            return J.hasData(e) || V.hasData(e)
        },
        data: function (e, t, n) {
            return J.access(e, t, n)
        },
        removeData: function (e, t) {
            J.remove(e, t)
        },
        _data: function (e, t, n) {
            return V.access(e, t, n)
        },
        _removeData: function (e, t) {
            V.remove(e, t)
        }
    }), S.fn.extend({
        data: function (n, e) {
            var t, i, o, r = this[0],
                s = r && r.attributes;
            if (void 0 !== n) return "object" == typeof n ? this.each(function () {
                J.set(this, n)
            }) : B(this, function (e) {
                var t;
                if (r && void 0 === e) return void 0 !== (t = J.get(r, n)) ? t : void 0 !== (t = ee(r, n)) ? t : void 0;
                this.each(function () {
                    J.set(this, n, e)
                })
            }, null, e, 1 < arguments.length, null, !0);
            if (this.length && (o = J.get(r), 1 === r.nodeType && !V.get(r, "hasDataAttrs"))) {
                for (t = s.length; t--;) s[t] && 0 === (i = s[t].name).indexOf("data-") && (i = X(i.slice(5)), ee(r, i, o[i]));
                V.set(r, "hasDataAttrs", !0)
            }
            return o
        },
        removeData: function (e) {
            return this.each(function () {
                J.remove(this, e)
            })
        }
    }), S.extend({
        queue: function (e, t, n) {
            var i;
            if (e) return t = (t || "fx") + "queue", i = V.get(e, t), n && (!i || Array.isArray(n) ? i = V.access(e, t, S.makeArray(n)) : i.push(n)), i || []
        },
        dequeue: function (e, t) {
            t = t || "fx";
            var n = S.queue(e, t),
                i = n.length,
                o = n.shift(),
                r = S._queueHooks(e, t);
            "inprogress" === o && (o = n.shift(), i--), o && ("fx" === t && n.unshift("inprogress"), delete r.stop, o.call(e, function () {
                S.dequeue(e, t)
            }, r)), !i && r && r.empty.fire()
        },
        _queueHooks: function (e, t) {
            var n = t + "queueHooks";
            return V.get(e, n) || V.access(e, n, {
                empty: S.Callbacks("once memory").add(function () {
                    V.remove(e, [t + "queue", n])
                })
            })
        }
    }), S.fn.extend({
        queue: function (t, n) {
            var e = 2;
            return "string" != typeof t && (n = t, t = "fx", e--), arguments.length < e ? S.queue(this[0], t) : void 0 === n ? this : this.each(function () {
                var e = S.queue(this, t, n);
                S._queueHooks(this, t), "fx" === t && "inprogress" !== e[0] && S.dequeue(this, t)
            })
        },
        dequeue: function (e) {
            return this.each(function () {
                S.dequeue(this, e)
            })
        },
        clearQueue: function (e) {
            return this.queue(e || "fx", [])
        },
        promise: function (e, t) {
            function n() {
                --o || r.resolveWith(s, [s])
            }
            var i, o = 1,
                r = S.Deferred(),
                s = this,
                a = this.length;
            for ("string" != typeof e && (t = e, e = void 0), e = e || "fx"; a--;)(i = V.get(s[a], e + "queueHooks")) && i.empty && (o++, i.empty.add(n));
            return n(), r.promise(t)
        }
    });
    var te = /[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,
        ne = new RegExp("^(?:([+-])=|)(" + te + ")([a-z%]*)$", "i"),
        ie = ["Top", "Right", "Bottom", "Left"],
        oe = C.documentElement,
        re = function (e) {
            return S.contains(e.ownerDocument, e)
        },
        se = {
            composed: !0
        };

    function ae(e, t) {
        return "none" === (e = t || e).style.display || "" === e.style.display && re(e) && "none" === S.css(e, "display")
    }

    function le(e, t, n, i) {
        var o, r, s = {};
        for (r in t) s[r] = e.style[r], e.style[r] = t[r];
        for (r in o = n.apply(e, i || []), t) e.style[r] = s[r];
        return o
    }

    function ce(e, t, n, i) {
        var o, r, s = 20,
            a = i ? function () {
                return i.cur()
            } : function () {
                return S.css(e, t, "")
            },
            l = a(),
            c = n && n[3] || (S.cssNumber[t] ? "" : "px"),
            d = e.nodeType && (S.cssNumber[t] || "px" !== c && +l) && ne.exec(S.css(e, t));
        if (d && d[3] !== c) {
            for (l /= 2, c = c || d[3], d = +l || 1; s--;) S.style(e, t, d + c), (1 - r) * (1 - (r = a() / l || .5)) <= 0 && (s = 0), d /= r;
            d *= 2, S.style(e, t, d + c), n = n || []
        }
        return n && (d = +d || +l || 0, o = n[1] ? d + (n[1] + 1) * n[2] : +n[2], i && (i.unit = c, i.start = d, i.end = o)), o
    }
    oe.getRootNode && (re = function (e) {
        return S.contains(e.ownerDocument, e) || e.getRootNode(se) === e.ownerDocument
    });
    var de = {};

    function ue(e, t) {
        for (var n, i, o, r, s, a, l, c = [], d = 0, u = e.length; d < u; d++)(i = e[d]).style && (n = i.style.display, t ? ("none" === n && (c[d] = V.get(i, "display") || null, c[d] || (i.style.display = "")), "" === i.style.display && ae(i) && (c[d] = (l = s = r = void 0, s = (o = i).ownerDocument, a = o.nodeName, (l = de[a]) || (r = s.body.appendChild(s.createElement(a)), l = S.css(r, "display"), r.parentNode.removeChild(r), "none" === l && (l = "block"), de[a] = l)))) : "none" !== n && (c[d] = "none", V.set(i, "display", n)));
        for (d = 0; d < u; d++) null != c[d] && (e[d].style.display = c[d]);
        return e
    }
    S.fn.extend({
        show: function () {
            return ue(this, !0)
        },
        hide: function () {
            return ue(this)
        },
        toggle: function (e) {
            return "boolean" == typeof e ? e ? this.show() : this.hide() : this.each(function () {
                ae(this) ? S(this).show() : S(this).hide()
            })
        }
    });
    var pe = /^(?:checkbox|radio)$/i,
        fe = /<([a-z][^\/\0>\x20\t\r\n\f]*)/i,
        he = /^$|^module$|\/(?:java|ecma)script/i,
        ve = {
            option: [1, "<select multiple='multiple'>", "</select>"],
            thead: [1, "<table>", "</table>"],
            col: [2, "<table><colgroup>", "</colgroup></table>"],
            tr: [2, "<table><tbody>", "</tbody></table>"],
            td: [3, "<table><tbody><tr>", "</tr></tbody></table>"],
            _default: [0, "", ""]
        };

    function ge(e, t) {
        var n;
        return n = void 0 !== e.getElementsByTagName ? e.getElementsByTagName(t || "*") : void 0 !== e.querySelectorAll ? e.querySelectorAll(t || "*") : [], void 0 === t || t && N(e, t) ? S.merge([e], n) : n
    }

    function me(e, t) {
        for (var n = 0, i = e.length; n < i; n++) V.set(e[n], "globalEval", !t || V.get(t[n], "globalEval"))
    }
    ve.optgroup = ve.option, ve.tbody = ve.tfoot = ve.colgroup = ve.caption = ve.thead, ve.th = ve.td;
    var ye, we, be = /<|&#?\w+;/;

    function xe(e, t, n, i, o) {
        for (var r, s, a, l, c, d, u = t.createDocumentFragment(), p = [], f = 0, h = e.length; f < h; f++)
            if ((r = e[f]) || 0 === r)
                if ("object" === x(r)) S.merge(p, r.nodeType ? [r] : r);
                else if (be.test(r)) {
            for (s = s || u.appendChild(t.createElement("div")), a = (fe.exec(r) || ["", ""])[1].toLowerCase(), l = ve[a] || ve._default, s.innerHTML = l[1] + S.htmlPrefilter(r) + l[2], d = l[0]; d--;) s = s.lastChild;
            S.merge(p, s.childNodes), (s = u.firstChild).textContent = ""
        } else p.push(t.createTextNode(r));
        for (u.textContent = "", f = 0; r = p[f++];)
            if (i && -1 < S.inArray(r, i)) o && o.push(r);
            else if (c = re(r), s = ge(u.appendChild(r), "script"), c && me(s), n)
            for (d = 0; r = s[d++];) he.test(r.type || "") && n.push(r);
        return u
    }
    ye = C.createDocumentFragment().appendChild(C.createElement("div")), (we = C.createElement("input")).setAttribute("type", "radio"), we.setAttribute("checked", "checked"), we.setAttribute("name", "t"), ye.appendChild(we), y.checkClone = ye.cloneNode(!0).cloneNode(!0).lastChild.checked, ye.innerHTML = "<textarea>x</textarea>", y.noCloneChecked = !!ye.cloneNode(!0).lastChild.defaultValue;
    var ke = /^key/,
        Te = /^(?:mouse|pointer|contextmenu|drag|drop)|click/,
        Ce = /^([^.]*)(?:\.(.+)|)/;

    function Se() {
        return !0
    }

    function $e() {
        return !1
    }

    function Ee(e, t) {
        return e === function () {
            try {
                return C.activeElement
            } catch (e) {}
        }() == ("focus" === t)
    }

    function Ne(e, t, n, i, o, r) {
        var s, a;
        if ("object" == typeof t) {
            for (a in "string" != typeof n && (i = i || n, n = void 0), t) Ne(e, a, n, i, t[a], r);
            return e
        }
        if (null == i && null == o ? (o = n, i = n = void 0) : null == o && ("string" == typeof n ? (o = i, i = void 0) : (o = i, i = n, n = void 0)), !1 === o) o = $e;
        else if (!o) return e;
        return 1 === r && (s = o, (o = function (e) {
            return S().off(e), s.apply(this, arguments)
        }).guid = s.guid || (s.guid = S.guid++)), e.each(function () {
            S.event.add(this, t, o, i, n)
        })
    }

    function Ae(e, o, r) {
        r ? (V.set(e, o, !1), S.event.add(e, o, {
            namespace: !1,
            handler: function (e) {
                var t, n, i = V.get(this, o);
                if (1 & e.isTrigger && this[o]) {
                    if (i.length)(S.event.special[o] || {}).delegateType && e.stopPropagation();
                    else if (i = a.call(arguments), V.set(this, o, i), t = r(this, o), this[o](), i !== (n = V.get(this, o)) || t ? V.set(this, o, !1) : n = {}, i !== n) return e.stopImmediatePropagation(), e.preventDefault(), n.value
                } else i.length && (V.set(this, o, {
                    value: S.event.trigger(S.extend(i[0], S.Event.prototype), i.slice(1), this)
                }), e.stopImmediatePropagation())
            }
        })) : void 0 === V.get(e, o) && S.event.add(e, o, Se)
    }
    S.event = {
        global: {},
        add: function (t, e, n, i, o) {
            var r, s, a, l, c, d, u, p, f, h, v, g = V.get(t);
            if (g)
                for (n.handler && (n = (r = n).handler, o = r.selector), o && S.find.matchesSelector(oe, o), n.guid || (n.guid = S.guid++), (l = g.events) || (l = g.events = {}), (s = g.handle) || (s = g.handle = function (e) {
                        return void 0 !== S && S.event.triggered !== e.type ? S.event.dispatch.apply(t, arguments) : void 0
                    }), c = (e = (e || "").match(L) || [""]).length; c--;) f = v = (a = Ce.exec(e[c]) || [])[1], h = (a[2] || "").split(".").sort(), f && (u = S.event.special[f] || {}, f = (o ? u.delegateType : u.bindType) || f, u = S.event.special[f] || {}, d = S.extend({
                    type: f,
                    origType: v,
                    data: i,
                    handler: n,
                    guid: n.guid,
                    selector: o,
                    needsContext: o && S.expr.match.needsContext.test(o),
                    namespace: h.join(".")
                }, r), (p = l[f]) || ((p = l[f] = []).delegateCount = 0, u.setup && !1 !== u.setup.call(t, i, h, s) || t.addEventListener && t.addEventListener(f, s)), u.add && (u.add.call(t, d), d.handler.guid || (d.handler.guid = n.guid)), o ? p.splice(p.delegateCount++, 0, d) : p.push(d), S.event.global[f] = !0)
        },
        remove: function (e, t, n, i, o) {
            var r, s, a, l, c, d, u, p, f, h, v, g = V.hasData(e) && V.get(e);
            if (g && (l = g.events)) {
                for (c = (t = (t || "").match(L) || [""]).length; c--;)
                    if (f = v = (a = Ce.exec(t[c]) || [])[1], h = (a[2] || "").split(".").sort(), f) {
                        for (u = S.event.special[f] || {}, p = l[f = (i ? u.delegateType : u.bindType) || f] || [], a = a[2] && new RegExp("(^|\\.)" + h.join("\\.(?:.*\\.|)") + "(\\.|$)"), s = r = p.length; r--;) d = p[r], !o && v !== d.origType || n && n.guid !== d.guid || a && !a.test(d.namespace) || i && i !== d.selector && ("**" !== i || !d.selector) || (p.splice(r, 1), d.selector && p.delegateCount--, u.remove && u.remove.call(e, d));
                        s && !p.length && (u.teardown && !1 !== u.teardown.call(e, h, g.handle) || S.removeEvent(e, f, g.handle), delete l[f])
                    } else
                        for (f in l) S.event.remove(e, f + t[c], n, i, !0);
                S.isEmptyObject(l) && V.remove(e, "handle events")
            }
        },
        dispatch: function (e) {
            var t, n, i, o, r, s, a = S.event.fix(e),
                l = new Array(arguments.length),
                c = (V.get(this, "events") || {})[a.type] || [],
                d = S.event.special[a.type] || {};
            for (l[0] = a, t = 1; t < arguments.length; t++) l[t] = arguments[t];
            if (a.delegateTarget = this, !d.preDispatch || !1 !== d.preDispatch.call(this, a)) {
                for (s = S.event.handlers.call(this, a, c), t = 0;
                    (o = s[t++]) && !a.isPropagationStopped();)
                    for (a.currentTarget = o.elem, n = 0;
                        (r = o.handlers[n++]) && !a.isImmediatePropagationStopped();) a.rnamespace && !1 !== r.namespace && !a.rnamespace.test(r.namespace) || (a.handleObj = r, a.data = r.data, void 0 !== (i = ((S.event.special[r.origType] || {}).handle || r.handler).apply(o.elem, l)) && !1 === (a.result = i) && (a.preventDefault(), a.stopPropagation()));
                return d.postDispatch && d.postDispatch.call(this, a), a.result
            }
        },
        handlers: function (e, t) {
            var n, i, o, r, s, a = [],
                l = t.delegateCount,
                c = e.target;
            if (l && c.nodeType && !("click" === e.type && 1 <= e.button))
                for (; c !== this; c = c.parentNode || this)
                    if (1 === c.nodeType && ("click" !== e.type || !0 !== c.disabled)) {
                        for (r = [], s = {}, n = 0; n < l; n++) void 0 === s[o = (i = t[n]).selector + " "] && (s[o] = i.needsContext ? -1 < S(o, this).index(c) : S.find(o, this, null, [c]).length), s[o] && r.push(i);
                        r.length && a.push({
                            elem: c,
                            handlers: r
                        })
                    } return c = this, l < t.length && a.push({
                elem: c,
                handlers: t.slice(l)
            }), a
        },
        addProp: function (t, e) {
            Object.defineProperty(S.Event.prototype, t, {
                enumerable: !0,
                configurable: !0,
                get: w(e) ? function () {
                    if (this.originalEvent) return e(this.originalEvent)
                } : function () {
                    if (this.originalEvent) return this.originalEvent[t]
                },
                set: function (e) {
                    Object.defineProperty(this, t, {
                        enumerable: !0,
                        configurable: !0,
                        writable: !0,
                        value: e
                    })
                }
            })
        },
        fix: function (e) {
            return e[S.expando] ? e : new S.Event(e)
        },
        special: {
            load: {
                noBubble: !0
            },
            click: {
                setup: function (e) {
                    var t = this || e;
                    return pe.test(t.type) && t.click && N(t, "input") && Ae(t, "click", Se), !1
                },
                trigger: function (e) {
                    var t = this || e;
                    return pe.test(t.type) && t.click && N(t, "input") && Ae(t, "click"), !0
                },
                _default: function (e) {
                    var t = e.target;
                    return pe.test(t.type) && t.click && N(t, "input") && V.get(t, "click") || N(t, "a")
                }
            },
            beforeunload: {
                postDispatch: function (e) {
                    void 0 !== e.result && e.originalEvent && (e.originalEvent.returnValue = e.result)
                }
            }
        }
    }, S.removeEvent = function (e, t, n) {
        e.removeEventListener && e.removeEventListener(t, n)
    }, S.Event = function (e, t) {
        if (!(this instanceof S.Event)) return new S.Event(e, t);
        e && e.type ? (this.originalEvent = e, this.type = e.type, this.isDefaultPrevented = e.defaultPrevented || void 0 === e.defaultPrevented && !1 === e.returnValue ? Se : $e, this.target = e.target && 3 === e.target.nodeType ? e.target.parentNode : e.target, this.currentTarget = e.currentTarget, this.relatedTarget = e.relatedTarget) : this.type = e, t && S.extend(this, t), this.timeStamp = e && e.timeStamp || Date.now(), this[S.expando] = !0
    }, S.Event.prototype = {
        constructor: S.Event,
        isDefaultPrevented: $e,
        isPropagationStopped: $e,
        isImmediatePropagationStopped: $e,
        isSimulated: !1,
        preventDefault: function () {
            var e = this.originalEvent;
            this.isDefaultPrevented = Se, e && !this.isSimulated && e.preventDefault()
        },
        stopPropagation: function () {
            var e = this.originalEvent;
            this.isPropagationStopped = Se, e && !this.isSimulated && e.stopPropagation()
        },
        stopImmediatePropagation: function () {
            var e = this.originalEvent;
            this.isImmediatePropagationStopped = Se, e && !this.isSimulated && e.stopImmediatePropagation(), this.stopPropagation()
        }
    }, S.each({
        altKey: !0,
        bubbles: !0,
        cancelable: !0,
        changedTouches: !0,
        ctrlKey: !0,
        detail: !0,
        eventPhase: !0,
        metaKey: !0,
        pageX: !0,
        pageY: !0,
        shiftKey: !0,
        view: !0,
        char: !0,
        code: !0,
        charCode: !0,
        key: !0,
        keyCode: !0,
        button: !0,
        buttons: !0,
        clientX: !0,
        clientY: !0,
        offsetX: !0,
        offsetY: !0,
        pointerId: !0,
        pointerType: !0,
        screenX: !0,
        screenY: !0,
        targetTouches: !0,
        toElement: !0,
        touches: !0,
        which: function (e) {
            var t = e.button;
            return null == e.which && ke.test(e.type) ? null != e.charCode ? e.charCode : e.keyCode : !e.which && void 0 !== t && Te.test(e.type) ? 1 & t ? 1 : 2 & t ? 3 : 4 & t ? 2 : 0 : e.which
        }
    }, S.event.addProp), S.each({
        focus: "focusin",
        blur: "focusout"
    }, function (e, t) {
        S.event.special[e] = {
            setup: function () {
                return Ae(this, e, Ee), !1
            },
            trigger: function () {
                return Ae(this, e), !0
            },
            delegateType: t
        }
    }), S.each({
        mouseenter: "mouseover",
        mouseleave: "mouseout",
        pointerenter: "pointerover",
        pointerleave: "pointerout"
    }, function (e, o) {
        S.event.special[e] = {
            delegateType: o,
            bindType: o,
            handle: function (e) {
                var t, n = e.relatedTarget,
                    i = e.handleObj;
                return n && (n === this || S.contains(this, n)) || (e.type = i.origType, t = i.handler.apply(this, arguments), e.type = o), t
            }
        }
    }), S.fn.extend({
        on: function (e, t, n, i) {
            return Ne(this, e, t, n, i)
        },
        one: function (e, t, n, i) {
            return Ne(this, e, t, n, i, 1)
        },
        off: function (e, t, n) {
            var i, o;
            if (e && e.preventDefault && e.handleObj) return i = e.handleObj, S(e.delegateTarget).off(i.namespace ? i.origType + "." + i.namespace : i.origType, i.selector, i.handler), this;
            if ("object" != typeof e) return !1 !== t && "function" != typeof t || (n = t, t = void 0), !1 === n && (n = $e), this.each(function () {
                S.event.remove(this, e, n, t)
            });
            for (o in e) this.off(o, t, e[o]);
            return this
        }
    });
    var je = /<(?!area|br|col|embed|hr|img|input|link|meta|param)(([a-z][^\/\0>\x20\t\r\n\f]*)[^>]*)\/>/gi,
        Oe = /<script|<style|<link/i,
        He = /checked\s*(?:[^=]|=\s*.checked.)/i,
        De = /^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;

    function Ie(e, t) {
        return N(e, "table") && N(11 !== t.nodeType ? t : t.firstChild, "tr") && S(e).children("tbody")[0] || e
    }

    function _e(e) {
        return e.type = (null !== e.getAttribute("type")) + "/" + e.type, e
    }

    function Le(e) {
        return "true/" === (e.type || "").slice(0, 5) ? e.type = e.type.slice(5) : e.removeAttribute("type"), e
    }

    function qe(e, t) {
        var n, i, o, r, s, a, l, c;
        if (1 === t.nodeType) {
            if (V.hasData(e) && (r = V.access(e), s = V.set(t, r), c = r.events))
                for (o in delete s.handle, s.events = {}, c)
                    for (n = 0, i = c[o].length; n < i; n++) S.event.add(t, o, c[o][n]);
            J.hasData(e) && (a = J.access(e), l = S.extend({}, a), J.set(t, l))
        }
    }

    function Pe(n, i, o, r) {
        i = g.apply([], i);
        var e, t, s, a, l, c, d = 0,
            u = n.length,
            p = u - 1,
            f = i[0],
            h = w(f);
        if (h || 1 < u && "string" == typeof f && !y.checkClone && He.test(f)) return n.each(function (e) {
            var t = n.eq(e);
            h && (i[0] = f.call(this, e, t.html())), Pe(t, i, o, r)
        });
        if (u && (t = (e = xe(i, n[0].ownerDocument, !1, n, r)).firstChild, 1 === e.childNodes.length && (e = t), t || r)) {
            for (a = (s = S.map(ge(e, "script"), _e)).length; d < u; d++) l = e, d !== p && (l = S.clone(l, !0, !0), a && S.merge(s, ge(l, "script"))), o.call(n[d], l, d);
            if (a)
                for (c = s[s.length - 1].ownerDocument, S.map(s, Le), d = 0; d < a; d++) l = s[d], he.test(l.type || "") && !V.access(l, "globalEval") && S.contains(c, l) && (l.src && "module" !== (l.type || "").toLowerCase() ? S._evalUrl && !l.noModule && S._evalUrl(l.src, {
                    nonce: l.nonce || l.getAttribute("nonce")
                }) : b(l.textContent.replace(De, ""), l, c))
        }
        return n
    }

    function Me(e, t, n) {
        for (var i, o = t ? S.filter(t, e) : e, r = 0; null != (i = o[r]); r++) n || 1 !== i.nodeType || S.cleanData(ge(i)), i.parentNode && (n && re(i) && me(ge(i, "script")), i.parentNode.removeChild(i));
        return e
    }
    S.extend({
        htmlPrefilter: function (e) {
            return e.replace(je, "<$1></$2>")
        },
        clone: function (e, t, n) {
            var i, o, r, s, a, l, c, d = e.cloneNode(!0),
                u = re(e);
            if (!(y.noCloneChecked || 1 !== e.nodeType && 11 !== e.nodeType || S.isXMLDoc(e)))
                for (s = ge(d), i = 0, o = (r = ge(e)).length; i < o; i++) a = r[i], "input" === (c = (l = s[i]).nodeName.toLowerCase()) && pe.test(a.type) ? l.checked = a.checked : "input" !== c && "textarea" !== c || (l.defaultValue = a.defaultValue);
            if (t)
                if (n)
                    for (r = r || ge(e), s = s || ge(d), i = 0, o = r.length; i < o; i++) qe(r[i], s[i]);
                else qe(e, d);
            return 0 < (s = ge(d, "script")).length && me(s, !u && ge(e, "script")), d
        },
        cleanData: function (e) {
            for (var t, n, i, o = S.event.special, r = 0; void 0 !== (n = e[r]); r++)
                if (Y(n)) {
                    if (t = n[V.expando]) {
                        if (t.events)
                            for (i in t.events) o[i] ? S.event.remove(n, i) : S.removeEvent(n, i, t.handle);
                        n[V.expando] = void 0
                    }
                    n[J.expando] && (n[J.expando] = void 0)
                }
        }
    }), S.fn.extend({
        detach: function (e) {
            return Me(this, e, !0)
        },
        remove: function (e) {
            return Me(this, e)
        },
        text: function (e) {
            return B(this, function (e) {
                return void 0 === e ? S.text(this) : this.empty().each(function () {
                    1 !== this.nodeType && 11 !== this.nodeType && 9 !== this.nodeType || (this.textContent = e)
                })
            }, null, e, arguments.length)
        },
        append: function () {
            return Pe(this, arguments, function (e) {
                1 !== this.nodeType && 11 !== this.nodeType && 9 !== this.nodeType || Ie(this, e).appendChild(e)
            })
        },
        prepend: function () {
            return Pe(this, arguments, function (e) {
                if (1 === this.nodeType || 11 === this.nodeType || 9 === this.nodeType) {
                    var t = Ie(this, e);
                    t.insertBefore(e, t.firstChild)
                }
            })
        },
        before: function () {
            return Pe(this, arguments, function (e) {
                this.parentNode && this.parentNode.insertBefore(e, this)
            })
        },
        after: function () {
            return Pe(this, arguments, function (e) {
                this.parentNode && this.parentNode.insertBefore(e, this.nextSibling)
            })
        },
        empty: function () {
            for (var e, t = 0; null != (e = this[t]); t++) 1 === e.nodeType && (S.cleanData(ge(e, !1)), e.textContent = "");
            return this
        },
        clone: function (e, t) {
            return e = null != e && e, t = null == t ? e : t, this.map(function () {
                return S.clone(this, e, t)
            })
        },
        html: function (e) {
            return B(this, function (e) {
                var t = this[0] || {},
                    n = 0,
                    i = this.length;
                if (void 0 === e && 1 === t.nodeType) return t.innerHTML;
                if ("string" == typeof e && !Oe.test(e) && !ve[(fe.exec(e) || ["", ""])[1].toLowerCase()]) {
                    e = S.htmlPrefilter(e);
                    try {
                        for (; n < i; n++) 1 === (t = this[n] || {}).nodeType && (S.cleanData(ge(t, !1)), t.innerHTML = e);
                        t = 0
                    } catch (e) {}
                }
                t && this.empty().append(e)
            }, null, e, arguments.length)
        },
        replaceWith: function () {
            var n = [];
            return Pe(this, arguments, function (e) {
                var t = this.parentNode;
                S.inArray(this, n) < 0 && (S.cleanData(ge(this)), t && t.replaceChild(e, this))
            }, n)
        }
    }), S.each({
        appendTo: "append",
        prependTo: "prepend",
        insertBefore: "before",
        insertAfter: "after",
        replaceAll: "replaceWith"
    }, function (e, s) {
        S.fn[e] = function (e) {
            for (var t, n = [], i = S(e), o = i.length - 1, r = 0; r <= o; r++) t = r === o ? this : this.clone(!0), S(i[r])[s](t), l.apply(n, t.get());
            return this.pushStack(n)
        }
    });
    var We, ze, Re, Be, Fe, Ge, Ue, Xe = new RegExp("^(" + te + ")(?!px)[a-z%]+$", "i"),
        Ye = function (e) {
            var t = e.ownerDocument.defaultView;
            return t && t.opener || (t = T), t.getComputedStyle(e)
        },
        Qe = new RegExp(ie.join("|"), "i");

    function Ve(e, t, n) {
        var i, o, r, s, a = e.style;
        return (n = n || Ye(e)) && ("" !== (s = n.getPropertyValue(t) || n[t]) || re(e) || (s = S.style(e, t)), !y.pixelBoxStyles() && Xe.test(s) && Qe.test(t) && (i = a.width, o = a.minWidth, r = a.maxWidth, a.minWidth = a.maxWidth = a.width = s, s = n.width, a.width = i, a.minWidth = o, a.maxWidth = r)), void 0 !== s ? s + "" : s
    }

    function Je(e, t) {
        return {
            get: function () {
                if (!e()) return (this.get = t).apply(this, arguments);
                delete this.get
            }
        }
    }

    function Ke() {
        if (Ue) {
            Ge.style.cssText = "position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0", Ue.style.cssText = "position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%", oe.appendChild(Ge).appendChild(Ue);
            var e = T.getComputedStyle(Ue);
            We = "1%" !== e.top, Fe = 12 === Ze(e.marginLeft), Ue.style.right = "60%", Be = 36 === Ze(e.right), ze = 36 === Ze(e.width), Ue.style.position = "absolute", Re = 12 === Ze(Ue.offsetWidth / 3), oe.removeChild(Ge), Ue = null
        }
    }

    function Ze(e) {
        return Math.round(parseFloat(e))
    }
    Ge = C.createElement("div"), (Ue = C.createElement("div")).style && (Ue.style.backgroundClip = "content-box", Ue.cloneNode(!0).style.backgroundClip = "", y.clearCloneStyle = "content-box" === Ue.style.backgroundClip, S.extend(y, {
        boxSizingReliable: function () {
            return Ke(), ze
        },
        pixelBoxStyles: function () {
            return Ke(), Be
        },
        pixelPosition: function () {
            return Ke(), We
        },
        reliableMarginLeft: function () {
            return Ke(), Fe
        },
        scrollboxSize: function () {
            return Ke(), Re
        }
    }));
    var et = ["Webkit", "Moz", "ms"],
        tt = C.createElement("div").style,
        nt = {};

    function it(e) {
        return S.cssProps[e] || nt[e] || (e in tt ? e : nt[e] = function (e) {
            for (var t = e[0].toUpperCase() + e.slice(1), n = et.length; n--;)
                if ((e = et[n] + t) in tt) return e
        }(e) || e)
    }
    var ot = /^(none|table(?!-c[ea]).+)/,
        rt = /^--/,
        st = {
            position: "absolute",
            visibility: "hidden",
            display: "block"
        },
        at = {
            letterSpacing: "0",
            fontWeight: "400"
        };

    function lt(e, t, n) {
        var i = ne.exec(t);
        return i ? Math.max(0, i[2] - (n || 0)) + (i[3] || "px") : t
    }

    function ct(e, t, n, i, o, r) {
        var s = "width" === t ? 1 : 0,
            a = 0,
            l = 0;
        if (n === (i ? "border" : "content")) return 0;
        for (; s < 4; s += 2) "margin" === n && (l += S.css(e, n + ie[s], !0, o)), i ? ("content" === n && (l -= S.css(e, "padding" + ie[s], !0, o)), "margin" !== n && (l -= S.css(e, "border" + ie[s] + "Width", !0, o))) : (l += S.css(e, "padding" + ie[s], !0, o), "padding" !== n ? l += S.css(e, "border" + ie[s] + "Width", !0, o) : a += S.css(e, "border" + ie[s] + "Width", !0, o));
        return !i && 0 <= r && (l += Math.max(0, Math.ceil(e["offset" + t[0].toUpperCase() + t.slice(1)] - r - l - a - .5)) || 0), l
    }

    function dt(e, t, n) {
        var i = Ye(e),
            o = (!y.boxSizingReliable() || n) && "border-box" === S.css(e, "boxSizing", !1, i),
            r = o,
            s = Ve(e, t, i),
            a = "offset" + t[0].toUpperCase() + t.slice(1);
        if (Xe.test(s)) {
            if (!n) return s;
            s = "auto"
        }
        return (!y.boxSizingReliable() && o || "auto" === s || !parseFloat(s) && "inline" === S.css(e, "display", !1, i)) && e.getClientRects().length && (o = "border-box" === S.css(e, "boxSizing", !1, i), (r = a in e) && (s = e[a])), (s = parseFloat(s) || 0) + ct(e, t, n || (o ? "border" : "content"), r, i, s) + "px"
    }

    function ut(e, t, n, i, o) {
        return new ut.prototype.init(e, t, n, i, o)
    }
    S.extend({
        cssHooks: {
            opacity: {
                get: function (e, t) {
                    if (t) {
                        var n = Ve(e, "opacity");
                        return "" === n ? "1" : n
                    }
                }
            }
        },
        cssNumber: {
            animationIterationCount: !0,
            columnCount: !0,
            fillOpacity: !0,
            flexGrow: !0,
            flexShrink: !0,
            fontWeight: !0,
            gridArea: !0,
            gridColumn: !0,
            gridColumnEnd: !0,
            gridColumnStart: !0,
            gridRow: !0,
            gridRowEnd: !0,
            gridRowStart: !0,
            lineHeight: !0,
            opacity: !0,
            order: !0,
            orphans: !0,
            widows: !0,
            zIndex: !0,
            zoom: !0
        },
        cssProps: {},
        style: function (e, t, n, i) {
            if (e && 3 !== e.nodeType && 8 !== e.nodeType && e.style) {
                var o, r, s, a = X(t),
                    l = rt.test(t),
                    c = e.style;
                if (l || (t = it(a)), s = S.cssHooks[t] || S.cssHooks[a], void 0 === n) return s && "get" in s && void 0 !== (o = s.get(e, !1, i)) ? o : c[t];
                "string" == (r = typeof n) && (o = ne.exec(n)) && o[1] && (n = ce(e, t, o), r = "number"), null != n && n == n && ("number" !== r || l || (n += o && o[3] || (S.cssNumber[a] ? "" : "px")), y.clearCloneStyle || "" !== n || 0 !== t.indexOf("background") || (c[t] = "inherit"), s && "set" in s && void 0 === (n = s.set(e, n, i)) || (l ? c.setProperty(t, n) : c[t] = n))
            }
        },
        css: function (e, t, n, i) {
            var o, r, s, a = X(t);
            return rt.test(t) || (t = it(a)), (s = S.cssHooks[t] || S.cssHooks[a]) && "get" in s && (o = s.get(e, !0, n)), void 0 === o && (o = Ve(e, t, i)), "normal" === o && t in at && (o = at[t]), "" === n || n ? (r = parseFloat(o), !0 === n || isFinite(r) ? r || 0 : o) : o
        }
    }), S.each(["height", "width"], function (e, l) {
        S.cssHooks[l] = {
            get: function (e, t, n) {
                if (t) return !ot.test(S.css(e, "display")) || e.getClientRects().length && e.getBoundingClientRect().width ? dt(e, l, n) : le(e, st, function () {
                    return dt(e, l, n)
                })
            },
            set: function (e, t, n) {
                var i, o = Ye(e),
                    r = !y.scrollboxSize() && "absolute" === o.position,
                    s = (r || n) && "border-box" === S.css(e, "boxSizing", !1, o),
                    a = n ? ct(e, l, n, s, o) : 0;
                return s && r && (a -= Math.ceil(e["offset" + l[0].toUpperCase() + l.slice(1)] - parseFloat(o[l]) - ct(e, l, "border", !1, o) - .5)), a && (i = ne.exec(t)) && "px" !== (i[3] || "px") && (e.style[l] = t, t = S.css(e, l)), lt(0, t, a)
            }
        }
    }), S.cssHooks.marginLeft = Je(y.reliableMarginLeft, function (e, t) {
        if (t) return (parseFloat(Ve(e, "marginLeft")) || e.getBoundingClientRect().left - le(e, {
            marginLeft: 0
        }, function () {
            return e.getBoundingClientRect().left
        })) + "px"
    }), S.each({
        margin: "",
        padding: "",
        border: "Width"
    }, function (o, r) {
        S.cssHooks[o + r] = {
            expand: function (e) {
                for (var t = 0, n = {}, i = "string" == typeof e ? e.split(" ") : [e]; t < 4; t++) n[o + ie[t] + r] = i[t] || i[t - 2] || i[0];
                return n
            }
        }, "margin" !== o && (S.cssHooks[o + r].set = lt)
    }), S.fn.extend({
        css: function (e, t) {
            return B(this, function (e, t, n) {
                var i, o, r = {},
                    s = 0;
                if (Array.isArray(t)) {
                    for (i = Ye(e), o = t.length; s < o; s++) r[t[s]] = S.css(e, t[s], !1, i);
                    return r
                }
                return void 0 !== n ? S.style(e, t, n) : S.css(e, t)
            }, e, t, 1 < arguments.length)
        }
    }), ((S.Tween = ut).prototype = {
        constructor: ut,
        init: function (e, t, n, i, o, r) {
            this.elem = e, this.prop = n, this.easing = o || S.easing._default, this.options = t, this.start = this.now = this.cur(), this.end = i, this.unit = r || (S.cssNumber[n] ? "" : "px")
        },
        cur: function () {
            var e = ut.propHooks[this.prop];
            return e && e.get ? e.get(this) : ut.propHooks._default.get(this)
        },
        run: function (e) {
            var t, n = ut.propHooks[this.prop];
            return this.options.duration ? this.pos = t = S.easing[this.easing](e, this.options.duration * e, 0, 1, this.options.duration) : this.pos = t = e, this.now = (this.end - this.start) * t + this.start, this.options.step && this.options.step.call(this.elem, this.now, this), n && n.set ? n.set(this) : ut.propHooks._default.set(this), this
        }
    }).init.prototype = ut.prototype, (ut.propHooks = {
        _default: {
            get: function (e) {
                var t;
                return 1 !== e.elem.nodeType || null != e.elem[e.prop] && null == e.elem.style[e.prop] ? e.elem[e.prop] : (t = S.css(e.elem, e.prop, "")) && "auto" !== t ? t : 0
            },
            set: function (e) {
                S.fx.step[e.prop] ? S.fx.step[e.prop](e) : 1 !== e.elem.nodeType || !S.cssHooks[e.prop] && null == e.elem.style[it(e.prop)] ? e.elem[e.prop] = e.now : S.style(e.elem, e.prop, e.now + e.unit)
            }
        }
    }).scrollTop = ut.propHooks.scrollLeft = {
        set: function (e) {
            e.elem.nodeType && e.elem.parentNode && (e.elem[e.prop] = e.now)
        }
    }, S.easing = {
        linear: function (e) {
            return e
        },
        swing: function (e) {
            return .5 - Math.cos(e * Math.PI) / 2
        },
        _default: "swing"
    }, S.fx = ut.prototype.init, S.fx.step = {};
    var pt, ft, ht, vt, gt = /^(?:toggle|show|hide)$/,
        mt = /queueHooks$/;

    function yt() {
        ft && (!1 === C.hidden && T.requestAnimationFrame ? T.requestAnimationFrame(yt) : T.setTimeout(yt, S.fx.interval), S.fx.tick())
    }

    function wt() {
        return T.setTimeout(function () {
            pt = void 0
        }), pt = Date.now()
    }

    function bt(e, t) {
        var n, i = 0,
            o = {
                height: e
            };
        for (t = t ? 1 : 0; i < 4; i += 2 - t) o["margin" + (n = ie[i])] = o["padding" + n] = e;
        return t && (o.opacity = o.width = e), o
    }

    function xt(e, t, n) {
        for (var i, o = (kt.tweeners[t] || []).concat(kt.tweeners["*"]), r = 0, s = o.length; r < s; r++)
            if (i = o[r].call(n, t, e)) return i
    }

    function kt(r, e, t) {
        var n, s, i = 0,
            o = kt.prefilters.length,
            a = S.Deferred().always(function () {
                delete l.elem
            }),
            l = function () {
                if (s) return !1;
                for (var e = pt || wt(), t = Math.max(0, c.startTime + c.duration - e), n = 1 - (t / c.duration || 0), i = 0, o = c.tweens.length; i < o; i++) c.tweens[i].run(n);
                return a.notifyWith(r, [c, n, t]), n < 1 && o ? t : (o || a.notifyWith(r, [c, 1, 0]), a.resolveWith(r, [c]), !1)
            },
            c = a.promise({
                elem: r,
                props: S.extend({}, e),
                opts: S.extend(!0, {
                    specialEasing: {},
                    easing: S.easing._default
                }, t),
                originalProperties: e,
                originalOptions: t,
                startTime: pt || wt(),
                duration: t.duration,
                tweens: [],
                createTween: function (e, t) {
                    var n = S.Tween(r, c.opts, e, t, c.opts.specialEasing[e] || c.opts.easing);
                    return c.tweens.push(n), n
                },
                stop: function (e) {
                    var t = 0,
                        n = e ? c.tweens.length : 0;
                    if (s) return this;
                    for (s = !0; t < n; t++) c.tweens[t].run(1);
                    return e ? (a.notifyWith(r, [c, 1, 0]), a.resolveWith(r, [c, e])) : a.rejectWith(r, [c, e]), this
                }
            }),
            d = c.props;
        for (function (e, t) {
                var n, i, o, r, s;
                for (n in e)
                    if (o = t[i = X(n)], r = e[n], Array.isArray(r) && (o = r[1], r = e[n] = r[0]), n !== i && (e[i] = r, delete e[n]), (s = S.cssHooks[i]) && "expand" in s)
                        for (n in r = s.expand(r), delete e[i], r) n in e || (e[n] = r[n], t[n] = o);
                    else t[i] = o
            }(d, c.opts.specialEasing); i < o; i++)
            if (n = kt.prefilters[i].call(c, r, d, c.opts)) return w(n.stop) && (S._queueHooks(c.elem, c.opts.queue).stop = n.stop.bind(n)), n;
        return S.map(d, xt, c), w(c.opts.start) && c.opts.start.call(r, c), c.progress(c.opts.progress).done(c.opts.done, c.opts.complete).fail(c.opts.fail).always(c.opts.always), S.fx.timer(S.extend(l, {
            elem: r,
            anim: c,
            queue: c.opts.queue
        })), c
    }
    S.Animation = S.extend(kt, {
        tweeners: {
            "*": [function (e, t) {
                var n = this.createTween(e, t);
                return ce(n.elem, e, ne.exec(t), n), n
            }]
        },
        tweener: function (e, t) {
            for (var n, i = 0, o = (e = w(e) ? (t = e, ["*"]) : e.match(L)).length; i < o; i++) n = e[i], kt.tweeners[n] = kt.tweeners[n] || [], kt.tweeners[n].unshift(t)
        },
        prefilters: [function (e, t, n) {
            var i, o, r, s, a, l, c, d, u = "width" in t || "height" in t,
                p = this,
                f = {},
                h = e.style,
                v = e.nodeType && ae(e),
                g = V.get(e, "fxshow");
            for (i in n.queue || (null == (s = S._queueHooks(e, "fx")).unqueued && (s.unqueued = 0, a = s.empty.fire, s.empty.fire = function () {
                    s.unqueued || a()
                }), s.unqueued++, p.always(function () {
                    p.always(function () {
                        s.unqueued--, S.queue(e, "fx").length || s.empty.fire()
                    })
                })), t)
                if (o = t[i], gt.test(o)) {
                    if (delete t[i], r = r || "toggle" === o, o === (v ? "hide" : "show")) {
                        if ("show" !== o || !g || void 0 === g[i]) continue;
                        v = !0
                    }
                    f[i] = g && g[i] || S.style(e, i)
                } if ((l = !S.isEmptyObject(t)) || !S.isEmptyObject(f))
                for (i in u && 1 === e.nodeType && (n.overflow = [h.overflow, h.overflowX, h.overflowY], null == (c = g && g.display) && (c = V.get(e, "display")), "none" === (d = S.css(e, "display")) && (c ? d = c : (ue([e], !0), c = e.style.display || c, d = S.css(e, "display"), ue([e]))), ("inline" === d || "inline-block" === d && null != c) && "none" === S.css(e, "float") && (l || (p.done(function () {
                        h.display = c
                    }), null == c && (d = h.display, c = "none" === d ? "" : d)), h.display = "inline-block")), n.overflow && (h.overflow = "hidden", p.always(function () {
                        h.overflow = n.overflow[0], h.overflowX = n.overflow[1], h.overflowY = n.overflow[2]
                    })), l = !1, f) l || (g ? "hidden" in g && (v = g.hidden) : g = V.access(e, "fxshow", {
                    display: c
                }), r && (g.hidden = !v), v && ue([e], !0), p.done(function () {
                    for (i in v || ue([e]), V.remove(e, "fxshow"), f) S.style(e, i, f[i])
                })), l = xt(v ? g[i] : 0, i, p), i in g || (g[i] = l.start, v && (l.end = l.start, l.start = 0))
        }],
        prefilter: function (e, t) {
            t ? kt.prefilters.unshift(e) : kt.prefilters.push(e)
        }
    }), S.speed = function (e, t, n) {
        var i = e && "object" == typeof e ? S.extend({}, e) : {
            complete: n || !n && t || w(e) && e,
            duration: e,
            easing: n && t || t && !w(t) && t
        };
        return S.fx.off ? i.duration = 0 : "number" != typeof i.duration && (i.duration in S.fx.speeds ? i.duration = S.fx.speeds[i.duration] : i.duration = S.fx.speeds._default), null != i.queue && !0 !== i.queue || (i.queue = "fx"), i.old = i.complete, i.complete = function () {
            w(i.old) && i.old.call(this), i.queue && S.dequeue(this, i.queue)
        }, i
    }, S.fn.extend({
        fadeTo: function (e, t, n, i) {
            return this.filter(ae).css("opacity", 0).show().end().animate({
                opacity: t
            }, e, n, i)
        },
        animate: function (t, e, n, i) {
            function o() {
                var e = kt(this, S.extend({}, t), s);
                (r || V.get(this, "finish")) && e.stop(!0)
            }
            var r = S.isEmptyObject(t),
                s = S.speed(e, n, i);
            return o.finish = o, r || !1 === s.queue ? this.each(o) : this.queue(s.queue, o)
        },
        stop: function (o, e, r) {
            function s(e) {
                var t = e.stop;
                delete e.stop, t(r)
            }
            return "string" != typeof o && (r = e, e = o, o = void 0), e && !1 !== o && this.queue(o || "fx", []), this.each(function () {
                var e = !0,
                    t = null != o && o + "queueHooks",
                    n = S.timers,
                    i = V.get(this);
                if (t) i[t] && i[t].stop && s(i[t]);
                else
                    for (t in i) i[t] && i[t].stop && mt.test(t) && s(i[t]);
                for (t = n.length; t--;) n[t].elem !== this || null != o && n[t].queue !== o || (n[t].anim.stop(r), e = !1, n.splice(t, 1));
                !e && r || S.dequeue(this, o)
            })
        },
        finish: function (s) {
            return !1 !== s && (s = s || "fx"), this.each(function () {
                var e, t = V.get(this),
                    n = t[s + "queue"],
                    i = t[s + "queueHooks"],
                    o = S.timers,
                    r = n ? n.length : 0;
                for (t.finish = !0, S.queue(this, s, []), i && i.stop && i.stop.call(this, !0), e = o.length; e--;) o[e].elem === this && o[e].queue === s && (o[e].anim.stop(!0), o.splice(e, 1));
                for (e = 0; e < r; e++) n[e] && n[e].finish && n[e].finish.call(this);
                delete t.finish
            })
        }
    }), S.each(["toggle", "show", "hide"], function (e, i) {
        var o = S.fn[i];
        S.fn[i] = function (e, t, n) {
            return null == e || "boolean" == typeof e ? o.apply(this, arguments) : this.animate(bt(i, !0), e, t, n)
        }
    }), S.each({
        slideDown: bt("show"),
        slideUp: bt("hide"),
        slideToggle: bt("toggle"),
        fadeIn: {
            opacity: "show"
        },
        fadeOut: {
            opacity: "hide"
        },
        fadeToggle: {
            opacity: "toggle"
        }
    }, function (e, i) {
        S.fn[e] = function (e, t, n) {
            return this.animate(i, e, t, n)
        }
    }), S.timers = [], S.fx.tick = function () {
        var e, t = 0,
            n = S.timers;
        for (pt = Date.now(); t < n.length; t++)(e = n[t])() || n[t] !== e || n.splice(t--, 1);
        n.length || S.fx.stop(), pt = void 0
    }, S.fx.timer = function (e) {
        S.timers.push(e), S.fx.start()
    }, S.fx.interval = 13, S.fx.start = function () {
        ft || (ft = !0, yt())
    }, S.fx.stop = function () {
        ft = null
    }, S.fx.speeds = {
        slow: 600,
        fast: 200,
        _default: 400
    }, S.fn.delay = function (i, e) {
        return i = S.fx && S.fx.speeds[i] || i, e = e || "fx", this.queue(e, function (e, t) {
            var n = T.setTimeout(e, i);
            t.stop = function () {
                T.clearTimeout(n)
            }
        })
    }, ht = C.createElement("input"), vt = C.createElement("select").appendChild(C.createElement("option")), ht.type = "checkbox", y.checkOn = "" !== ht.value, y.optSelected = vt.selected, (ht = C.createElement("input")).value = "t", ht.type = "radio", y.radioValue = "t" === ht.value;
    var Tt, Ct = S.expr.attrHandle;
    S.fn.extend({
        attr: function (e, t) {
            return B(this, S.attr, e, t, 1 < arguments.length)
        },
        removeAttr: function (e) {
            return this.each(function () {
                S.removeAttr(this, e)
            })
        }
    }), S.extend({
        attr: function (e, t, n) {
            var i, o, r = e.nodeType;
            if (3 !== r && 8 !== r && 2 !== r) return void 0 === e.getAttribute ? S.prop(e, t, n) : (1 === r && S.isXMLDoc(e) || (o = S.attrHooks[t.toLowerCase()] || (S.expr.match.bool.test(t) ? Tt : void 0)), void 0 !== n ? null === n ? void S.removeAttr(e, t) : o && "set" in o && void 0 !== (i = o.set(e, n, t)) ? i : (e.setAttribute(t, n + ""), n) : o && "get" in o && null !== (i = o.get(e, t)) ? i : null == (i = S.find.attr(e, t)) ? void 0 : i)
        },
        attrHooks: {
            type: {
                set: function (e, t) {
                    if (!y.radioValue && "radio" === t && N(e, "input")) {
                        var n = e.value;
                        return e.setAttribute("type", t), n && (e.value = n), t
                    }
                }
            }
        },
        removeAttr: function (e, t) {
            var n, i = 0,
                o = t && t.match(L);
            if (o && 1 === e.nodeType)
                for (; n = o[i++];) e.removeAttribute(n)
        }
    }), Tt = {
        set: function (e, t, n) {
            return !1 === t ? S.removeAttr(e, n) : e.setAttribute(n, n), n
        }
    }, S.each(S.expr.match.bool.source.match(/\w+/g), function (e, t) {
        var s = Ct[t] || S.find.attr;
        Ct[t] = function (e, t, n) {
            var i, o, r = t.toLowerCase();
            return n || (o = Ct[r], Ct[r] = i, i = null != s(e, t, n) ? r : null, Ct[r] = o), i
        }
    });
    var St = /^(?:input|select|textarea|button)$/i,
        $t = /^(?:a|area)$/i;

    function Et(e) {
        return (e.match(L) || []).join(" ")
    }

    function Nt(e) {
        return e.getAttribute && e.getAttribute("class") || ""
    }

    function At(e) {
        return Array.isArray(e) ? e : "string" == typeof e && e.match(L) || []
    }
    S.fn.extend({
        prop: function (e, t) {
            return B(this, S.prop, e, t, 1 < arguments.length)
        },
        removeProp: function (e) {
            return this.each(function () {
                delete this[S.propFix[e] || e]
            })
        }
    }), S.extend({
        prop: function (e, t, n) {
            var i, o, r = e.nodeType;
            if (3 !== r && 8 !== r && 2 !== r) return 1 === r && S.isXMLDoc(e) || (t = S.propFix[t] || t, o = S.propHooks[t]), void 0 !== n ? o && "set" in o && void 0 !== (i = o.set(e, n, t)) ? i : e[t] = n : o && "get" in o && null !== (i = o.get(e, t)) ? i : e[t]
        },
        propHooks: {
            tabIndex: {
                get: function (e) {
                    var t = S.find.attr(e, "tabindex");
                    return t ? parseInt(t, 10) : St.test(e.nodeName) || $t.test(e.nodeName) && e.href ? 0 : -1
                }
            }
        },
        propFix: {
            for: "htmlFor",
            class: "className"
        }
    }), y.optSelected || (S.propHooks.selected = {
        get: function (e) {
            var t = e.parentNode;
            return t && t.parentNode && t.parentNode.selectedIndex, null
        },
        set: function (e) {
            var t = e.parentNode;
            t && (t.selectedIndex, t.parentNode && t.parentNode.selectedIndex)
        }
    }), S.each(["tabIndex", "readOnly", "maxLength", "cellSpacing", "cellPadding", "rowSpan", "colSpan", "useMap", "frameBorder", "contentEditable"], function () {
        S.propFix[this.toLowerCase()] = this
    }), S.fn.extend({
        addClass: function (t) {
            var e, n, i, o, r, s, a, l = 0;
            if (w(t)) return this.each(function (e) {
                S(this).addClass(t.call(this, e, Nt(this)))
            });
            if ((e = At(t)).length)
                for (; n = this[l++];)
                    if (o = Nt(n), i = 1 === n.nodeType && " " + Et(o) + " ") {
                        for (s = 0; r = e[s++];) i.indexOf(" " + r + " ") < 0 && (i += r + " ");
                        o !== (a = Et(i)) && n.setAttribute("class", a)
                    } return this
        },
        removeClass: function (t) {
            var e, n, i, o, r, s, a, l = 0;
            if (w(t)) return this.each(function (e) {
                S(this).removeClass(t.call(this, e, Nt(this)))
            });
            if (!arguments.length) return this.attr("class", "");
            if ((e = At(t)).length)
                for (; n = this[l++];)
                    if (o = Nt(n), i = 1 === n.nodeType && " " + Et(o) + " ") {
                        for (s = 0; r = e[s++];)
                            for (; - 1 < i.indexOf(" " + r + " ");) i = i.replace(" " + r + " ", " ");
                        o !== (a = Et(i)) && n.setAttribute("class", a)
                    } return this
        },
        toggleClass: function (o, t) {
            var r = typeof o,
                s = "string" == r || Array.isArray(o);
            return "boolean" == typeof t && s ? t ? this.addClass(o) : this.removeClass(o) : w(o) ? this.each(function (e) {
                S(this).toggleClass(o.call(this, e, Nt(this), t), t)
            }) : this.each(function () {
                var e, t, n, i;
                if (s)
                    for (t = 0, n = S(this), i = At(o); e = i[t++];) n.hasClass(e) ? n.removeClass(e) : n.addClass(e);
                else void 0 !== o && "boolean" != r || ((e = Nt(this)) && V.set(this, "__className__", e), this.setAttribute && this.setAttribute("class", e || !1 === o ? "" : V.get(this, "__className__") || ""))
            })
        },
        hasClass: function (e) {
            var t, n, i = 0;
            for (t = " " + e + " "; n = this[i++];)
                if (1 === n.nodeType && -1 < (" " + Et(Nt(n)) + " ").indexOf(t)) return !0;
            return !1
        }
    });
    var jt = /\r/g;

    function Ot(e) {
        e.stopPropagation()
    }
    S.fn.extend({
        val: function (n) {
            var i, e, o, t = this[0];
            return arguments.length ? (o = w(n), this.each(function (e) {
                var t;
                1 === this.nodeType && (null == (t = o ? n.call(this, e, S(this).val()) : n) ? t = "" : "number" == typeof t ? t += "" : Array.isArray(t) && (t = S.map(t, function (e) {
                    return null == e ? "" : e + ""
                })), (i = S.valHooks[this.type] || S.valHooks[this.nodeName.toLowerCase()]) && "set" in i && void 0 !== i.set(this, t, "value") || (this.value = t))
            })) : t ? (i = S.valHooks[t.type] || S.valHooks[t.nodeName.toLowerCase()]) && "get" in i && void 0 !== (e = i.get(t, "value")) ? e : "string" == typeof (e = t.value) ? e.replace(jt, "") : null == e ? "" : e : void 0
        }
    }), S.extend({
        valHooks: {
            option: {
                get: function (e) {
                    var t = S.find.attr(e, "value");
                    return null != t ? t : Et(S.text(e))
                }
            },
            select: {
                get: function (e) {
                    var t, n, i, o = e.options,
                        r = e.selectedIndex,
                        s = "select-one" === e.type,
                        a = s ? null : [],
                        l = s ? r + 1 : o.length;
                    for (i = r < 0 ? l : s ? r : 0; i < l; i++)
                        if (((n = o[i]).selected || i === r) && !n.disabled && (!n.parentNode.disabled || !N(n.parentNode, "optgroup"))) {
                            if (t = S(n).val(), s) return t;
                            a.push(t)
                        } return a
                },
                set: function (e, t) {
                    for (var n, i, o = e.options, r = S.makeArray(t), s = o.length; s--;)((i = o[s]).selected = -1 < S.inArray(S.valHooks.option.get(i), r)) && (n = !0);
                    return n || (e.selectedIndex = -1), r
                }
            }
        }
    }), S.each(["radio", "checkbox"], function () {
        S.valHooks[this] = {
            set: function (e, t) {
                if (Array.isArray(t)) return e.checked = -1 < S.inArray(S(e).val(), t)
            }
        }, y.checkOn || (S.valHooks[this].get = function (e) {
            return null === e.getAttribute("value") ? "on" : e.value
        })
    }), y.focusin = "onfocusin" in T;
    var Ht = /^(?:focusinfocus|focusoutblur)$/;
    S.extend(S.event, {
        trigger: function (e, t, n, i) {
            var o, r, s, a, l, c, d, u, p = [n || C],
                f = m.call(e, "type") ? e.type : e,
                h = m.call(e, "namespace") ? e.namespace.split(".") : [];
            if (r = u = s = n = n || C, 3 !== n.nodeType && 8 !== n.nodeType && !Ht.test(f + S.event.triggered) && (-1 < f.indexOf(".") && (f = (h = f.split(".")).shift(), h.sort()), l = f.indexOf(":") < 0 && "on" + f, (e = e[S.expando] ? e : new S.Event(f, "object" == typeof e && e)).isTrigger = i ? 2 : 3, e.namespace = h.join("."), e.rnamespace = e.namespace ? new RegExp("(^|\\.)" + h.join("\\.(?:.*\\.|)") + "(\\.|$)") : null, e.result = void 0, e.target || (e.target = n), t = null == t ? [e] : S.makeArray(t, [e]), d = S.event.special[f] || {}, i || !d.trigger || !1 !== d.trigger.apply(n, t))) {
                if (!i && !d.noBubble && !v(n)) {
                    for (a = d.delegateType || f, Ht.test(a + f) || (r = r.parentNode); r; r = r.parentNode) p.push(r), s = r;
                    s === (n.ownerDocument || C) && p.push(s.defaultView || s.parentWindow || T)
                }
                for (o = 0;
                    (r = p[o++]) && !e.isPropagationStopped();) u = r, e.type = 1 < o ? a : d.bindType || f, (c = (V.get(r, "events") || {})[e.type] && V.get(r, "handle")) && c.apply(r, t), (c = l && r[l]) && c.apply && Y(r) && (e.result = c.apply(r, t), !1 === e.result && e.preventDefault());
                return e.type = f, i || e.isDefaultPrevented() || d._default && !1 !== d._default.apply(p.pop(), t) || !Y(n) || l && w(n[f]) && !v(n) && ((s = n[l]) && (n[l] = null), S.event.triggered = f, e.isPropagationStopped() && u.addEventListener(f, Ot), n[f](), e.isPropagationStopped() && u.removeEventListener(f, Ot), S.event.triggered = void 0, s && (n[l] = s)), e.result
            }
        },
        simulate: function (e, t, n) {
            var i = S.extend(new S.Event, n, {
                type: e,
                isSimulated: !0
            });
            S.event.trigger(i, null, t)
        }
    }), S.fn.extend({
        trigger: function (e, t) {
            return this.each(function () {
                S.event.trigger(e, t, this)
            })
        },
        triggerHandler: function (e, t) {
            var n = this[0];
            if (n) return S.event.trigger(e, t, n, !0)
        }
    }), y.focusin || S.each({
        focus: "focusin",
        blur: "focusout"
    }, function (n, i) {
        function o(e) {
            S.event.simulate(i, e.target, S.event.fix(e))
        }
        S.event.special[i] = {
            setup: function () {
                var e = this.ownerDocument || this,
                    t = V.access(e, i);
                t || e.addEventListener(n, o, !0), V.access(e, i, (t || 0) + 1)
            },
            teardown: function () {
                var e = this.ownerDocument || this,
                    t = V.access(e, i) - 1;
                t ? V.access(e, i, t) : (e.removeEventListener(n, o, !0), V.remove(e, i))
            }
        }
    });
    var Dt = T.location,
        It = Date.now(),
        _t = /\?/;
    S.parseXML = function (e) {
        var t;
        if (!e || "string" != typeof e) return null;
        try {
            t = (new T.DOMParser).parseFromString(e, "text/xml")
        } catch (e) {
            t = void 0
        }
        return t && !t.getElementsByTagName("parsererror").length || S.error("Invalid XML: " + e), t
    };
    var Lt = /\[\]$/,
        qt = /\r?\n/g,
        Pt = /^(?:submit|button|image|reset|file)$/i,
        Mt = /^(?:input|select|textarea|keygen)/i;

    function Wt(n, e, i, o) {
        var t;
        if (Array.isArray(e)) S.each(e, function (e, t) {
            i || Lt.test(n) ? o(n, t) : Wt(n + "[" + ("object" == typeof t && null != t ? e : "") + "]", t, i, o)
        });
        else if (i || "object" !== x(e)) o(n, e);
        else
            for (t in e) Wt(n + "[" + t + "]", e[t], i, o)
    }
    S.param = function (e, t) {
        function n(e, t) {
            var n = w(t) ? t() : t;
            o[o.length] = encodeURIComponent(e) + "=" + encodeURIComponent(null == n ? "" : n)
        }
        var i, o = [];
        if (null == e) return "";
        if (Array.isArray(e) || e.jquery && !S.isPlainObject(e)) S.each(e, function () {
            n(this.name, this.value)
        });
        else
            for (i in e) Wt(i, e[i], t, n);
        return o.join("&")
    }, S.fn.extend({
        serialize: function () {
            return S.param(this.serializeArray())
        },
        serializeArray: function () {
            return this.map(function () {
                var e = S.prop(this, "elements");
                return e ? S.makeArray(e) : this
            }).filter(function () {
                var e = this.type;
                return this.name && !S(this).is(":disabled") && Mt.test(this.nodeName) && !Pt.test(e) && (this.checked || !pe.test(e))
            }).map(function (e, t) {
                var n = S(this).val();
                return null == n ? null : Array.isArray(n) ? S.map(n, function (e) {
                    return {
                        name: t.name,
                        value: e.replace(qt, "\r\n")
                    }
                }) : {
                    name: t.name,
                    value: n.replace(qt, "\r\n")
                }
            }).get()
        }
    });
    var zt = /%20/g,
        Rt = /#.*$/,
        Bt = /([?&])_=[^&]*/,
        Ft = /^(.*?):[ \t]*([^\r\n]*)$/gm,
        Gt = /^(?:GET|HEAD)$/,
        Ut = /^\/\//,
        Xt = {},
        Yt = {},
        Qt = "*/".concat("*"),
        Vt = C.createElement("a");

    function Jt(r) {
        return function (e, t) {
            "string" != typeof e && (t = e, e = "*");
            var n, i = 0,
                o = e.toLowerCase().match(L) || [];
            if (w(t))
                for (; n = o[i++];) "+" === n[0] ? (n = n.slice(1) || "*", (r[n] = r[n] || []).unshift(t)) : (r[n] = r[n] || []).push(t)
        }
    }

    function Kt(t, o, r, s) {
        var a = {},
            l = t === Yt;

        function c(e) {
            var i;
            return a[e] = !0, S.each(t[e] || [], function (e, t) {
                var n = t(o, r, s);
                return "string" != typeof n || l || a[n] ? l ? !(i = n) : void 0 : (o.dataTypes.unshift(n), c(n), !1)
            }), i
        }
        return c(o.dataTypes[0]) || !a["*"] && c("*")
    }

    function Zt(e, t) {
        var n, i, o = S.ajaxSettings.flatOptions || {};
        for (n in t) void 0 !== t[n] && ((o[n] ? e : i = i || {})[n] = t[n]);
        return i && S.extend(!0, e, i), e
    }
    Vt.href = Dt.href, S.extend({
        active: 0,
        lastModified: {},
        etag: {},
        ajaxSettings: {
            url: Dt.href,
            type: "GET",
            isLocal: /^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Dt.protocol),
            global: !0,
            processData: !0,
            async: !0,
            contentType: "application/x-www-form-urlencoded; charset=UTF-8",
            accepts: {
                "*": Qt,
                text: "text/plain",
                html: "text/html",
                xml: "application/xml, text/xml",
                json: "application/json, text/javascript"
            },
            contents: {
                xml: /\bxml\b/,
                html: /\bhtml/,
                json: /\bjson\b/
            },
            responseFields: {
                xml: "responseXML",
                text: "responseText",
                json: "responseJSON"
            },
            converters: {
                "* text": String,
                "text html": !0,
                "text json": JSON.parse,
                "text xml": S.parseXML
            },
            flatOptions: {
                url: !0,
                context: !0
            }
        },
        ajaxSetup: function (e, t) {
            return t ? Zt(Zt(e, S.ajaxSettings), t) : Zt(S.ajaxSettings, e)
        },
        ajaxPrefilter: Jt(Xt),
        ajaxTransport: Jt(Yt),
        ajax: function (e, t) {
            "object" == typeof e && (t = e, e = void 0), t = t || {};
            var d, u, p, n, f, i, h, v, o, r, g = S.ajaxSetup({}, t),
                m = g.context || g,
                y = g.context && (m.nodeType || m.jquery) ? S(m) : S.event,
                w = S.Deferred(),
                b = S.Callbacks("once memory"),
                x = g.statusCode || {},
                s = {},
                a = {},
                l = "canceled",
                k = {
                    readyState: 0,
                    getResponseHeader: function (e) {
                        var t;
                        if (h) {
                            if (!n)
                                for (n = {}; t = Ft.exec(p);) n[t[1].toLowerCase() + " "] = (n[t[1].toLowerCase() + " "] || []).concat(t[2]);
                            t = n[e.toLowerCase() + " "]
                        }
                        return null == t ? null : t.join(", ")
                    },
                    getAllResponseHeaders: function () {
                        return h ? p : null
                    },
                    setRequestHeader: function (e, t) {
                        return null == h && (e = a[e.toLowerCase()] = a[e.toLowerCase()] || e, s[e] = t), this
                    },
                    overrideMimeType: function (e) {
                        return null == h && (g.mimeType = e), this
                    },
                    statusCode: function (e) {
                        var t;
                        if (e)
                            if (h) k.always(e[k.status]);
                            else
                                for (t in e) x[t] = [x[t], e[t]];
                        return this
                    },
                    abort: function (e) {
                        var t = e || l;
                        return d && d.abort(t), c(0, t), this
                    }
                };
            if (w.promise(k), g.url = ((e || g.url || Dt.href) + "").replace(Ut, Dt.protocol + "//"), g.type = t.method || t.type || g.method || g.type, g.dataTypes = (g.dataType || "*").toLowerCase().match(L) || [""], null == g.crossDomain) {
                i = C.createElement("a");
                try {
                    i.href = g.url, i.href = i.href, g.crossDomain = Vt.protocol + "//" + Vt.host != i.protocol + "//" + i.host
                } catch (e) {
                    g.crossDomain = !0
                }
            }
            if (g.data && g.processData && "string" != typeof g.data && (g.data = S.param(g.data, g.traditional)), Kt(Xt, g, t, k), h) return k;
            for (o in (v = S.event && g.global) && 0 == S.active++ && S.event.trigger("ajaxStart"), g.type = g.type.toUpperCase(), g.hasContent = !Gt.test(g.type), u = g.url.replace(Rt, ""), g.hasContent ? g.data && g.processData && 0 === (g.contentType || "").indexOf("application/x-www-form-urlencoded") && (g.data = g.data.replace(zt, "+")) : (r = g.url.slice(u.length), g.data && (g.processData || "string" == typeof g.data) && (u += (_t.test(u) ? "&" : "?") + g.data, delete g.data), !1 === g.cache && (u = u.replace(Bt, "$1"), r = (_t.test(u) ? "&" : "?") + "_=" + It++ + r), g.url = u + r), g.ifModified && (S.lastModified[u] && k.setRequestHeader("If-Modified-Since", S.lastModified[u]), S.etag[u] && k.setRequestHeader("If-None-Match", S.etag[u])), (g.data && g.hasContent && !1 !== g.contentType || t.contentType) && k.setRequestHeader("Content-Type", g.contentType), k.setRequestHeader("Accept", g.dataTypes[0] && g.accepts[g.dataTypes[0]] ? g.accepts[g.dataTypes[0]] + ("*" !== g.dataTypes[0] ? ", " + Qt + "; q=0.01" : "") : g.accepts["*"]), g.headers) k.setRequestHeader(o, g.headers[o]);
            if (g.beforeSend && (!1 === g.beforeSend.call(m, k, g) || h)) return k.abort();
            if (l = "abort", b.add(g.complete), k.done(g.success), k.fail(g.error), d = Kt(Yt, g, t, k)) {
                if (k.readyState = 1, v && y.trigger("ajaxSend", [k, g]), h) return k;
                g.async && 0 < g.timeout && (f = T.setTimeout(function () {
                    k.abort("timeout")
                }, g.timeout));
                try {
                    h = !1, d.send(s, c)
                } catch (e) {
                    if (h) throw e;
                    c(-1, e)
                }
            } else c(-1, "No Transport");

            function c(e, t, n, i) {
                var o, r, s, a, l, c = t;
                h || (h = !0, f && T.clearTimeout(f), d = void 0, p = i || "", k.readyState = 0 < e ? 4 : 0, o = 200 <= e && e < 300 || 304 === e, n && (a = function (e, t, n) {
                    for (var i, o, r, s, a = e.contents, l = e.dataTypes;
                        "*" === l[0];) l.shift(), void 0 === i && (i = e.mimeType || t.getResponseHeader("Content-Type"));
                    if (i)
                        for (o in a)
                            if (a[o] && a[o].test(i)) {
                                l.unshift(o);
                                break
                            } if (l[0] in n) r = l[0];
                    else {
                        for (o in n) {
                            if (!l[0] || e.converters[o + " " + l[0]]) {
                                r = o;
                                break
                            }
                            s = s || o
                        }
                        r = r || s
                    }
                    if (r) return r !== l[0] && l.unshift(r), n[r]
                }(g, k, n)), a = function (e, t, n, i) {
                    var o, r, s, a, l, c = {},
                        d = e.dataTypes.slice();
                    if (d[1])
                        for (s in e.converters) c[s.toLowerCase()] = e.converters[s];
                    for (r = d.shift(); r;)
                        if (e.responseFields[r] && (n[e.responseFields[r]] = t), !l && i && e.dataFilter && (t = e.dataFilter(t, e.dataType)), l = r, r = d.shift())
                            if ("*" === r) r = l;
                            else if ("*" !== l && l !== r) {
                        if (!(s = c[l + " " + r] || c["* " + r]))
                            for (o in c)
                                if ((a = o.split(" "))[1] === r && (s = c[l + " " + a[0]] || c["* " + a[0]])) {
                                    !0 === s ? s = c[o] : !0 !== c[o] && (r = a[0], d.unshift(a[1]));
                                    break
                                } if (!0 !== s)
                            if (s && e.throws) t = s(t);
                            else try {
                                t = s(t)
                            } catch (e) {
                                return {
                                    state: "parsererror",
                                    error: s ? e : "No conversion from " + l + " to " + r
                                }
                            }
                    }
                    return {
                        state: "success",
                        data: t
                    }
                }(g, a, k, o), o ? (g.ifModified && ((l = k.getResponseHeader("Last-Modified")) && (S.lastModified[u] = l), (l = k.getResponseHeader("etag")) && (S.etag[u] = l)), 204 === e || "HEAD" === g.type ? c = "nocontent" : 304 === e ? c = "notmodified" : (c = a.state, r = a.data, o = !(s = a.error))) : (s = c, !e && c || (c = "error", e < 0 && (e = 0))), k.status = e, k.statusText = (t || c) + "", o ? w.resolveWith(m, [r, c, k]) : w.rejectWith(m, [k, c, s]), k.statusCode(x), x = void 0, v && y.trigger(o ? "ajaxSuccess" : "ajaxError", [k, g, o ? r : s]), b.fireWith(m, [k, c]), v && (y.trigger("ajaxComplete", [k, g]), --S.active || S.event.trigger("ajaxStop")))
            }
            return k
        },
        getJSON: function (e, t, n) {
            return S.get(e, t, n, "json")
        },
        getScript: function (e, t) {
            return S.get(e, void 0, t, "script")
        }
    }), S.each(["get", "post"], function (e, o) {
        S[o] = function (e, t, n, i) {
            return w(t) && (i = i || n, n = t, t = void 0), S.ajax(S.extend({
                url: e,
                type: o,
                dataType: i,
                data: t,
                success: n
            }, S.isPlainObject(e) && e))
        }
    }), S._evalUrl = function (e, t) {
        return S.ajax({
            url: e,
            type: "GET",
            dataType: "script",
            cache: !0,
            async: !1,
            global: !1,
            converters: {
                "text script": function () {}
            },
            dataFilter: function (e) {
                S.globalEval(e, t)
            }
        })
    }, S.fn.extend({
        wrapAll: function (e) {
            var t;
            return this[0] && (w(e) && (e = e.call(this[0])), t = S(e, this[0].ownerDocument).eq(0).clone(!0), this[0].parentNode && t.insertBefore(this[0]), t.map(function () {
                for (var e = this; e.firstElementChild;) e = e.firstElementChild;
                return e
            }).append(this)), this
        },
        wrapInner: function (n) {
            return w(n) ? this.each(function (e) {
                S(this).wrapInner(n.call(this, e))
            }) : this.each(function () {
                var e = S(this),
                    t = e.contents();
                t.length ? t.wrapAll(n) : e.append(n)
            })
        },
        wrap: function (t) {
            var n = w(t);
            return this.each(function (e) {
                S(this).wrapAll(n ? t.call(this, e) : t)
            })
        },
        unwrap: function (e) {
            return this.parent(e).not("body").each(function () {
                S(this).replaceWith(this.childNodes)
            }), this
        }
    }), S.expr.pseudos.hidden = function (e) {
        return !S.expr.pseudos.visible(e)
    }, S.expr.pseudos.visible = function (e) {
        return !!(e.offsetWidth || e.offsetHeight || e.getClientRects().length)
    }, S.ajaxSettings.xhr = function () {
        try {
            return new T.XMLHttpRequest
        } catch (e) {}
    };
    var en = {
            0: 200,
            1223: 204
        },
        tn = S.ajaxSettings.xhr();
    y.cors = !!tn && "withCredentials" in tn, y.ajax = tn = !!tn, S.ajaxTransport(function (o) {
        var r, s;
        if (y.cors || tn && !o.crossDomain) return {
            send: function (e, t) {
                var n, i = o.xhr();
                if (i.open(o.type, o.url, o.async, o.username, o.password), o.xhrFields)
                    for (n in o.xhrFields) i[n] = o.xhrFields[n];
                for (n in o.mimeType && i.overrideMimeType && i.overrideMimeType(o.mimeType), o.crossDomain || e["X-Requested-With"] || (e["X-Requested-With"] = "XMLHttpRequest"), e) i.setRequestHeader(n, e[n]);
                r = function (e) {
                    return function () {
                        r && (r = s = i.onload = i.onerror = i.onabort = i.ontimeout = i.onreadystatechange = null, "abort" === e ? i.abort() : "error" === e ? "number" != typeof i.status ? t(0, "error") : t(i.status, i.statusText) : t(en[i.status] || i.status, i.statusText, "text" !== (i.responseType || "text") || "string" != typeof i.responseText ? {
                            binary: i.response
                        } : {
                            text: i.responseText
                        }, i.getAllResponseHeaders()))
                    }
                }, i.onload = r(), s = i.onerror = i.ontimeout = r("error"), void 0 !== i.onabort ? i.onabort = s : i.onreadystatechange = function () {
                    4 === i.readyState && T.setTimeout(function () {
                        r && s()
                    })
                }, r = r("abort");
                try {
                    i.send(o.hasContent && o.data || null)
                } catch (e) {
                    if (r) throw e
                }
            },
            abort: function () {
                r && r()
            }
        }
    }), S.ajaxPrefilter(function (e) {
        e.crossDomain && (e.contents.script = !1)
    }), S.ajaxSetup({
        accepts: {
            script: "text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"
        },
        contents: {
            script: /\b(?:java|ecma)script\b/
        },
        converters: {
            "text script": function (e) {
                return S.globalEval(e), e
            }
        }
    }), S.ajaxPrefilter("script", function (e) {
        void 0 === e.cache && (e.cache = !1), e.crossDomain && (e.type = "GET")
    }), S.ajaxTransport("script", function (n) {
        var i, o;
        if (n.crossDomain || n.scriptAttrs) return {
            send: function (e, t) {
                i = S("<script>").attr(n.scriptAttrs || {}).prop({
                    charset: n.scriptCharset,
                    src: n.url
                }).on("load error", o = function (e) {
                    i.remove(), o = null, e && t("error" === e.type ? 404 : 200, e.type)
                }), C.head.appendChild(i[0])
            },
            abort: function () {
                o && o()
            }
        }
    });
    var nn, on = [],
        rn = /(=)\?(?=&|$)|\?\?/;
    S.ajaxSetup({
        jsonp: "callback",
        jsonpCallback: function () {
            var e = on.pop() || S.expando + "_" + It++;
            return this[e] = !0, e
        }
    }), S.ajaxPrefilter("json jsonp", function (e, t, n) {
        var i, o, r, s = !1 !== e.jsonp && (rn.test(e.url) ? "url" : "string" == typeof e.data && 0 === (e.contentType || "").indexOf("application/x-www-form-urlencoded") && rn.test(e.data) && "data");
        if (s || "jsonp" === e.dataTypes[0]) return i = e.jsonpCallback = w(e.jsonpCallback) ? e.jsonpCallback() : e.jsonpCallback, s ? e[s] = e[s].replace(rn, "$1" + i) : !1 !== e.jsonp && (e.url += (_t.test(e.url) ? "&" : "?") + e.jsonp + "=" + i), e.converters["script json"] = function () {
            return r || S.error(i + " was not called"), r[0]
        }, e.dataTypes[0] = "json", o = T[i], T[i] = function () {
            r = arguments
        }, n.always(function () {
            void 0 === o ? S(T).removeProp(i) : T[i] = o, e[i] && (e.jsonpCallback = t.jsonpCallback, on.push(i)), r && w(o) && o(r[0]), r = o = void 0
        }), "script"
    }), y.createHTMLDocument = ((nn = C.implementation.createHTMLDocument("").body).innerHTML = "<form></form><form></form>", 2 === nn.childNodes.length), S.parseHTML = function (e, t, n) {
        return "string" != typeof e ? [] : ("boolean" == typeof t && (n = t, t = !1), t || (y.createHTMLDocument ? ((i = (t = C.implementation.createHTMLDocument("")).createElement("base")).href = C.location.href, t.head.appendChild(i)) : t = C), r = !n && [], (o = A.exec(e)) ? [t.createElement(o[1])] : (o = xe([e], t, r), r && r.length && S(r).remove(), S.merge([], o.childNodes)));
        var i, o, r
    }, S.fn.load = function (e, t, n) {
        var i, o, r, s = this,
            a = e.indexOf(" ");
        return -1 < a && (i = Et(e.slice(a)), e = e.slice(0, a)), w(t) ? (n = t, t = void 0) : t && "object" == typeof t && (o = "POST"), 0 < s.length && S.ajax({
            url: e,
            type: o || "GET",
            dataType: "html",
            data: t
        }).done(function (e) {
            r = arguments, s.html(i ? S("<div>").append(S.parseHTML(e)).find(i) : e)
        }).always(n && function (e, t) {
            s.each(function () {
                n.apply(this, r || [e.responseText, t, e])
            })
        }), this
    }, S.each(["ajaxStart", "ajaxStop", "ajaxComplete", "ajaxError", "ajaxSuccess", "ajaxSend"], function (e, t) {
        S.fn[t] = function (e) {
            return this.on(t, e)
        }
    }), S.expr.pseudos.animated = function (t) {
        return S.grep(S.timers, function (e) {
            return t === e.elem
        }).length
    }, S.offset = {
        setOffset: function (e, t, n) {
            var i, o, r, s, a, l, c = S.css(e, "position"),
                d = S(e),
                u = {};
            "static" === c && (e.style.position = "relative"), a = d.offset(), r = S.css(e, "top"), l = S.css(e, "left"), o = ("absolute" === c || "fixed" === c) && -1 < (r + l).indexOf("auto") ? (s = (i = d.position()).top, i.left) : (s = parseFloat(r) || 0, parseFloat(l) || 0), w(t) && (t = t.call(e, n, S.extend({}, a))), null != t.top && (u.top = t.top - a.top + s), null != t.left && (u.left = t.left - a.left + o), "using" in t ? t.using.call(e, u) : d.css(u)
        }
    }, S.fn.extend({
        offset: function (t) {
            if (arguments.length) return void 0 === t ? this : this.each(function (e) {
                S.offset.setOffset(this, t, e)
            });
            var e, n, i = this[0];
            return i ? i.getClientRects().length ? (e = i.getBoundingClientRect(), n = i.ownerDocument.defaultView, {
                top: e.top + n.pageYOffset,
                left: e.left + n.pageXOffset
            }) : {
                top: 0,
                left: 0
            } : void 0
        },
        position: function () {
            if (this[0]) {
                var e, t, n, i = this[0],
                    o = {
                        top: 0,
                        left: 0
                    };
                if ("fixed" === S.css(i, "position")) t = i.getBoundingClientRect();
                else {
                    for (t = this.offset(), n = i.ownerDocument, e = i.offsetParent || n.documentElement; e && (e === n.body || e === n.documentElement) && "static" === S.css(e, "position");) e = e.parentNode;
                    e && e !== i && 1 === e.nodeType && ((o = S(e).offset()).top += S.css(e, "borderTopWidth", !0), o.left += S.css(e, "borderLeftWidth", !0))
                }
                return {
                    top: t.top - o.top - S.css(i, "marginTop", !0),
                    left: t.left - o.left - S.css(i, "marginLeft", !0)
                }
            }
        },
        offsetParent: function () {
            return this.map(function () {
                for (var e = this.offsetParent; e && "static" === S.css(e, "position");) e = e.offsetParent;
                return e || oe
            })
        }
    }), S.each({
        scrollLeft: "pageXOffset",
        scrollTop: "pageYOffset"
    }, function (t, o) {
        var r = "pageYOffset" === o;
        S.fn[t] = function (e) {
            return B(this, function (e, t, n) {
                var i;
                if (v(e) ? i = e : 9 === e.nodeType && (i = e.defaultView), void 0 === n) return i ? i[o] : e[t];
                i ? i.scrollTo(r ? i.pageXOffset : n, r ? n : i.pageYOffset) : e[t] = n
            }, t, e, arguments.length)
        }
    }), S.each(["top", "left"], function (e, n) {
        S.cssHooks[n] = Je(y.pixelPosition, function (e, t) {
            if (t) return t = Ve(e, n), Xe.test(t) ? S(e).position()[n] + "px" : t
        })
    }), S.each({
        Height: "height",
        Width: "width"
    }, function (s, a) {
        S.each({
            padding: "inner" + s,
            content: a,
            "": "outer" + s
        }, function (i, r) {
            S.fn[r] = function (e, t) {
                var n = arguments.length && (i || "boolean" != typeof e),
                    o = i || (!0 === e || !0 === t ? "margin" : "border");
                return B(this, function (e, t, n) {
                    var i;
                    return v(e) ? 0 === r.indexOf("outer") ? e["inner" + s] : e.document.documentElement["client" + s] : 9 === e.nodeType ? (i = e.documentElement, Math.max(e.body["scroll" + s], i["scroll" + s], e.body["offset" + s], i["offset" + s], i["client" + s])) : void 0 === n ? S.css(e, t, o) : S.style(e, t, n, o)
                }, a, n ? e : void 0, n)
            }
        })
    }), S.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "), function (e, n) {
        S.fn[n] = function (e, t) {
            return 0 < arguments.length ? this.on(n, null, e, t) : this.trigger(n)
        }
    }), S.fn.extend({
        hover: function (e, t) {
            return this.mouseenter(e).mouseleave(t || e)
        }
    }), S.fn.extend({
        bind: function (e, t, n) {
            return this.on(e, null, t, n)
        },
        unbind: function (e, t) {
            return this.off(e, null, t)
        },
        delegate: function (e, t, n, i) {
            return this.on(t, e, n, i)
        },
        undelegate: function (e, t, n) {
            return 1 === arguments.length ? this.off(e, "**") : this.off(t, e || "**", n)
        }
    }), S.proxy = function (e, t) {
        var n, i, o;
        if ("string" == typeof t && (n = e[t], t = e, e = n), w(e)) return i = a.call(arguments, 2), (o = function () {
            return e.apply(t || this, i.concat(a.call(arguments)))
        }).guid = e.guid = e.guid || S.guid++, o
    }, S.holdReady = function (e) {
        e ? S.readyWait++ : S.ready(!0)
    }, S.isArray = Array.isArray, S.parseJSON = JSON.parse, S.nodeName = N, S.isFunction = w, S.isWindow = v, S.camelCase = X, S.type = x, S.now = Date.now, S.isNumeric = function (e) {
        var t = S.type(e);
        return ("number" === t || "string" === t) && !isNaN(e - parseFloat(e))
    }, "function" == typeof define && define.amd && define("jquery", [], function () {
        return S
    });
    var sn = T.jQuery,
        an = T.$;
    return S.noConflict = function (e) {
        return T.$ === S && (T.$ = an), e && T.jQuery === S && (T.jQuery = sn), S
    }, e || (T.jQuery = T.$ = S), S
}),
function (e) {
    "use strict";
    "function" == typeof define && define.amd ? define(["jquery"], e) : "undefined" != typeof module && module.exports ? module.exports = e(require("jquery")) : e(jQuery)
}(function (l) {
    function c(e) {
        return parseFloat(e) || 0
    }

    function d(e) {
        var t = l(e),
            i = null,
            o = [];
        return t.each(function () {
            var e = l(this),
                t = e.offset().top - c(e.css("margin-top")),
                n = 0 < o.length ? o[o.length - 1] : null;
            null === n ? o.push(e) : Math.floor(Math.abs(i - t)) <= 1 ? o[o.length - 1] = n.add(e) : o.push(e), i = t
        }), o
    }

    function u(e) {
        var t = {
            byRow: !0,
            property: "height",
            target: null,
            remove: !1
        };
        return "object" == typeof e ? l.extend(t, e) : ("boolean" == typeof e ? t.byRow = e : "remove" === e && (t.remove = !0), t)
    }
    var i = -1,
        o = -1,
        p = l.fn.matchHeight = function (e) {
            var t = u(e);
            if (t.remove) {
                var n = this;
                return this.css(t.property, ""), l.each(p._groups, function (e, t) {
                    t.elements = t.elements.not(n)
                }), this
            }
            return this.length <= 1 && !t.target || (p._groups.push({
                elements: this,
                options: t
            }), p._apply(this, t)), this
        };
    p.version = "0.7.2", p._groups = [], p._throttle = 80, p._maintainScroll = !1, p._beforeUpdate = null, p._afterUpdate = null, p._rows = d, p._parse = c, p._parseOptions = u, p._apply = function (e, t) {
        var r = u(t),
            n = l(e),
            i = [n],
            o = l(window).scrollTop(),
            s = l("html").outerHeight(!0),
            a = n.parents().filter(":hidden");
        return a.each(function () {
            var e = l(this);
            e.data("style-cache", e.attr("style"))
        }), a.css("display", "block"), r.byRow && !r.target && (n.each(function () {
            var e = l(this),
                t = e.css("display");
            "inline-block" !== t && "flex" !== t && "inline-flex" !== t && (t = "block"), e.data("style-cache", e.attr("style")), e.css({
                display: t,
                "padding-top": "0",
                "padding-bottom": "0",
                "margin-top": "0",
                "margin-bottom": "0",
                "border-top-width": "0",
                "border-bottom-width": "0",
                height: "100px",
                overflow: "hidden"
            })
        }), i = d(n), n.each(function () {
            var e = l(this);
            e.attr("style", e.data("style-cache") || "")
        })), l.each(i, function (e, t) {
            var n = l(t),
                o = 0;
            if (r.target) o = r.target.outerHeight(!1);
            else {
                if (r.byRow && n.length <= 1) return void n.css(r.property, "");
                n.each(function () {
                    var e = l(this),
                        t = e.attr("style"),
                        n = e.css("display");
                    "inline-block" !== n && "flex" !== n && "inline-flex" !== n && (n = "block");
                    var i = {
                        display: n
                    };
                    i[r.property] = "", e.css(i), e.outerHeight(!1) > o && (o = e.outerHeight(!1)), t ? e.attr("style", t) : e.css("display", "")
                })
            }
            n.each(function () {
                var e = l(this),
                    t = 0;
                r.target && e.is(r.target) || ("border-box" !== e.css("box-sizing") && (t += c(e.css("border-top-width")) + c(e.css("border-bottom-width")), t += c(e.css("padding-top")) + c(e.css("padding-bottom"))), e.css(r.property, o - t + "px"))
            })
        }), a.each(function () {
            var e = l(this);
            e.attr("style", e.data("style-cache") || null)
        }), p._maintainScroll && l(window).scrollTop(o / s * l("html").outerHeight(!0)), this
    }, p._applyDataApi = function () {
        var n = {};
        l("[data-match-height], [data-mh]").each(function () {
            var e = l(this),
                t = e.attr("data-mh") || e.attr("data-match-height");
            n[t] = t in n ? n[t].add(e) : e
        }), l.each(n, function () {
            this.matchHeight(!0)
        })
    };

    function r(e) {
        p._beforeUpdate && p._beforeUpdate(e, p._groups), l.each(p._groups, function () {
            p._apply(this.elements, this.options)
        }), p._afterUpdate && p._afterUpdate(e, p._groups)
    }
    p._update = function (e, t) {
        if (t && "resize" === t.type) {
            var n = l(window).width();
            if (n === i) return;
            i = n
        }
        e ? -1 === o && (o = setTimeout(function () {
            r(t), o = -1
        }, p._throttle)) : r(t)
    }, l(p._applyDataApi);
    var e = l.fn.on ? "on" : "bind";
    l(window)[e]("load", function (e) {
        p._update(!1, e)
    }), l(window)[e]("resize orientationchange", function (e) {
        p._update(!0, e)
    })
}),
function (t) {
    "function" == typeof define && define.amd ? define(["jquery"], function (e) {
        return t(e)
    }) : "object" == typeof module && "object" == typeof module.exports ? exports = t(require("jquery")) : t(jQuery)
}(function (t) {
    t.easing.jswing = t.easing.swing;
    var n = Math.pow,
        i = Math.sqrt,
        o = Math.sin,
        r = Math.cos,
        s = Math.PI,
        a = 1.70158,
        l = 1.525 * a,
        c = 2 * s / 3,
        d = 2 * s / 4.5;

    function u(e) {
        var t = 7.5625,
            n = 2.75;
        return e < 1 / n ? t * e * e : e < 2 / n ? t * (e -= 1.5 / n) * e + .75 : e < 2.5 / n ? t * (e -= 2.25 / n) * e + .9375 : t * (e -= 2.625 / n) * e + .984375
    }
    t.extend(t.easing, {
        def: "easeOutQuad",
        swing: function (e) {
            return t.easing[t.easing.def](e)
        },
        easeInQuad: function (e) {
            return e * e
        },
        easeOutQuad: function (e) {
            return 1 - (1 - e) * (1 - e)
        },
        easeInOutQuad: function (e) {
            return e < .5 ? 2 * e * e : 1 - n(-2 * e + 2, 2) / 2
        },
        easeInCubic: function (e) {
            return e * e * e
        },
        easeOutCubic: function (e) {
            return 1 - n(1 - e, 3)
        },
        easeInOutCubic: function (e) {
            return e < .5 ? 4 * e * e * e : 1 - n(-2 * e + 2, 3) / 2
        },
        easeInQuart: function (e) {
            return e * e * e * e
        },
        easeOutQuart: function (e) {
            return 1 - n(1 - e, 4)
        },
        easeInOutQuart: function (e) {
            return e < .5 ? 8 * e * e * e * e : 1 - n(-2 * e + 2, 4) / 2
        },
        easeInQuint: function (e) {
            return e * e * e * e * e
        },
        easeOutQuint: function (e) {
            return 1 - n(1 - e, 5)
        },
        easeInOutQuint: function (e) {
            return e < .5 ? 16 * e * e * e * e * e : 1 - n(-2 * e + 2, 5) / 2
        },
        easeInSine: function (e) {
            return 1 - r(e * s / 2)
        },
        easeOutSine: function (e) {
            return o(e * s / 2)
        },
        easeInOutSine: function (e) {
            return -(r(s * e) - 1) / 2
        },
        easeInExpo: function (e) {
            return 0 === e ? 0 : n(2, 10 * e - 10)
        },
        easeOutExpo: function (e) {
            return 1 === e ? 1 : 1 - n(2, -10 * e)
        },
        easeInOutExpo: function (e) {
            return 0 === e ? 0 : 1 === e ? 1 : e < .5 ? n(2, 20 * e - 10) / 2 : (2 - n(2, -20 * e + 10)) / 2
        },
        easeInCirc: function (e) {
            return 1 - i(1 - n(e, 2))
        },
        easeOutCirc: function (e) {
            return i(1 - n(e - 1, 2))
        },
        easeInOutCirc: function (e) {
            return e < .5 ? (1 - i(1 - n(2 * e, 2))) / 2 : (i(1 - n(-2 * e + 2, 2)) + 1) / 2
        },
        easeInElastic: function (e) {
            return 0 === e ? 0 : 1 === e ? 1 : -n(2, 10 * e - 10) * o((10 * e - 10.75) * c)
        },
        easeOutElastic: function (e) {
            return 0 === e ? 0 : 1 === e ? 1 : n(2, -10 * e) * o((10 * e - .75) * c) + 1
        },
        easeInOutElastic: function (e) {
            return 0 === e ? 0 : 1 === e ? 1 : e < .5 ? -n(2, 20 * e - 10) * o((20 * e - 11.125) * d) / 2 : n(2, -20 * e + 10) * o((20 * e - 11.125) * d) / 2 + 1
        },
        easeInBack: function (e) {
            return 2.70158 * e * e * e - a * e * e
        },
        easeOutBack: function (e) {
            return 1 + 2.70158 * n(e - 1, 3) + a * n(e - 1, 2)
        },
        easeInOutBack: function (e) {
            return e < .5 ? n(2 * e, 2) * (7.189819 * e - l) / 2 : (n(2 * e - 2, 2) * ((1 + l) * (2 * e - 2) + l) + 2) / 2
        },
        easeInBounce: function (e) {
            return 1 - u(1 - e)
        },
        easeOutBounce: u,
        easeInOutBounce: function (e) {
            return e < .5 ? (1 - u(1 - 2 * e)) / 2 : (1 + u(2 * e - 1)) / 2
        }
    })
}),
function (e) {
    "function" == typeof define && define.amd ? define(["jquery"], e) : "object" == typeof exports ? module.exports = e(require("jquery")) : e(jQuery)
}(function (d) {
    function t() {
        if (f.length) {
            var e = 0,
                t = d.map(f, function (e) {
                    var t = e.data.selector,
                        n = e.$element;
                    return t ? n.find(t) : n
                });
            for (u = u || (c = l = a = void 0, (c = {
                    height: v.innerHeight,
                    width: v.innerWidth
                }).height || !(a = h.compatMode) && d.support.boxModel || (c = {
                    height: (l = "CSS1Compat" === a ? g : h.body).clientHeight,
                    width: l.clientWidth
                }), c), p = p || {
                    top: v.pageYOffset || g.scrollTop || h.body.scrollTop,
                    left: v.pageXOffset || g.scrollLeft || h.body.scrollLeft
                }; e < f.length; e++)
                if (d.contains(g, t[e][0])) {
                    var n = d(t[e]),
                        i = n[0].offsetHeight,
                        o = n[0].offsetWidth,
                        r = n.offset(),
                        s = n.data("inview");
                    if (!p || !u) return;
                    r.top + i > p.top && r.top < p.top + u.height && r.left + o > p.left && r.left < p.left + u.width ? s || n.data("inview", !0).trigger("inview", [!0]) : s && n.data("inview", !1).trigger("inview", [!1])
                }
        }
        var a, l, c
    }
    var u, p, i, f = [],
        h = document,
        v = window,
        g = h.documentElement;
    d.event.special.inview = {
        add: function (e) {
            f.push({
                data: e,
                $element: d(this),
                element: this
            }), !i && f.length && (i = setInterval(t, 250))
        },
        remove: function (e) {
            for (var t = 0; t < f.length; t++) {
                var n = f[t];
                if (n.element === this && n.data.guid === e.guid) {
                    f.splice(t, 1);
                    break
                }
            }
            f.length || (clearInterval(i), i = null)
        }
    }, d(v).on("scroll resize scrollstop", function () {
        u = p = null
    }), !g.addEventListener && g.attachEvent && g.attachEvent("onfocusin", function () {
        p = null
    })
}),
function (e) {
    "use strict";
    "function" == typeof define && define.amd ? define(["jquery"], e) : "undefined" != typeof exports ? module.exports = e(require("jquery")) : e(jQuery)
}(function (c) {
    "use strict";
    var o, s = window.Slick;
    (o = 0, s = function (e, t) {
        var n, i = this;
        i.defaults = {
            accessibility: !0,
            adaptiveHeight: !1,
            appendArrows: c(e),
            appendDots: c(e),
            arrows: !0,
            asNavFor: null,
            prevArrow: '<button class="slick-prev" aria-label="Previous" type="button">Previous</button>',
            nextArrow: '<button class="slick-next" aria-label="Next" type="button">Next</button>',
            autoplay: !1,
            autoplaySpeed: 3e3,
            centerMode: !1,
            centerPadding: "50px",
            cssEase: "ease",
            customPaging: function (e, t) {
                return c('<button type="button" />').text(t + 1)
            },
            dots: !1,
            dotsClass: "slick-dots",
            draggable: !0,
            easing: "linear",
            edgeFriction: .35,
            fade: !1,
            focusOnSelect: !1,
            focusOnChange: !1,
            infinite: !0,
            initialSlide: 0,
            lazyLoad: "ondemand",
            mobileFirst: !1,
            pauseOnHover: !0,
            pauseOnFocus: !0,
            pauseOnDotsHover: !1,
            respondTo: "window",
            responsive: null,
            rows: 1,
            rtl: !1,
            slide: "",
            slidesPerRow: 1,
            slidesToShow: 1,
            slidesToScroll: 1,
            speed: 500,
            swipe: !0,
            swipeToSlide: !1,
            touchMove: !0,
            touchThreshold: 5,
            useCSS: !0,
            useTransform: !0,
            variableWidth: !1,
            vertical: !1,
            verticalSwiping: !1,
            waitForAnimate: !0,
            zIndex: 1e3
        }, i.initials = {
            animating: !1,
            dragging: !1,
            autoPlayTimer: null,
            currentDirection: 0,
            currentLeft: null,
            currentSlide: 0,
            direction: 1,
            $dots: null,
            listWidth: null,
            listHeight: null,
            loadIndex: 0,
            $nextArrow: null,
            $prevArrow: null,
            scrolling: !1,
            slideCount: null,
            slideWidth: null,
            $slideTrack: null,
            $slides: null,
            sliding: !1,
            slideOffset: 0,
            swipeLeft: null,
            swiping: !1,
            $list: null,
            touchObject: {},
            transformsEnabled: !1,
            unslicked: !1
        }, c.extend(i, i.initials), i.activeBreakpoint = null, i.animType = null, i.animProp = null, i.breakpoints = [], i.breakpointSettings = [], i.cssTransitions = !1, i.focussed = !1, i.interrupted = !1, i.hidden = "hidden", i.paused = !0, i.positionProp = null, i.respondTo = null, i.rowCount = 1, i.shouldClick = !0, i.$slider = c(e), i.$slidesCache = null, i.transformType = null, i.transitionType = null, i.visibilityChange = "visibilitychange", i.windowWidth = 0, i.windowTimer = null, n = c(e).data("slick") || {}, i.options = c.extend({}, i.defaults, t, n), i.currentSlide = i.options.initialSlide, i.originalSettings = i.options, void 0 !== document.mozHidden ? (i.hidden = "mozHidden", i.visibilityChange = "mozvisibilitychange") : void 0 !== document.webkitHidden && (i.hidden = "webkitHidden", i.visibilityChange = "webkitvisibilitychange"), i.autoPlay = c.proxy(i.autoPlay, i), i.autoPlayClear = c.proxy(i.autoPlayClear, i), i.autoPlayIterator = c.proxy(i.autoPlayIterator, i), i.changeSlide = c.proxy(i.changeSlide, i), i.clickHandler = c.proxy(i.clickHandler, i), i.selectHandler = c.proxy(i.selectHandler, i), i.setPosition = c.proxy(i.setPosition, i), i.swipeHandler = c.proxy(i.swipeHandler, i), i.dragHandler = c.proxy(i.dragHandler, i), i.keyHandler = c.proxy(i.keyHandler, i), i.instanceUid = o++, i.htmlExpr = /^(?:\s*(<[\w\W]+>)[^>]*)$/, i.registerBreakpoints(), i.init(!0)
    }).prototype.activateADA = function () {
        this.$slideTrack.find(".slick-active").attr({
            "aria-hidden": "false"
        }).find("a, input, button, select").attr({
            tabindex: "0"
        })
    }, s.prototype.addSlide = s.prototype.slickAdd = function (e, t, n) {
        var i = this;
        if ("boolean" == typeof t) n = t, t = null;
        else if (t < 0 || t >= i.slideCount) return !1;
        i.unload(), "number" == typeof t ? 0 === t && 0 === i.$slides.length ? c(e).appendTo(i.$slideTrack) : n ? c(e).insertBefore(i.$slides.eq(t)) : c(e).insertAfter(i.$slides.eq(t)) : !0 === n ? c(e).prependTo(i.$slideTrack) : c(e).appendTo(i.$slideTrack), i.$slides = i.$slideTrack.children(this.options.slide), i.$slideTrack.children(this.options.slide).detach(), i.$slideTrack.append(i.$slides), i.$slides.each(function (e, t) {
            c(t).attr("data-slick-index", e)
        }), i.$slidesCache = i.$slides, i.reinit()
    }, s.prototype.animateHeight = function () {
        var e = this;
        if (1 === e.options.slidesToShow && !0 === e.options.adaptiveHeight && !1 === e.options.vertical) {
            var t = e.$slides.eq(e.currentSlide).outerHeight(!0);
            e.$list.animate({
                height: t
            }, e.options.speed)
        }
    }, s.prototype.animateSlide = function (e, t) {
        var n = {},
            i = this;
        i.animateHeight(), !0 === i.options.rtl && !1 === i.options.vertical && (e = -e), !1 === i.transformsEnabled ? !1 === i.options.vertical ? i.$slideTrack.animate({
            left: e
        }, i.options.speed, i.options.easing, t) : i.$slideTrack.animate({
            top: e
        }, i.options.speed, i.options.easing, t) : !1 === i.cssTransitions ? (!0 === i.options.rtl && (i.currentLeft = -i.currentLeft), c({
            animStart: i.currentLeft
        }).animate({
            animStart: e
        }, {
            duration: i.options.speed,
            easing: i.options.easing,
            step: function (e) {
                e = Math.ceil(e), !1 === i.options.vertical ? n[i.animType] = "translate(" + e + "px, 0px)" : n[i.animType] = "translate(0px," + e + "px)", i.$slideTrack.css(n)
            },
            complete: function () {
                t && t.call()
            }
        })) : (i.applyTransition(), e = Math.ceil(e), !1 === i.options.vertical ? n[i.animType] = "translate3d(" + e + "px, 0px, 0px)" : n[i.animType] = "translate3d(0px," + e + "px, 0px)", i.$slideTrack.css(n), t && setTimeout(function () {
            i.disableTransition(), t.call()
        }, i.options.speed))
    }, s.prototype.getNavTarget = function () {
        var e = this.options.asNavFor;
        return e && null !== e && (e = c(e).not(this.$slider)), e
    }, s.prototype.asNavFor = function (t) {
        var e = this.getNavTarget();
        null !== e && "object" == typeof e && e.each(function () {
            var e = c(this).slick("getSlick");
            e.unslicked || e.slideHandler(t, !0)
        })
    }, s.prototype.applyTransition = function (e) {
        var t = this,
            n = {};
        !1 === t.options.fade ? n[t.transitionType] = t.transformType + " " + t.options.speed + "ms " + t.options.cssEase : n[t.transitionType] = "opacity " + t.options.speed + "ms " + t.options.cssEase, !1 === t.options.fade ? t.$slideTrack.css(n) : t.$slides.eq(e).css(n)
    }, s.prototype.autoPlay = function () {
        var e = this;
        e.autoPlayClear(), e.slideCount > e.options.slidesToShow && (e.autoPlayTimer = setInterval(e.autoPlayIterator, e.options.autoplaySpeed))
    }, s.prototype.autoPlayClear = function () {
        this.autoPlayTimer && clearInterval(this.autoPlayTimer)
    }, s.prototype.autoPlayIterator = function () {
        var e = this,
            t = e.currentSlide + e.options.slidesToScroll;
        e.paused || e.interrupted || e.focussed || (!1 === e.options.infinite && (1 === e.direction && e.currentSlide + 1 === e.slideCount - 1 ? e.direction = 0 : 0 === e.direction && (t = e.currentSlide - e.options.slidesToScroll, e.currentSlide - 1 == 0 && (e.direction = 1))), e.slideHandler(t))
    }, s.prototype.buildArrows = function () {
        var e = this;
        !0 === e.options.arrows && (e.$prevArrow = c(e.options.prevArrow).addClass("slick-arrow"), e.$nextArrow = c(e.options.nextArrow).addClass("slick-arrow"), e.slideCount > e.options.slidesToShow ? (e.$prevArrow.removeClass("slick-hidden").removeAttr("aria-hidden tabindex"), e.$nextArrow.removeClass("slick-hidden").removeAttr("aria-hidden tabindex"), e.htmlExpr.test(e.options.prevArrow) && e.$prevArrow.prependTo(e.options.appendArrows), e.htmlExpr.test(e.options.nextArrow) && e.$nextArrow.appendTo(e.options.appendArrows), !0 !== e.options.infinite && e.$prevArrow.addClass("slick-disabled").attr("aria-disabled", "true")) : e.$prevArrow.add(e.$nextArrow).addClass("slick-hidden").attr({
            "aria-disabled": "true",
            tabindex: "-1"
        }))
    }, s.prototype.buildDots = function () {
        var e, t, n = this;
        if (!0 === n.options.dots) {
            for (n.$slider.addClass("slick-dotted"), t = c("<ul />").addClass(n.options.dotsClass), e = 0; e <= n.getDotCount(); e += 1) t.append(c("<li />").append(n.options.customPaging.call(this, n, e)));
            n.$dots = t.appendTo(n.options.appendDots), n.$dots.find("li").first().addClass("slick-active")
        }
    }, s.prototype.buildOut = function () {
        var e = this;
        e.$slides = e.$slider.children(e.options.slide + ":not(.slick-cloned)").addClass("slick-slide"), e.slideCount = e.$slides.length, e.$slides.each(function (e, t) {
            c(t).attr("data-slick-index", e).data("originalStyling", c(t).attr("style") || "")
        }), e.$slider.addClass("slick-slider"), e.$slideTrack = 0 === e.slideCount ? c('<div class="slick-track"/>').appendTo(e.$slider) : e.$slides.wrapAll('<div class="slick-track"/>').parent(), e.$list = e.$slideTrack.wrap('<div class="slick-list"/>').parent(), e.$slideTrack.css("opacity", 0), !0 !== e.options.centerMode && !0 !== e.options.swipeToSlide || (e.options.slidesToScroll = 1), c("img[data-lazy]", e.$slider).not("[src]").addClass("slick-loading"), e.setupInfinite(), e.buildArrows(), e.buildDots(), e.updateDots(), e.setSlideClasses("number" == typeof e.currentSlide ? e.currentSlide : 0), !0 === e.options.draggable && e.$list.addClass("draggable")
    }, s.prototype.buildRows = function () {
        var e, t, n, i, o, r, s, a = this;
        if (i = document.createDocumentFragment(), r = a.$slider.children(), 1 < a.options.rows) {
            for (s = a.options.slidesPerRow * a.options.rows, o = Math.ceil(r.length / s), e = 0; e < o; e++) {
                var l = document.createElement("div");
                for (t = 0; t < a.options.rows; t++) {
                    var c = document.createElement("div");
                    for (n = 0; n < a.options.slidesPerRow; n++) {
                        var d = e * s + (t * a.options.slidesPerRow + n);
                        r.get(d) && c.appendChild(r.get(d))
                    }
                    l.appendChild(c)
                }
                i.appendChild(l)
            }
            a.$slider.empty().append(i), a.$slider.children().children().children().css({
                width: 100 / a.options.slidesPerRow + "%",
                display: "inline-block"
            })
        }
    }, s.prototype.checkResponsive = function (e, t) {
        var n, i, o, r = this,
            s = !1,
            a = r.$slider.width(),
            l = window.innerWidth || c(window).width();
        if ("window" === r.respondTo ? o = l : "slider" === r.respondTo ? o = a : "min" === r.respondTo && (o = Math.min(l, a)), r.options.responsive && r.options.responsive.length && null !== r.options.responsive) {
            for (n in i = null, r.breakpoints) r.breakpoints.hasOwnProperty(n) && (!1 === r.originalSettings.mobileFirst ? o < r.breakpoints[n] && (i = r.breakpoints[n]) : o > r.breakpoints[n] && (i = r.breakpoints[n]));
            null !== i ? null !== r.activeBreakpoint && i === r.activeBreakpoint && !t || (r.activeBreakpoint = i, "unslick" === r.breakpointSettings[i] ? r.unslick(i) : (r.options = c.extend({}, r.originalSettings, r.breakpointSettings[i]), !0 === e && (r.currentSlide = r.options.initialSlide), r.refresh(e)), s = i) : null !== r.activeBreakpoint && (r.activeBreakpoint = null, r.options = r.originalSettings, !0 === e && (r.currentSlide = r.options.initialSlide), r.refresh(e), s = i), e || !1 === s || r.$slider.trigger("breakpoint", [r, s])
        }
    }, s.prototype.changeSlide = function (e, t) {
        var n, i, o = this,
            r = c(e.currentTarget);
        switch (r.is("a") && e.preventDefault(), r.is("li") || (r = r.closest("li")), n = o.slideCount % o.options.slidesToScroll != 0 ? 0 : (o.slideCount - o.currentSlide) % o.options.slidesToScroll, e.data.message) {
            case "previous":
                i = 0 == n ? o.options.slidesToScroll : o.options.slidesToShow - n, o.slideCount > o.options.slidesToShow && o.slideHandler(o.currentSlide - i, !1, t);
                break;
            case "next":
                i = 0 == n ? o.options.slidesToScroll : n, o.slideCount > o.options.slidesToShow && o.slideHandler(o.currentSlide + i, !1, t);
                break;
            case "index":
                var s = 0 === e.data.index ? 0 : e.data.index || r.index() * o.options.slidesToScroll;
                o.slideHandler(o.checkNavigable(s), !1, t), r.children().trigger("focus");
                break;
            default:
                return
        }
    }, s.prototype.checkNavigable = function (e) {
        var t, n;
        if (n = 0, e > (t = this.getNavigableIndexes())[t.length - 1]) e = t[t.length - 1];
        else
            for (var i in t) {
                if (e < t[i]) {
                    e = n;
                    break
                }
                n = t[i]
            }
        return e
    }, s.prototype.cleanUpEvents = function () {
        var e = this;
        e.options.dots && null !== e.$dots && (c("li", e.$dots).off("click.slick", e.changeSlide).off("mouseenter.slick", c.proxy(e.interrupt, e, !0)).off("mouseleave.slick", c.proxy(e.interrupt, e, !1)), !0 === e.options.accessibility && e.$dots.off("keydown.slick", e.keyHandler)), e.$slider.off("focus.slick blur.slick"), !0 === e.options.arrows && e.slideCount > e.options.slidesToShow && (e.$prevArrow && e.$prevArrow.off("click.slick", e.changeSlide), e.$nextArrow && e.$nextArrow.off("click.slick", e.changeSlide), !0 === e.options.accessibility && (e.$prevArrow && e.$prevArrow.off("keydown.slick", e.keyHandler), e.$nextArrow && e.$nextArrow.off("keydown.slick", e.keyHandler))), e.$list.off("touchstart.slick mousedown.slick", e.swipeHandler), e.$list.off("touchmove.slick mousemove.slick", e.swipeHandler), e.$list.off("touchend.slick mouseup.slick", e.swipeHandler), e.$list.off("touchcancel.slick mouseleave.slick", e.swipeHandler), e.$list.off("click.slick", e.clickHandler), c(document).off(e.visibilityChange, e.visibility), e.cleanUpSlideEvents(), !0 === e.options.accessibility && e.$list.off("keydown.slick", e.keyHandler), !0 === e.options.focusOnSelect && c(e.$slideTrack).children().off("click.slick", e.selectHandler), c(window).off("orientationchange.slick.slick-" + e.instanceUid, e.orientationChange), c(window).off("resize.slick.slick-" + e.instanceUid, e.resize), c("[draggable!=true]", e.$slideTrack).off("dragstart", e.preventDefault), c(window).off("load.slick.slick-" + e.instanceUid, e.setPosition)
    }, s.prototype.cleanUpSlideEvents = function () {
        var e = this;
        e.$list.off("mouseenter.slick", c.proxy(e.interrupt, e, !0)), e.$list.off("mouseleave.slick", c.proxy(e.interrupt, e, !1))
    }, s.prototype.cleanUpRows = function () {
        var e;
        1 < this.options.rows && ((e = this.$slides.children().children()).removeAttr("style"), this.$slider.empty().append(e))
    }, s.prototype.clickHandler = function (e) {
        !1 === this.shouldClick && (e.stopImmediatePropagation(), e.stopPropagation(), e.preventDefault())
    }, s.prototype.destroy = function (e) {
        var t = this;
        t.autoPlayClear(), t.touchObject = {}, t.cleanUpEvents(), c(".slick-cloned", t.$slider).detach(), t.$dots && t.$dots.remove(), t.$prevArrow && t.$prevArrow.length && (t.$prevArrow.removeClass("slick-disabled slick-arrow slick-hidden").removeAttr("aria-hidden aria-disabled tabindex").css("display", ""), t.htmlExpr.test(t.options.prevArrow) && t.$prevArrow.remove()), t.$nextArrow && t.$nextArrow.length && (t.$nextArrow.removeClass("slick-disabled slick-arrow slick-hidden").removeAttr("aria-hidden aria-disabled tabindex").css("display", ""), t.htmlExpr.test(t.options.nextArrow) && t.$nextArrow.remove()), t.$slides && (t.$slides.removeClass("slick-slide slick-active slick-center slick-visible slick-current").removeAttr("aria-hidden").removeAttr("data-slick-index").each(function () {
            c(this).attr("style", c(this).data("originalStyling"))
        }), t.$slideTrack.children(this.options.slide).detach(), t.$slideTrack.detach(), t.$list.detach(), t.$slider.append(t.$slides)), t.cleanUpRows(), t.$slider.removeClass("slick-slider"), t.$slider.removeClass("slick-initialized"), t.$slider.removeClass("slick-dotted"), t.unslicked = !0, e || t.$slider.trigger("destroy", [t])
    }, s.prototype.disableTransition = function (e) {
        var t = {};
        t[this.transitionType] = "", !1 === this.options.fade ? this.$slideTrack.css(t) : this.$slides.eq(e).css(t)
    }, s.prototype.fadeSlide = function (e, t) {
        var n = this;
        !1 === n.cssTransitions ? (n.$slides.eq(e).css({
            zIndex: n.options.zIndex
        }), n.$slides.eq(e).animate({
            opacity: 1
        }, n.options.speed, n.options.easing, t)) : (n.applyTransition(e), n.$slides.eq(e).css({
            opacity: 1,
            zIndex: n.options.zIndex
        }), t && setTimeout(function () {
            n.disableTransition(e), t.call()
        }, n.options.speed))
    }, s.prototype.fadeSlideOut = function (e) {
        var t = this;
        !1 === t.cssTransitions ? t.$slides.eq(e).animate({
            opacity: 0,
            zIndex: t.options.zIndex - 2
        }, t.options.speed, t.options.easing) : (t.applyTransition(e), t.$slides.eq(e).css({
            opacity: 0,
            zIndex: t.options.zIndex - 2
        }))
    }, s.prototype.filterSlides = s.prototype.slickFilter = function (e) {
        var t = this;
        null !== e && (t.$slidesCache = t.$slides, t.unload(), t.$slideTrack.children(this.options.slide).detach(), t.$slidesCache.filter(e).appendTo(t.$slideTrack), t.reinit())
    }, s.prototype.focusHandler = function () {
        var n = this;
        n.$slider.off("focus.slick blur.slick").on("focus.slick blur.slick", "*", function (e) {
            e.stopImmediatePropagation();
            var t = c(this);
            setTimeout(function () {
                n.options.pauseOnFocus && (n.focussed = t.is(":focus"), n.autoPlay())
            }, 0)
        })
    }, s.prototype.getCurrent = s.prototype.slickCurrentSlide = function () {
        return this.currentSlide
    }, s.prototype.getDotCount = function () {
        var e = this,
            t = 0,
            n = 0,
            i = 0;
        if (!0 === e.options.infinite)
            if (e.slideCount <= e.options.slidesToShow) ++i;
            else
                for (; t < e.slideCount;) ++i, t = n + e.options.slidesToScroll, n += e.options.slidesToScroll <= e.options.slidesToShow ? e.options.slidesToScroll : e.options.slidesToShow;
        else if (!0 === e.options.centerMode) i = e.slideCount;
        else if (e.options.asNavFor)
            for (; t < e.slideCount;) ++i, t = n + e.options.slidesToScroll, n += e.options.slidesToScroll <= e.options.slidesToShow ? e.options.slidesToScroll : e.options.slidesToShow;
        else i = 1 + Math.ceil((e.slideCount - e.options.slidesToShow) / e.options.slidesToScroll);
        return i - 1
    }, s.prototype.getLeft = function (e) {
        var t, n, i, o, r = this,
            s = 0;
        return r.slideOffset = 0, n = r.$slides.first().outerHeight(!0), !0 === r.options.infinite ? (r.slideCount > r.options.slidesToShow && (r.slideOffset = r.slideWidth * r.options.slidesToShow * -1, o = -1, !0 === r.options.vertical && !0 === r.options.centerMode && (2 === r.options.slidesToShow ? o = -1.5 : 1 === r.options.slidesToShow && (o = -2)), s = n * r.options.slidesToShow * o), r.slideCount % r.options.slidesToScroll != 0 && e + r.options.slidesToScroll > r.slideCount && r.slideCount > r.options.slidesToShow && (s = e > r.slideCount ? (r.slideOffset = (r.options.slidesToShow - (e - r.slideCount)) * r.slideWidth * -1, (r.options.slidesToShow - (e - r.slideCount)) * n * -1) : (r.slideOffset = r.slideCount % r.options.slidesToScroll * r.slideWidth * -1, r.slideCount % r.options.slidesToScroll * n * -1))) : e + r.options.slidesToShow > r.slideCount && (r.slideOffset = (e + r.options.slidesToShow - r.slideCount) * r.slideWidth, s = (e + r.options.slidesToShow - r.slideCount) * n), r.slideCount <= r.options.slidesToShow && (s = r.slideOffset = 0), !0 === r.options.centerMode && r.slideCount <= r.options.slidesToShow ? r.slideOffset = r.slideWidth * Math.floor(r.options.slidesToShow) / 2 - r.slideWidth * r.slideCount / 2 : !0 === r.options.centerMode && !0 === r.options.infinite ? r.slideOffset += r.slideWidth * Math.floor(r.options.slidesToShow / 2) - r.slideWidth : !0 === r.options.centerMode && (r.slideOffset = 0, r.slideOffset += r.slideWidth * Math.floor(r.options.slidesToShow / 2)), t = !1 === r.options.vertical ? e * r.slideWidth * -1 + r.slideOffset : e * n * -1 + s, !0 === r.options.variableWidth && (i = r.slideCount <= r.options.slidesToShow || !1 === r.options.infinite ? r.$slideTrack.children(".slick-slide").eq(e) : r.$slideTrack.children(".slick-slide").eq(e + r.options.slidesToShow), t = !0 === r.options.rtl ? i[0] ? -1 * (r.$slideTrack.width() - i[0].offsetLeft - i.width()) : 0 : i[0] ? -1 * i[0].offsetLeft : 0, !0 === r.options.centerMode && (i = r.slideCount <= r.options.slidesToShow || !1 === r.options.infinite ? r.$slideTrack.children(".slick-slide").eq(e) : r.$slideTrack.children(".slick-slide").eq(e + r.options.slidesToShow + 1), t = !0 === r.options.rtl ? i[0] ? -1 * (r.$slideTrack.width() - i[0].offsetLeft - i.width()) : 0 : i[0] ? -1 * i[0].offsetLeft : 0, t += (r.$list.width() - i.outerWidth()) / 2)), t
    }, s.prototype.getOption = s.prototype.slickGetOption = function (e) {
        return this.options[e]
    }, s.prototype.getNavigableIndexes = function () {
        var e, t = this,
            n = 0,
            i = 0,
            o = [];
        for (e = !1 === t.options.infinite ? t.slideCount : (n = -1 * t.options.slidesToScroll, i = -1 * t.options.slidesToScroll, 2 * t.slideCount); n < e;) o.push(n), n = i + t.options.slidesToScroll, i += t.options.slidesToScroll <= t.options.slidesToShow ? t.options.slidesToScroll : t.options.slidesToShow;
        return o
    }, s.prototype.getSlick = function () {
        return this
    }, s.prototype.getSlideCount = function () {
        var n, i, o = this;
        return i = !0 === o.options.centerMode ? o.slideWidth * Math.floor(o.options.slidesToShow / 2) : 0, !0 === o.options.swipeToSlide ? (o.$slideTrack.find(".slick-slide").each(function (e, t) {
            if (t.offsetLeft - i + c(t).outerWidth() / 2 > -1 * o.swipeLeft) return n = t, !1
        }), Math.abs(c(n).attr("data-slick-index") - o.currentSlide) || 1) : o.options.slidesToScroll
    }, s.prototype.goTo = s.prototype.slickGoTo = function (e, t) {
        this.changeSlide({
            data: {
                message: "index",
                index: parseInt(e)
            }
        }, t)
    }, s.prototype.init = function (e) {
        var t = this;
        c(t.$slider).hasClass("slick-initialized") || (c(t.$slider).addClass("slick-initialized"), t.buildRows(), t.buildOut(), t.setProps(), t.startLoad(), t.loadSlider(), t.initializeEvents(), t.updateArrows(), t.updateDots(), t.checkResponsive(!0), t.focusHandler()), e && t.$slider.trigger("init", [t]), !0 === t.options.accessibility && t.initADA(), t.options.autoplay && (t.paused = !1, t.autoPlay())
    }, s.prototype.initADA = function () {
        var n = this,
            i = Math.ceil(n.slideCount / n.options.slidesToShow),
            o = n.getNavigableIndexes().filter(function (e) {
                return 0 <= e && e < n.slideCount
            });
        n.$slides.add(n.$slideTrack.find(".slick-cloned")).attr({
            "aria-hidden": "true",
            tabindex: "-1"
        }).find("a, input, button, select").attr({
            tabindex: "-1"
        }), null !== n.$dots && (n.$slides.not(n.$slideTrack.find(".slick-cloned")).each(function (e) {
            var t = o.indexOf(e);
            c(this).attr({
                role: "tabpanel",
                id: "slick-slide" + n.instanceUid + e,
                tabindex: -1
            }), -1 !== t && c(this).attr({
                "aria-describedby": "slick-slide-control" + n.instanceUid + t
            })
        }), n.$dots.attr("role", "tablist").find("li").each(function (e) {
            var t = o[e];
            c(this).attr({
                role: "presentation"
            }), c(this).find("button").first().attr({
                role: "tab",
                id: "slick-slide-control" + n.instanceUid + e,
                "aria-controls": "slick-slide" + n.instanceUid + t,
                "aria-label": e + 1 + " of " + i,
                "aria-selected": null,
                tabindex: "-1"
            })
        }).eq(n.currentSlide).find("button").attr({
            "aria-selected": "true",
            tabindex: "0"
        }).end());
        for (var e = n.currentSlide, t = e + n.options.slidesToShow; e < t; e++) n.$slides.eq(e).attr("tabindex", 0);
        n.activateADA()
    }, s.prototype.initArrowEvents = function () {
        var e = this;
        !0 === e.options.arrows && e.slideCount > e.options.slidesToShow && (e.$prevArrow.off("click.slick").on("click.slick", {
            message: "previous"
        }, e.changeSlide), e.$nextArrow.off("click.slick").on("click.slick", {
            message: "next"
        }, e.changeSlide), !0 === e.options.accessibility && (e.$prevArrow.on("keydown.slick", e.keyHandler), e.$nextArrow.on("keydown.slick", e.keyHandler)))
    }, s.prototype.initDotEvents = function () {
        var e = this;
        !0 === e.options.dots && (c("li", e.$dots).on("click.slick", {
            message: "index"
        }, e.changeSlide), !0 === e.options.accessibility && e.$dots.on("keydown.slick", e.keyHandler)), !0 === e.options.dots && !0 === e.options.pauseOnDotsHover && c("li", e.$dots).on("mouseenter.slick", c.proxy(e.interrupt, e, !0)).on("mouseleave.slick", c.proxy(e.interrupt, e, !1))
    }, s.prototype.initSlideEvents = function () {
        var e = this;
        e.options.pauseOnHover && (e.$list.on("mouseenter.slick", c.proxy(e.interrupt, e, !0)), e.$list.on("mouseleave.slick", c.proxy(e.interrupt, e, !1)))
    }, s.prototype.initializeEvents = function () {
        var e = this;
        e.initArrowEvents(), e.initDotEvents(), e.initSlideEvents(), e.$list.on("touchstart.slick mousedown.slick", {
            action: "start"
        }, e.swipeHandler), e.$list.on("touchmove.slick mousemove.slick", {
            action: "move"
        }, e.swipeHandler), e.$list.on("touchend.slick mouseup.slick", {
            action: "end"
        }, e.swipeHandler), e.$list.on("touchcancel.slick mouseleave.slick", {
            action: "end"
        }, e.swipeHandler), e.$list.on("click.slick", e.clickHandler), c(document).on(e.visibilityChange, c.proxy(e.visibility, e)), !0 === e.options.accessibility && e.$list.on("keydown.slick", e.keyHandler), !0 === e.options.focusOnSelect && c(e.$slideTrack).children().on("click.slick", e.selectHandler), c(window).on("orientationchange.slick.slick-" + e.instanceUid, c.proxy(e.orientationChange, e)), c(window).on("resize.slick.slick-" + e.instanceUid, c.proxy(e.resize, e)), c("[draggable!=true]", e.$slideTrack).on("dragstart", e.preventDefault), c(window).on("load.slick.slick-" + e.instanceUid, e.setPosition), c(e.setPosition)
    }, s.prototype.initUI = function () {
        var e = this;
        !0 === e.options.arrows && e.slideCount > e.options.slidesToShow && (e.$prevArrow.show(), e.$nextArrow.show()), !0 === e.options.dots && e.slideCount > e.options.slidesToShow && e.$dots.show()
    }, s.prototype.keyHandler = function (e) {
        var t = this;
        e.target.tagName.match("TEXTAREA|INPUT|SELECT") || (37 === e.keyCode && !0 === t.options.accessibility ? t.changeSlide({
            data: {
                message: !0 === t.options.rtl ? "next" : "previous"
            }
        }) : 39 === e.keyCode && !0 === t.options.accessibility && t.changeSlide({
            data: {
                message: !0 === t.options.rtl ? "previous" : "next"
            }
        }))
    }, s.prototype.lazyLoad = function () {
        function e(e) {
            c("img[data-lazy]", e).each(function () {
                var e = c(this),
                    t = c(this).attr("data-lazy"),
                    n = c(this).attr("data-srcset"),
                    i = c(this).attr("data-sizes") || r.$slider.attr("data-sizes"),
                    o = document.createElement("img");
                o.onload = function () {
                    e.animate({
                        opacity: 0
                    }, 100, function () {
                        n && (e.attr("srcset", n), i && e.attr("sizes", i)), e.attr("src", t).animate({
                            opacity: 1
                        }, 200, function () {
                            e.removeAttr("data-lazy data-srcset data-sizes").removeClass("slick-loading")
                        }), r.$slider.trigger("lazyLoaded", [r, e, t])
                    })
                }, o.onerror = function () {
                    e.removeAttr("data-lazy").removeClass("slick-loading").addClass("slick-lazyload-error"), r.$slider.trigger("lazyLoadError", [r, e, t])
                }, o.src = t
            })
        }
        var t, n, i, r = this;
        if (!0 === r.options.centerMode ? i = !0 === r.options.infinite ? (n = r.currentSlide + (r.options.slidesToShow / 2 + 1)) + r.options.slidesToShow + 2 : (n = Math.max(0, r.currentSlide - (r.options.slidesToShow / 2 + 1)), r.options.slidesToShow / 2 + 1 + 2 + r.currentSlide) : (n = r.options.infinite ? r.options.slidesToShow + r.currentSlide : r.currentSlide, i = Math.ceil(n + r.options.slidesToShow), !0 === r.options.fade && (0 < n && n--, i <= r.slideCount && i++)), t = r.$slider.find(".slick-slide").slice(n, i), "anticipated" === r.options.lazyLoad)
            for (var o = n - 1, s = i, a = r.$slider.find(".slick-slide"), l = 0; l < r.options.slidesToScroll; l++) o < 0 && (o = r.slideCount - 1), t = (t = t.add(a.eq(o))).add(a.eq(s)), o--, s++;
        e(t), r.slideCount <= r.options.slidesToShow ? e(r.$slider.find(".slick-slide")) : r.currentSlide >= r.slideCount - r.options.slidesToShow ? e(r.$slider.find(".slick-cloned").slice(0, r.options.slidesToShow)) : 0 === r.currentSlide && e(r.$slider.find(".slick-cloned").slice(-1 * r.options.slidesToShow))
    }, s.prototype.loadSlider = function () {
        var e = this;
        e.setPosition(), e.$slideTrack.css({
            opacity: 1
        }), e.$slider.removeClass("slick-loading"), e.initUI(), "progressive" === e.options.lazyLoad && e.progressiveLazyLoad()
    }, s.prototype.next = s.prototype.slickNext = function () {
        this.changeSlide({
            data: {
                message: "next"
            }
        })
    }, s.prototype.orientationChange = function () {
        this.checkResponsive(), this.setPosition()
    }, s.prototype.pause = s.prototype.slickPause = function () {
        this.autoPlayClear(), this.paused = !0
    }, s.prototype.play = s.prototype.slickPlay = function () {
        var e = this;
        e.autoPlay(), e.options.autoplay = !0, e.paused = !1, e.focussed = !1, e.interrupted = !1
    }, s.prototype.postSlide = function (e) {
        var t = this;
        t.unslicked || (t.$slider.trigger("afterChange", [t, e]), t.animating = !1, t.slideCount > t.options.slidesToShow && t.setPosition(), t.swipeLeft = null, t.options.autoplay && t.autoPlay(), !0 === t.options.accessibility && (t.initADA(), t.options.focusOnChange && c(t.$slides.get(t.currentSlide)).attr("tabindex", 0).focus()))
    }, s.prototype.prev = s.prototype.slickPrev = function () {
        this.changeSlide({
            data: {
                message: "previous"
            }
        })
    }, s.prototype.preventDefault = function (e) {
        e.preventDefault()
    }, s.prototype.progressiveLazyLoad = function (e) {
        e = e || 1;
        var t, n, i, o, r, s = this,
            a = c("img[data-lazy]", s.$slider);
        a.length ? (t = a.first(), n = t.attr("data-lazy"), i = t.attr("data-srcset"), o = t.attr("data-sizes") || s.$slider.attr("data-sizes"), (r = document.createElement("img")).onload = function () {
            i && (t.attr("srcset", i), o && t.attr("sizes", o)), t.attr("src", n).removeAttr("data-lazy data-srcset data-sizes").removeClass("slick-loading"), !0 === s.options.adaptiveHeight && s.setPosition(), s.$slider.trigger("lazyLoaded", [s, t, n]), s.progressiveLazyLoad()
        }, r.onerror = function () {
            e < 3 ? setTimeout(function () {
                s.progressiveLazyLoad(e + 1)
            }, 500) : (t.removeAttr("data-lazy").removeClass("slick-loading").addClass("slick-lazyload-error"), s.$slider.trigger("lazyLoadError", [s, t, n]), s.progressiveLazyLoad())
        }, r.src = n) : s.$slider.trigger("allImagesLoaded", [s])
    }, s.prototype.refresh = function (e) {
        var t, n, i = this;
        n = i.slideCount - i.options.slidesToShow, !i.options.infinite && i.currentSlide > n && (i.currentSlide = n), i.slideCount <= i.options.slidesToShow && (i.currentSlide = 0), t = i.currentSlide, i.destroy(!0), c.extend(i, i.initials, {
            currentSlide: t
        }), i.init(), e || i.changeSlide({
            data: {
                message: "index",
                index: t
            }
        }, !1)
    }, s.prototype.registerBreakpoints = function () {
        var e, t, n, i = this,
            o = i.options.responsive || null;
        if ("array" === c.type(o) && o.length) {
            for (e in i.respondTo = i.options.respondTo || "window", o)
                if (n = i.breakpoints.length - 1, o.hasOwnProperty(e)) {
                    for (t = o[e].breakpoint; 0 <= n;) i.breakpoints[n] && i.breakpoints[n] === t && i.breakpoints.splice(n, 1), n--;
                    i.breakpoints.push(t), i.breakpointSettings[t] = o[e].settings
                } i.breakpoints.sort(function (e, t) {
                return i.options.mobileFirst ? e - t : t - e
            })
        }
    }, s.prototype.reinit = function () {
        var e = this;
        e.$slides = e.$slideTrack.children(e.options.slide).addClass("slick-slide"), e.slideCount = e.$slides.length, e.currentSlide >= e.slideCount && 0 !== e.currentSlide && (e.currentSlide = e.currentSlide - e.options.slidesToScroll), e.slideCount <= e.options.slidesToShow && (e.currentSlide = 0), e.registerBreakpoints(), e.setProps(), e.setupInfinite(), e.buildArrows(), e.updateArrows(), e.initArrowEvents(), e.buildDots(), e.updateDots(), e.initDotEvents(), e.cleanUpSlideEvents(), e.initSlideEvents(), e.checkResponsive(!1, !0), !0 === e.options.focusOnSelect && c(e.$slideTrack).children().on("click.slick", e.selectHandler), e.setSlideClasses("number" == typeof e.currentSlide ? e.currentSlide : 0), e.setPosition(), e.focusHandler(), e.paused = !e.options.autoplay, e.autoPlay(), e.$slider.trigger("reInit", [e])
    }, s.prototype.resize = function () {
        var e = this;
        c(window).width() !== e.windowWidth && (clearTimeout(e.windowDelay), e.windowDelay = window.setTimeout(function () {
            e.windowWidth = c(window).width(), e.checkResponsive(), e.unslicked || e.setPosition()
        }, 50))
    }, s.prototype.removeSlide = s.prototype.slickRemove = function (e, t, n) {
        var i = this;
        if (e = "boolean" == typeof e ? !0 === (t = e) ? 0 : i.slideCount - 1 : !0 === t ? --e : e, i.slideCount < 1 || e < 0 || e > i.slideCount - 1) return !1;
        i.unload(), !0 === n ? i.$slideTrack.children().remove() : i.$slideTrack.children(this.options.slide).eq(e).remove(), i.$slides = i.$slideTrack.children(this.options.slide), i.$slideTrack.children(this.options.slide).detach(), i.$slideTrack.append(i.$slides), i.$slidesCache = i.$slides, i.reinit()
    }, s.prototype.setCSS = function (e) {
        var t, n, i = this,
            o = {};
        !0 === i.options.rtl && (e = -e), t = "left" == i.positionProp ? Math.ceil(e) + "px" : "0px", n = "top" == i.positionProp ? Math.ceil(e) + "px" : "0px", o[i.positionProp] = e, !1 === i.transformsEnabled || (!(o = {}) === i.cssTransitions ? o[i.animType] = "translate(" + t + ", " + n + ")" : o[i.animType] = "translate3d(" + t + ", " + n + ", 0px)"), i.$slideTrack.css(o)
    }, s.prototype.setDimensions = function () {
        var e = this;
        !1 === e.options.vertical ? !0 === e.options.centerMode && e.$list.css({
            padding: "0px " + e.options.centerPadding
        }) : (e.$list.height(e.$slides.first().outerHeight(!0) * e.options.slidesToShow), !0 === e.options.centerMode && e.$list.css({
            padding: e.options.centerPadding + " 0px"
        })), e.listWidth = e.$list.width(), e.listHeight = e.$list.height(), !1 === e.options.vertical && !1 === e.options.variableWidth ? (e.slideWidth = Math.ceil(e.listWidth / e.options.slidesToShow), e.$slideTrack.width(Math.ceil(e.slideWidth * e.$slideTrack.children(".slick-slide").length))) : !0 === e.options.variableWidth ? e.$slideTrack.width(5e3 * e.slideCount) : (e.slideWidth = Math.ceil(e.listWidth), e.$slideTrack.height(Math.ceil(e.$slides.first().outerHeight(!0) * e.$slideTrack.children(".slick-slide").length)));
        var t = e.$slides.first().outerWidth(!0) - e.$slides.first().width();
        !1 === e.options.variableWidth && e.$slideTrack.children(".slick-slide").width(e.slideWidth - t)
    }, s.prototype.setFade = function () {
        var n, i = this;
        i.$slides.each(function (e, t) {
            n = i.slideWidth * e * -1, !0 === i.options.rtl ? c(t).css({
                position: "relative",
                right: n,
                top: 0,
                zIndex: i.options.zIndex - 2,
                opacity: 0
            }) : c(t).css({
                position: "relative",
                left: n,
                top: 0,
                zIndex: i.options.zIndex - 2,
                opacity: 0
            })
        }), i.$slides.eq(i.currentSlide).css({
            zIndex: i.options.zIndex - 1,
            opacity: 1
        })
    }, s.prototype.setHeight = function () {
        var e = this;
        if (1 === e.options.slidesToShow && !0 === e.options.adaptiveHeight && !1 === e.options.vertical) {
            var t = e.$slides.eq(e.currentSlide).outerHeight(!0);
            e.$list.css("height", t)
        }
    }, s.prototype.setOption = s.prototype.slickSetOption = function () {
        var e, t, n, i, o, r = this,
            s = !1;
        if ("object" === c.type(arguments[0]) ? (n = arguments[0], s = arguments[1], o = "multiple") : "string" === c.type(arguments[0]) && (i = arguments[1], s = arguments[2], "responsive" === (n = arguments[0]) && "array" === c.type(arguments[1]) ? o = "responsive" : void 0 !== arguments[1] && (o = "single")), "single" === o) r.options[n] = i;
        else if ("multiple" === o) c.each(n, function (e, t) {
            r.options[e] = t
        });
        else if ("responsive" === o)
            for (t in i)
                if ("array" !== c.type(r.options.responsive)) r.options.responsive = [i[t]];
                else {
                    for (e = r.options.responsive.length - 1; 0 <= e;) r.options.responsive[e].breakpoint === i[t].breakpoint && r.options.responsive.splice(e, 1), e--;
                    r.options.responsive.push(i[t])
                } s && (r.unload(), r.reinit())
    }, s.prototype.setPosition = function () {
        var e = this;
        e.setDimensions(), e.setHeight(), !1 === e.options.fade ? e.setCSS(e.getLeft(e.currentSlide)) : e.setFade(), e.$slider.trigger("setPosition", [e])
    }, s.prototype.setProps = function () {
        var e = this,
            t = document.body.style;
        e.positionProp = !0 === e.options.vertical ? "top" : "left", "top" === e.positionProp ? e.$slider.addClass("slick-vertical") : e.$slider.removeClass("slick-vertical"), void 0 === t.WebkitTransition && void 0 === t.MozTransition && void 0 === t.msTransition || !0 === e.options.useCSS && (e.cssTransitions = !0), e.options.fade && ("number" == typeof e.options.zIndex ? e.options.zIndex < 3 && (e.options.zIndex = 3) : e.options.zIndex = e.defaults.zIndex), void 0 !== t.OTransform && (e.animType = "OTransform", e.transformType = "-o-transform", e.transitionType = "OTransition", void 0 === t.perspectiveProperty && void 0 === t.webkitPerspective && (e.animType = !1)), void 0 !== t.MozTransform && (e.animType = "MozTransform", e.transformType = "-moz-transform", e.transitionType = "MozTransition", void 0 === t.perspectiveProperty && void 0 === t.MozPerspective && (e.animType = !1)), void 0 !== t.webkitTransform && (e.animType = "webkitTransform", e.transformType = "-webkit-transform", e.transitionType = "webkitTransition", void 0 === t.perspectiveProperty && void 0 === t.webkitPerspective && (e.animType = !1)), void 0 !== t.msTransform && (e.animType = "msTransform", e.transformType = "-ms-transform", e.transitionType = "msTransition", void 0 === t.msTransform && (e.animType = !1)), void 0 !== t.transform && !1 !== e.animType && (e.animType = "transform", e.transformType = "transform", e.transitionType = "transition"), e.transformsEnabled = e.options.useTransform && null !== e.animType && !1 !== e.animType
    }, s.prototype.setSlideClasses = function (e) {
        var t, n, i, o, r = this;
        if (n = r.$slider.find(".slick-slide").removeClass("slick-active slick-center slick-current").attr("aria-hidden", "true"), r.$slides.eq(e).addClass("slick-current"), !0 === r.options.centerMode) {
            var s = r.options.slidesToShow % 2 == 0 ? 1 : 0;
            t = Math.floor(r.options.slidesToShow / 2), !0 === r.options.infinite && (t <= e && e <= r.slideCount - 1 - t ? r.$slides.slice(e - t + s, e + t + 1).addClass("slick-active").attr("aria-hidden", "false") : (i = r.options.slidesToShow + e, n.slice(i - t + 1 + s, i + t + 2).addClass("slick-active").attr("aria-hidden", "false")), 0 === e ? n.eq(n.length - 1 - r.options.slidesToShow).addClass("slick-center") : e === r.slideCount - 1 && n.eq(r.options.slidesToShow).addClass("slick-center")), r.$slides.eq(e).addClass("slick-center")
        } else 0 <= e && e <= r.slideCount - r.options.slidesToShow ? r.$slides.slice(e, e + r.options.slidesToShow).addClass("slick-active").attr("aria-hidden", "false") : n.length <= r.options.slidesToShow ? n.addClass("slick-active").attr("aria-hidden", "false") : (o = r.slideCount % r.options.slidesToShow, i = !0 === r.options.infinite ? r.options.slidesToShow + e : e, r.options.slidesToShow == r.options.slidesToScroll && r.slideCount - e < r.options.slidesToShow ? n.slice(i - (r.options.slidesToShow - o), i + o).addClass("slick-active").attr("aria-hidden", "false") : n.slice(i, i + r.options.slidesToShow).addClass("slick-active").attr("aria-hidden", "false"));
        "ondemand" !== r.options.lazyLoad && "anticipated" !== r.options.lazyLoad || r.lazyLoad()
    }, s.prototype.setupInfinite = function () {
        var e, t, n, i = this;
        if (!0 === i.options.fade && (i.options.centerMode = !1), !0 === i.options.infinite && !1 === i.options.fade && (t = null, i.slideCount > i.options.slidesToShow)) {
            for (n = !0 === i.options.centerMode ? i.options.slidesToShow + 1 : i.options.slidesToShow, e = i.slideCount; e > i.slideCount - n; e -= 1) t = e - 1, c(i.$slides[t]).clone(!0).attr("id", "").attr("data-slick-index", t - i.slideCount).prependTo(i.$slideTrack).addClass("slick-cloned");
            for (e = 0; e < n + i.slideCount; e += 1) t = e, c(i.$slides[t]).clone(!0).attr("id", "").attr("data-slick-index", t + i.slideCount).appendTo(i.$slideTrack).addClass("slick-cloned");
            i.$slideTrack.find(".slick-cloned").find("[id]").each(function () {
                c(this).attr("id", "")
            })
        }
    }, s.prototype.interrupt = function (e) {
        e || this.autoPlay(), this.interrupted = e
    }, s.prototype.selectHandler = function (e) {
        var t = c(e.target).is(".slick-slide") ? c(e.target) : c(e.target).parents(".slick-slide"),
            n = parseInt(t.attr("data-slick-index"));
        n = n || 0, this.slideCount <= this.options.slidesToShow ? this.slideHandler(n, !1, !0) : this.slideHandler(n)
    }, s.prototype.slideHandler = function (e, t, n) {
        var i, o, r, s, a, l = null,
            c = this;
        if (t = t || !1, !(!0 === c.animating && !0 === c.options.waitForAnimate || !0 === c.options.fade && c.currentSlide === e))
            if (!1 === t && c.asNavFor(e), i = e, l = c.getLeft(i), s = c.getLeft(c.currentSlide), c.currentLeft = null === c.swipeLeft ? s : c.swipeLeft, !1 === c.options.infinite && !1 === c.options.centerMode && (e < 0 || e > c.getDotCount() * c.options.slidesToScroll)) !1 === c.options.fade && (i = c.currentSlide, !0 !== n ? c.animateSlide(s, function () {
                c.postSlide(i)
            }) : c.postSlide(i));
            else if (!1 === c.options.infinite && !0 === c.options.centerMode && (e < 0 || e > c.slideCount - c.options.slidesToScroll)) !1 === c.options.fade && (i = c.currentSlide, !0 !== n ? c.animateSlide(s, function () {
            c.postSlide(i)
        }) : c.postSlide(i));
        else {
            if (c.options.autoplay && clearInterval(c.autoPlayTimer), o = i < 0 ? c.slideCount % c.options.slidesToScroll != 0 ? c.slideCount - c.slideCount % c.options.slidesToScroll : c.slideCount + i : i >= c.slideCount ? c.slideCount % c.options.slidesToScroll != 0 ? 0 : i - c.slideCount : i, c.animating = !0, c.$slider.trigger("beforeChange", [c, c.currentSlide, o]), r = c.currentSlide, c.currentSlide = o, c.setSlideClasses(c.currentSlide), c.options.asNavFor && (a = (a = c.getNavTarget()).slick("getSlick")).slideCount <= a.options.slidesToShow && a.setSlideClasses(c.currentSlide), c.updateDots(), c.updateArrows(), !0 === c.options.fade) return !0 !== n ? (c.fadeSlideOut(r), c.fadeSlide(o, function () {
                c.postSlide(o)
            })) : c.postSlide(o), void c.animateHeight();
            !0 !== n ? c.animateSlide(l, function () {
                c.postSlide(o)
            }) : c.postSlide(o)
        }
    }, s.prototype.startLoad = function () {
        var e = this;
        !0 === e.options.arrows && e.slideCount > e.options.slidesToShow && (e.$prevArrow.hide(), e.$nextArrow.hide()), !0 === e.options.dots && e.slideCount > e.options.slidesToShow && e.$dots.hide(), e.$slider.addClass("slick-loading")
    }, s.prototype.swipeDirection = function () {
        var e, t, n, i, o = this;
        return e = o.touchObject.startX - o.touchObject.curX, t = o.touchObject.startY - o.touchObject.curY, n = Math.atan2(t, e), (i = Math.round(180 * n / Math.PI)) < 0 && (i = 360 - Math.abs(i)), i <= 45 && 0 <= i ? !1 === o.options.rtl ? "left" : "right" : i <= 360 && 315 <= i ? !1 === o.options.rtl ? "left" : "right" : 135 <= i && i <= 225 ? !1 === o.options.rtl ? "right" : "left" : !0 === o.options.verticalSwiping ? 35 <= i && i <= 135 ? "down" : "up" : "vertical"
    }, s.prototype.swipeEnd = function (e) {
        var t, n, i = this;
        if (i.dragging = !1, i.swiping = !1, i.scrolling) return i.scrolling = !1;
        if (i.interrupted = !1, i.shouldClick = !(10 < i.touchObject.swipeLength), void 0 === i.touchObject.curX) return !1;
        if (!0 === i.touchObject.edgeHit && i.$slider.trigger("edge", [i, i.swipeDirection()]), i.touchObject.swipeLength >= i.touchObject.minSwipe) {
            switch (n = i.swipeDirection()) {
                case "left":
                case "down":
                    t = i.options.swipeToSlide ? i.checkNavigable(i.currentSlide + i.getSlideCount()) : i.currentSlide + i.getSlideCount(), i.currentDirection = 0;
                    break;
                case "right":
                case "up":
                    t = i.options.swipeToSlide ? i.checkNavigable(i.currentSlide - i.getSlideCount()) : i.currentSlide - i.getSlideCount(), i.currentDirection = 1
            }
            "vertical" != n && (i.slideHandler(t), i.touchObject = {}, i.$slider.trigger("swipe", [i, n]))
        } else i.touchObject.startX !== i.touchObject.curX && (i.slideHandler(i.currentSlide), i.touchObject = {})
    }, s.prototype.swipeHandler = function (e) {
        var t = this;
        if (!(!1 === t.options.swipe || "ontouchend" in document && !1 === t.options.swipe || !1 === t.options.draggable && -1 !== e.type.indexOf("mouse"))) switch (t.touchObject.fingerCount = e.originalEvent && void 0 !== e.originalEvent.touches ? e.originalEvent.touches.length : 1, t.touchObject.minSwipe = t.listWidth / t.options.touchThreshold, !0 === t.options.verticalSwiping && (t.touchObject.minSwipe = t.listHeight / t.options.touchThreshold), e.data.action) {
            case "start":
                t.swipeStart(e);
                break;
            case "move":
                t.swipeMove(e);
                break;
            case "end":
                t.swipeEnd(e)
        }
    }, s.prototype.swipeMove = function (e) {
        var t, n, i, o, r, s, a = this;
        return r = void 0 !== e.originalEvent ? e.originalEvent.touches : null, !(!a.dragging || a.scrolling || r && 1 !== r.length) && (t = a.getLeft(a.currentSlide), a.touchObject.curX = void 0 !== r ? r[0].pageX : e.clientX, a.touchObject.curY = void 0 !== r ? r[0].pageY : e.clientY, a.touchObject.swipeLength = Math.round(Math.sqrt(Math.pow(a.touchObject.curX - a.touchObject.startX, 2))), s = Math.round(Math.sqrt(Math.pow(a.touchObject.curY - a.touchObject.startY, 2))), !a.options.verticalSwiping && !a.swiping && 4 < s ? !(a.scrolling = !0) : (!0 === a.options.verticalSwiping && (a.touchObject.swipeLength = s), n = a.swipeDirection(), void 0 !== e.originalEvent && 4 < a.touchObject.swipeLength && (a.swiping = !0, e.preventDefault()), o = (!1 === a.options.rtl ? 1 : -1) * (a.touchObject.curX > a.touchObject.startX ? 1 : -1), !0 === a.options.verticalSwiping && (o = a.touchObject.curY > a.touchObject.startY ? 1 : -1), i = a.touchObject.swipeLength, (a.touchObject.edgeHit = !1) === a.options.infinite && (0 === a.currentSlide && "right" === n || a.currentSlide >= a.getDotCount() && "left" === n) && (i = a.touchObject.swipeLength * a.options.edgeFriction, a.touchObject.edgeHit = !0), !1 === a.options.vertical ? a.swipeLeft = t + i * o : a.swipeLeft = t + i * (a.$list.height() / a.listWidth) * o, !0 === a.options.verticalSwiping && (a.swipeLeft = t + i * o), !0 !== a.options.fade && !1 !== a.options.touchMove && (!0 === a.animating ? (a.swipeLeft = null, !1) : void a.setCSS(a.swipeLeft))))
    }, s.prototype.swipeStart = function (e) {
        var t, n = this;
        if (n.interrupted = !0, 1 !== n.touchObject.fingerCount || n.slideCount <= n.options.slidesToShow) return !(n.touchObject = {});
        void 0 !== e.originalEvent && void 0 !== e.originalEvent.touches && (t = e.originalEvent.touches[0]), n.touchObject.startX = n.touchObject.curX = void 0 !== t ? t.pageX : e.clientX, n.touchObject.startY = n.touchObject.curY = void 0 !== t ? t.pageY : e.clientY, n.dragging = !0
    }, s.prototype.unfilterSlides = s.prototype.slickUnfilter = function () {
        var e = this;
        null !== e.$slidesCache && (e.unload(), e.$slideTrack.children(this.options.slide).detach(), e.$slidesCache.appendTo(e.$slideTrack), e.reinit())
    }, s.prototype.unload = function () {
        var e = this;
        c(".slick-cloned", e.$slider).remove(), e.$dots && e.$dots.remove(), e.$prevArrow && e.htmlExpr.test(e.options.prevArrow) && e.$prevArrow.remove(), e.$nextArrow && e.htmlExpr.test(e.options.nextArrow) && e.$nextArrow.remove(), e.$slides.removeClass("slick-slide slick-active slick-visible slick-current").attr("aria-hidden", "true").css("width", "")
    }, s.prototype.unslick = function (e) {
        this.$slider.trigger("unslick", [this, e]), this.destroy()
    }, s.prototype.updateArrows = function () {
        var e = this;
        Math.floor(e.options.slidesToShow / 2), !0 === e.options.arrows && e.slideCount > e.options.slidesToShow && !e.options.infinite && (e.$prevArrow.removeClass("slick-disabled").attr("aria-disabled", "false"), e.$nextArrow.removeClass("slick-disabled").attr("aria-disabled", "false"), 0 === e.currentSlide ? (e.$prevArrow.addClass("slick-disabled").attr("aria-disabled", "true"), e.$nextArrow.removeClass("slick-disabled").attr("aria-disabled", "false")) : e.currentSlide >= e.slideCount - e.options.slidesToShow && !1 === e.options.centerMode ? (e.$nextArrow.addClass("slick-disabled").attr("aria-disabled", "true"), e.$prevArrow.removeClass("slick-disabled").attr("aria-disabled", "false")) : e.currentSlide >= e.slideCount - 1 && !0 === e.options.centerMode && (e.$nextArrow.addClass("slick-disabled").attr("aria-disabled", "true"), e.$prevArrow.removeClass("slick-disabled").attr("aria-disabled", "false")))
    }, s.prototype.updateDots = function () {
        var e = this;
        null !== e.$dots && (e.$dots.find("li").removeClass("slick-active").end(), e.$dots.find("li").eq(Math.floor(e.currentSlide / e.options.slidesToScroll)).addClass("slick-active"))
    }, s.prototype.visibility = function () {
        this.options.autoplay && (document[this.hidden] ? this.interrupted = !0 : this.interrupted = !1)
    }, c.fn.slick = function () {
        var e, t, n = this,
            i = arguments[0],
            o = Array.prototype.slice.call(arguments, 1),
            r = n.length;
        for (e = 0; e < r; e++)
            if ("object" == typeof i || void 0 === i ? n[e].slick = new s(n[e], i) : t = n[e].slick[i].apply(n[e].slick, o), void 0 !== t) return t;
        return n
    }
});
var winW, headerH, objectFitImages = function () {
        "use strict";

        function i(e, t, n) {
            var i = function (e, t) {
                return "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='" + e + "' height='" + t + "'%3E%3C/svg%3E"
            }(t || 1, n || 0);
            f.call(e, "src") !== i && h.call(e, "src", i)
        }

        function o(e, t) {
            e.naturalWidth ? t(e) : setTimeout(o, 100, e, t)
        }

        function r(t) {
            var e = function (e) {
                    for (var t, n = getComputedStyle(e).fontFamily, i = {}; null !== (t = l.exec(n));) i[t[1]] = t[2];
                    return i
                }(t),
                n = t[a];
            if (e["object-fit"] = e["object-fit"] || "fill", !n.img) {
                if ("fill" === e["object-fit"]) return;
                if (!n.skipTest && c && !e["object-position"]) return
            }
            if (!n.img) {
                n.img = new Image(t.width, t.height), n.img.srcset = f.call(t, "data-ofi-srcset") || t.srcset, n.img.src = f.call(t, "data-ofi-src") || t.src, h.call(t, "data-ofi-src", t.src), t.srcset && h.call(t, "data-ofi-srcset", t.srcset), i(t, t.naturalWidth || t.width, t.naturalHeight || t.height), t.srcset && (t.srcset = "");
                try {
                    ! function (n) {
                        var t = {
                            get: function (e) {
                                return n[a].img[e || "src"]
                            },
                            set: function (e, t) {
                                return n[a].img[t || "src"] = e, h.call(n, "data-ofi-" + t, e), r(n), e
                            }
                        };
                        Object.defineProperty(n, "src", t), Object.defineProperty(n, "currentSrc", {
                            get: function () {
                                return t.get("currentSrc")
                            }
                        }), Object.defineProperty(n, "srcset", {
                            get: function () {
                                return t.get("srcset")
                            },
                            set: function (e) {
                                return t.set(e, "srcset")
                            }
                        })
                    }(t)
                } catch (t) {
                    window.console && console.warn("https://bit.ly/ofi-old-browser")
                }
            }(function (e) {
                if (e.srcset && !p && window.picturefill) {
                    var t = window.picturefill._;
                    e[t.ns] && e[t.ns].evaled || t.fillImg(e, {
                        reselect: !0
                    }), e[t.ns].curSrc || (e[t.ns].supported = !1, t.fillImg(e, {
                        reselect: !0
                    })), e.currentSrc = e[t.ns].curSrc || e.src
                }
            })(n.img), t.style.backgroundImage = 'url("' + (n.img.currentSrc || n.img.src).replace(/"/g, '\\"') + '")', t.style.backgroundPosition = e["object-position"] || "center", t.style.backgroundRepeat = "no-repeat", t.style.backgroundOrigin = "content-box", /scale-down/.test(e["object-fit"]) ? o(n.img, function () {
                n.img.naturalWidth > t.width || n.img.naturalHeight > t.height ? t.style.backgroundSize = "contain" : t.style.backgroundSize = "auto"
            }) : t.style.backgroundSize = e["object-fit"].replace("none", "auto").replace("fill", "100% 100%"), o(n.img, function (e) {
                i(t, e.naturalWidth, e.naturalHeight)
            })
        }

        function s(e, t) {
            var n = !v && !e;
            if (t = t || {}, e = e || "img", d && !t.skipTest || !u) return !1;
            "img" === e ? e = document.getElementsByTagName("img") : "string" == typeof e ? e = document.querySelectorAll(e) : "length" in e || (e = [e]);
            for (var i = 0; i < e.length; i++) e[i][a] = e[i][a] || {
                skipTest: t.skipTest
            }, r(e[i]);
            n && (document.body.addEventListener("load", function (e) {
                "IMG" === e.target.tagName && s(e.target, {
                    skipTest: t.skipTest
                })
            }, !0), v = !0, e = "img"), t.watchMQ && window.addEventListener("resize", s.bind(null, e, {
                skipTest: t.skipTest
            }))
        }
        var a = "fregante:object-fit-images",
            l = /(object-fit|object-position)\s*:\s*([-.\w\s%]+)/g,
            e = "undefined" == typeof Image ? {
                style: {
                    "object-position": 1
                }
            } : new Image,
            c = "object-fit" in e.style,
            d = "object-position" in e.style,
            u = "background-size" in e.style,
            p = "string" == typeof e.currentSrc,
            f = e.getAttribute,
            h = e.setAttribute,
            v = !1;
        return s.supportsObjectFit = c, (s.supportsObjectPosition = d) || (HTMLImageElement.prototype.getAttribute = function (e) {
            return f.call(n(this, e), e)
        }, HTMLImageElement.prototype.setAttribute = function (e, t) {
            return h.call(n(this, e), e, String(t))
        }), s;

        function n(e, t) {
            return e[a] && e[a].img && ("src" === t || "srcset" === t) ? e[a].img : e
        }
    }(),
    ENGINE = ENGINE || {},
    adjustH = 40;

function slideSet() {
    $(".js-topSlide").not(".slick-initialized").slick({
        arrows: !1,
        autoplay: !0,
        infinite: !0,
        touchThreshold: 15,
        slidesToShow: 1,
        centerMode: !0,
        autoplaySpeed: 6e3,
        speed: 800,
        dots: !0,
        pauseOnFocus: !1,
        pauseOnHover: !1,
        pauseOnDotsHover: !1,
        centerPadding: "0",
        fade: !0,
        customPaging: function (e, t) {
            e.$slides.eq(t).html();
            return '<div class="slick-dots__dot"></div>'
        }
    }).on({
        beforeChange: function (e, t, n, i) {
            $(".slick-slide", this).eq(n).addClass("preve-slide"), $(".slick-slide", this).eq(i).addClass("slide-animation")
        },
        afterChange: function () {
            $(".preve-slide", this).removeClass("preve-slide slide-animation")
        },
        touchmove: function (e, t, n, i) {
            $(".js-topSlide").slick("slickPlay")
        }
    }), $(".js-topSlide").find(".slick-current").eq(0).addClass("slide-animation")
}

function singleSlideSet() {
    if($('.js-slide .uq_tvantelin-lineup__slide--item').length > 1) {
		$(".js-slide").not(".slick-initialized").slick({
            arrows: !0,
            autoplay: !0,
            infinite: !0,
            touchThreshold: 15,
            slidesToShow: 1,
            centerMode: !0,
            autoplaySpeed: 2500,
            pauseOnFocus: !1,
            pauseOnHover: !1,
            centerPadding: "0",
            speed: 800,
            dots: !0,
            prevArrow: '<button class="m_slick__arrow m_slick__arrow_prev"></button>',
            nextArrow: '<button class="m_slick__arrow m_slick__arrow_next"></button>',
            responsive: [{
                breakpoint: 750,
                settings: {
                    slidesToShow: 1,
                    centerPadding: "0"
                }
            }],
            customPaging: function (e, t) {
                e.$slides.eq(t).html();
                return '<div class="slick-dots__dot m_slick__dot"></div>'
            }
        })
	}
    
}
$(function () {}), ENGINE.common = {
    init: function () {},
    __vh: function () {
        var t = .01 * window.innerHeight;
        document.documentElement.style.setProperty("--vh", t + "px");
        var n = $(window).width();
        $(window).on("resize", function () {
            var e = $(window).width();
            n !== e && (t = .01 * window.innerHeight, document.documentElement.style.setProperty("--vh", t + "px"))
        })
    },
    _lord_resize: function () {
        $(window).on("load resize", function () {
            winW = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth, headerH = $(".l_header").outerHeight()
        })
    }
}, ENGINE.scroll = {
    init: function () {
        var t = location.hash;
        t && ($("body, html").stop().scrollTop(0), setTimeout(function () {
            var e = $(t).offset().top - adjustH;
            $("body,html").stop().animate({
                scrollTop: e
            }, 500, "swing")
        }, 200)), $('.js-scroll a[href^="#"], a.js-scroll[href^="#"]').on("click", function () {
            void 0 !== $(".js-accoBtnHeader") && $(".js-accoBtnHeader").each(function () {
                $(this).hasClass("is-accoOpen") && $(this).next().slideToggle(300), $(this).removeClass("is-accoOpen")
            });
            return winW < 750 && $(".l_header").hasClass("is-navOpen") && (ENGINE.header._spNav(), $(".l_header-nav-wrap").fadeToggle(200)), void 0 !== $($(this).attr("href")).offset().top && $("html, body").animate({
                scrollTop: $($(this).attr("href")).offset().top - adjustH
            }, 500, "swing"), !1
        })
    }
}, ENGINE.header = {
    _spNav: function () {
        if ($(".l_header").hasClass("is-navOpen")) $(".l_header").removeClass("is-navOpen"), $(".l_main").removeClass("is-navOpen"), $("body").removeClass("is-fixed"), $(".m_pagetop").slideToggle(0), $(".js-headerIn").removeClass("is-show"), $(".js-headerIn li").removeClass("is-show");
        else {
            $(".l_header").addClass("is-navOpen"), $(".l_main").addClass("is-navOpen"), $("body").addClass("is-fixed"), $(".m_pagetop").slideToggle(0);
            var e = $(".js-headerIn");
            e.delay(100).queue(function () {
                e.addClass("is-show").dequeue(), e.find(".js-headerIn__item").each(function (e) {
                    var t = $(this);
                    t.delay(200 * (e + 1)).queue(function () {
                        t.addClass("is-show").dequeue()
                    })
                })
            })
        }
    },
    _spSubNav: function () {
        $(".m_datail_nav").hasClass("is-navOpen") ? $(".m_datail_nav").removeClass("is-navOpen") : $(".m_datail_nav").addClass("is-navOpen"), $(".m_pagetop").slideToggle(0)
    },
    _blackOnclick: function () {
        var t = $(".l_header-nav-wrap");
        $(document).on("click touchstart", function (e) {
            $(e.target).closest(".l_header").length || $(".l_header").hasClass("is-navOpen") && (ENGINE.header._spNav(), t.fadeToggle(200))
        })
    }
}, ENGINE.NaviBtn = {
    init: function () {
        var e = $(".js-spNavBtn"),
            t = $(".l_header-nav-wrap");
        e.on("click", function () {
            ENGINE.header._spNav(), t.fadeToggle(200)
        })
    },
    subNav: function () {
        var e = $(".js-SubNavBtn"),
            t = $(".m_datail_nav__list"),
            n = $("#datail_nav");
        e.on("click", function () {
            ENGINE.header._spSubNav(), t.fadeToggle(200), n.hasClass("is-navOpen") && n.hasClass("is_fixed") ? $("body").addClass("is-fixed") : $("body").removeClass("is-fixed")
        })
    }
}, ENGINE.NaviGlobalHover = {
    init: function () {
        $(".js_global").hover(function () {
            750 < winW && ($(".nav__item").hasClass("is-haslongChild") || ($(".l_header-nav_global-wrap").stop(!0).addClass("is-navOpen"), $(".l_main").stop(!0).addClass("is-navOpen")))
        }, function () {
            750 < winW && ($(".nav__item").hasClass("is-haslongChild") || ($(".l_header-nav_global-wrap").removeClass("is-navOpen"), $(".l_main").removeClass("is-navOpen")))
        })
    }
}, ENGINE.DatailNav = {
    init: function () {
        var t;
        t = $(".m_datail_nav__list");
        var n, i, o = $(".l_header"),
            r = $("#datail_nav"),
            s = $(window).width();
        $(window).on("load", function () {
            n = !!r.get(0) && r.offset().top, i = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth
        }), $(window).on("resize", function () {
            n = !!r.get(0) && r.parent().offset().top, i = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
            var e = $(window).width();
            s !== e && (750 < i ? r.hasClass("is-navOpen") || t.css("display", "block") : t.css("display", "none"))
        }), $(window).on("scroll", function () {
            var e = $(window).scrollTop();
            n && (n < e ? (r.addClass("is_fixed"), o.addClass("is-none"), r.hasClass("is-navOpen") ? $("body").addClass("is-fixed") : $("body").removeClass("is-fixed")) : (r.removeClass("is_fixed"), o.removeClass("is-none"), $("body").removeClass("is-fixed"))), e < 60 ? o.addClass("is-break") : o.removeClass("is-break")
        }), $(".m_datail_nav__list-area--item.is-hasChild").hover(function () {
            750 < i && ($(".nav__item").hasClass("is-haslongChild") || $(this).stop(!0).addClass("is-navSubOpen"))
        }, function () {
            750 < i && ($(".nav__item").hasClass("is-haslongChild") || $(this).removeClass("is-navSubOpen"))
        }), $(".js-accoBtnHeader").on("click", function () {
            i <= 750 && ENGINE.accoSP.init($(this))
        })
    }
}, ENGINE.accoBtnSpCloce = {
    init: function () {
        var t, n = $(window).width(),
            i = $(".m_linup--accodion.is-accoBtn");
        $(window).on("load", function () {
            (t = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth) <= 750 && i.find(".js-accoBtn").each(function () {
                $(this).removeClass("is-accoOpen")
            })
        }), $(window).on("resize", function () {
            var e = $(window).width();
            t = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth, n !== e && t <= 750 && i.find(".js-accoBtn").each(function () {
                $(this).removeClass("is-accoOpen")
            })
        })
    }
}, ENGINE.accoBtn = {
    init: function () {
        $(".js-accoBtn").on("click", function () {
            ENGINE.accoSP.init($(this))
        })
    }
}, ENGINE.accoBtnSp = {
    init: function () {
        $(".js-accoBtnSp").on("click", function () {
            winW < 750 && ENGINE.accoSP.init($(this))
        })
    }
}, ENGINE.accoSP = {
    init: function (e) {
        void 0 !== e && (e.hasClass("is-accoOpen") ? e.removeClass("is-accoOpen") : e.addClass("is-accoOpen"), e.next().slideToggle(300))
    }
}, ENGINE.readmore = {
    init: function () {
        var e, t = "Read More",
            n = "Read More";
        $("#read_more").on("click", function () {
            void 0 !== (e = $(this)) && (e.hasClass("is-accoOpen") ? (e.removeClass("is-accoOpen"), t = e.data("open"), e.find("span").text(t)) : (e.addClass("is-accoOpen"), n = e.data("close"), e.find("span").text(n)), e.prev().slideToggle(300))
        })
    }
}, ENGINE.DatailNavOnClick = {
    init: function () {
        var e = $(".m_datail_nav__list");
        $(".m_datail_nav__list-area--item a").on("click", function () {
            winW < 750 && (ENGINE.header._spSubNav(), e.fadeToggle(200)), $(".m_datail_nav__list-area--item").removeClass("is-navSubOpen")
        })
    }
}, ENGINE.ContactonClick = {
    init: function () {
        $(".js_contact").on("copy contextmenu", function () {
            return !1
        })
    }
}, ENGINE.slide = {
    init: function () {
        $(".js-slideIn").on("inview", function () {
            var e = $(this);
            e.delay(230).queue(function () {
                e.addClass("is-show"), e.find(".js-slideIn__item").each(function (e) {
                    var t = $(this);
                    t.delay(230 * (e + 1)).queue(function () {
                        t.addClass("is-show")
                    })
                })
            })
        }), $(".js-orderIn").on("inview", function () {
            var e = $(this).children();
            e.each(function (e) {
                var t = $(this);
                t.delay(600 * e).queue(function (e) {
                    t.addClass("is-show"), e()
                })
            })
        }), $(".is-anime").on("inview", function () {
            var e = $(this);
            e.delay(230).queue(function () {
                e.addClass("is-show"), e.find(".is-anime__Rightin").each(function (e) {
                    var t = $(this);
                    t.delay(230 * (e + 1)).queue(function () {
                        t.addClass("is-show")
                    })
                }), e.find(".is-anime__Upin").each(function (e) {
                    var t = $(this);
                    t.delay(230 * (e + 1)).queue(function () {
                        t.addClass("is-show")
                    })
                }), e.find(".is-anime__Leftin").each(function (e) {
                    var t = $(this);
                    t.delay(230 * (e + 1)).queue(function () {
                        t.addClass("is-show")
                    })
                }), e.find(".is-anime__Downin").each(function (e) {
                    var t = $(this);
                    t.delay(230 * (e + 1)).queue(function () {
                        t.addClass("is-show")
                    })
                }), e.find(".is-anime__item").each(function (e) {
                    var t = $(this);
                    t.delay(230 * (e + 1)).queue(function () {
                        t.addClass("is-show")
                    })
                })
            })
        })
    }
}, ENGINE.mask = {
    init: function () {
        speed = 1e3, easing = "easeInOutCubic", 
        $mask = $(".m_mask"), 
        $mask.wrap("<div class='m_mask-wrap'>"), 
        $mask.after("<div class='m_mask-bg'></div>"), 
        $(".is-mask").on("inview", function () {
            $(this).find(".m_mask-wrap").each(function (e) {
                var t = $(this);
                t.delay(230 * (e + 1)).queue(function () {
                    t.find(".m_mask").addClass("is-show"), t.find(".m_mask-bg").addClass("is-show")
                })
            })
        })
    }
}, ENGINE.pageTop = {
    init: function () {
        var n = $("#footer_pagetop");
        $(window).on("scroll", function () {
            var e = $(this).scrollTop(),
                t = $("#mv").innerHeight();
            $("#footer").innerHeight();
            void 0 !== t ? t / 3 < e ? n.addClass("is-fadeIn") : n.removeClass("is-fadeIn") : n.addClass("is-fadeIn")
        })
    }
}, window.addEventListener("DOMContentLoaded", function () {
    ENGINE.common._lord_resize(), ENGINE.common.__vh(), ENGINE.pageTop.init(), ENGINE.scroll.init(), ENGINE.header._blackOnclick(), ENGINE.DatailNav.init(), ENGINE.NaviGlobalHover.init(), ENGINE.DatailNavOnClick.init(), ENGINE.NaviBtn.init(), ENGINE.NaviBtn.subNav(), ENGINE.accoBtn.init(), ENGINE.accoBtnSp.init(), ENGINE.accoSP.init(), ENGINE.readmore.init(), ENGINE.accoBtnSpCloce.init(), ENGINE.slide.init(), ENGINE.mask.init(), ENGINE.ContactonClick.init()
}), $(window).on("load resize orientationchange", function () {
    slideSet(), singleSlideSet()
});


//slider
$(window).on('load resize', function(){
    windowWidth = window.screen.width;
    if(windowWidth <= 750){
        $('.js-slider--onsp').slick({
            arrows :true,
            dots : false,
            centerMode :true,
            variableWidth: true,
            slidesToShow: 1,
          });
    } else {
        $('.js-slider--onsp').slick('unslick');
    }
  });

//modal popup
{
var modalNum;
$(document).ready(function(){
  // close
  $(".js-modalCloseBtn").on('click',function () {
    $(".js-modal--wrapper,.js-modal,.js-modal img:not('.modal__close-btn__image')").fadeOut();
  });

  $(".js-modal--wrapper").on('click',function (e) {
    if ( !$.contains($('.js-modal--wrapper')[0], e.target) ) {
      $(".js-modal--wrapper,.js-modal,.js-modal img:not('.modal__close-btn__image')").fadeOut();
    }
  });
  
  //open
  $('.js-modalOpenBtn').on('click',function(){
    modalNum = $(this).data('num');
    $('.js-modal--wrapper').fadeIn('fast').css('display','flex');
    $('.js-modal,.js-modalCloseBtn,.js-modal img[data-num=' + modalNum + ']').fadeIn('slow');
  });
});
}

