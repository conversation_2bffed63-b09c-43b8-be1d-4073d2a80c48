var Mm = Mm || {};

(function($) {

	var Obj = {};

	/* 設定用変数
	 ------------------------------------------------------------------------------------------- */
	// チェック開始のイベント
	Obj.VALIDATE_EVENT = 'change';
	// Obj.VALIDATE_EVENT = 'keyup';


	/* 各種変数
	 ------------------------------------------------------------------------------------------- */
	// チェック対象のformオブジェクト（jQueryオブジェクトではないので注意）
	Obj.form  = false;

	// チェックルールの定義を格納する変数
	// ※ php側（Entry.php）で設定されているチェックルールを使用する
	Obj.validates = false;
	Obj.hasError  = false;

	Obj.isValidOnce = false;

	// フォームからの離脱チェックをするかどうか
	Obj.isUnloadCheck = true;

	// 各フォームフィールドを格納する変数
	// ※ 入力フィールドのname属性に関して
	//    input[type=radio]以外、同名のものはセットされていないことを想定
	Obj.submits     = []; // 指定されたform内にあるsubmitボタンを格納する配列
	Obj.fieldGroups = {}; // 指定されたform内にあるフィールドをグループ化したものを格納する連想配列（radioボタン対応）

	Obj.options = {
		submitDisable      : true,  // submitボタンのdisabledの制御をするか
		errorMove          : true,  // submit時にエラーがある最初の項目位置へ遷移するか
		errorMoveFunc      : null,  // submit時にエラーがある最初の項目位置へ移動するための関数
		errorShowAnimation : false, // エラー表示時にアニメーション表示するか
		hintShowAnimation  : false, // ヒント表示時にアニメーション表示するか
		cls: {
			error: '_error',        // チェックError時に付与するclass名
			success: '_success'     // チェックSuccess時に付与するclass名
		},
		isUnloadCheck : false,      // 離脱時にメッセージを表示するか
		unloadMessage : ''          // 離脱時のメッセージ
	};

	/**
	 * 初期設定
	 *
	 */
	Obj.init = function(formName, params) {

		var $form = $(formName);

		Obj.form  = $form.get(0);

		if (params) {
			Obj.options = $.extend(Obj.options, params);
		}

		// 設定のチェック - START
		// formオブジェクトが取得できない場合
		if ($form.length !== 1) {
			window.alert('exception occurred. \n設定されたフォームが存在しません。');
			return false;
		}

		// validationのルールが取得できない場合
		if (!Obj.validates) {
			window.alert('exception occurred. \n入力チェック設定が取得できませんでした。');
			return false;
		}
		// 設定のチェック - END

		// ページを離れる前に確認メッセージを表示
		Obj.isUnloadCheck = Obj.options.isUnloadCheck;
		$(window).on('beforeunload', function() {
			// フラグが立っている場合、エラーメッセージを表示
			if (Obj.isUnloadCheck) {
				return Obj.options.unloadMessage;
			}
		});

		// formオブジェクトの初期化
		Obj.setup(Obj.form);

		// 初期チェックの実行
		Obj.validate(false);

	};

	/**
	 * 各フォームフィールドの分類とイベント設定
	 *
	 */
	Obj.setup = function(form) {

		var elemType  = '';
		var elemName  = '';
		var fieldType = '';

		// フォーム内の入力フィールドの分類を行う
		for(var i=0; i<form.elements.length; i++) {

			elemType  = (form.elements[i].nodeName + '').toLowerCase();
			elemName  = form.elements[i].name;
			fieldType = '';

			// 取得したフォーム内の入力フィールドの種類による分岐
			switch(elemType) {
				// <input>タグの場合
				case 'input':
					fieldType = (form.elements[i].type + '').toLowerCase();
					switch(fieldType) {
						case 'submit':
						case 'image':
							// submit or imageの場合、両方ともsubmitボタン扱い
							fieldType = 'submit';
							break;

						case 'radio':
						case 'checkbox':
						case 'hidden':
							break;

						// type=text、以外に指定なし、email、phoneなどもtextと同じ扱いとする
						default:
							fieldType = 'text';
							break;

					}
					break;

				// <textarea>タグの場合
				case 'textarea':
					fieldType = elemType;
					break;

				// <select>タグの場合
				case 'select':
					fieldType = elemType;
					break;

				// <button>タグの場合（type="submit"なら、input:submit / input:imageと同じ扱い）
				case 'button':
					fieldType = (form.elements[i].type + '').toLowerCase();

					switch(fieldType) {
						case 'reset':
						case 'button':
							// reset or button の場合は処理なし
							break;

						default:
							// submit or default(指定なし) の場合、両方ともsubmitボタン扱い
							fieldType = 'submit';

							break;
					}

					break;
				default:

					break;
			}

			// fieldTypeに値がセットされている場合
			if (fieldType) {
				// checkboxの場合
				if (fieldType === 'checkbox') {
					// elemNameに"[]"がある場合（複数選択）
					if (elemName.indexOf('[]') >= 0) {
						fieldType = 'checkbox-multi';
					}
				}


				// submitボタンの場合
				if (fieldType === 'submit') {
					Obj.initSubmit(form.elements[i]);

				// hiddenフィールドの場合
				// } else if (fieldType !== 'hidden') {
				} else {
					Obj.initElem(form.elements[i], fieldType);
				}
			}
		}
	};


	/**
	 * 入力フィールドの初期化
	 *
	 */
	Obj.initElem = function(elem, type) {
		var $elem    = $(elem);
		var elemName = $elem.attr('name');

		var targetID = $elem.data('target-id');
		var errorID  = 'data-error-' + targetID;
		var hintID   = 'data-hint-' + targetID;
		var resultID = 'data-result-' + targetID;

		// フィールドグループをセット
		// name属性が同じものは１つのフィールドタイプになっていることが前提
		if (!Obj.fieldGroups[elemName]) {
			Obj.fieldGroups[elemName] = {
				'type' : type,
				'errorID' : errorID,
				'resultID' : resultID,
				'hasError' : false
			};
		}

		// active時のhint表示制御
		var $hintBox = false;
		if (hintID) {
			$hintBox = $('#' + hintID);
		}

		// focus（active）時のイベント
		$elem.on('focus', function() {
			// ヒントの表示
			_showHint();
		});

		// click（active）時のイベント
		$elem.on('click', function() {
			// ヒントの表示
			_showHint();
		});

		// blur（非active）時のイベント
		$elem.on('blur', function() {
			// ヒントの非表示
			_hideHint();
		});

		// inputのテキスト入力フィールド（type=text,email,phoneなど）やradio、checkboxの場合、
		// enterキーでフォームが送信されるのを抑制する
		if ((type == 'text') || (type == 'radio') || (type == 'checkbox') || (type == 'select')) {
			$elem.on('keypress', function(e) {
				if ((e.which && e.which === 13) || (e.keyCode && e.keyCode === 13)) {
					return false;
				} else {
					return true;
				}
			});
		}

		// change 時のイベント
		$elem.on(Obj.VALIDATE_EVENT, function() {

			// 拡張機能（引数optionで渡された項目ごとのvalidate実行前関数の実行）
			if (Obj.options['func'] && Obj.options['func'][elemName] && Obj.options['func'][elemName]['before']) {
				if (typeof(Obj.options['func'][elemName]['before']) === 'function') {
					Obj.options['func'][elemName]['before']();
				}
			}

			// validateの実行
			Obj.validate(true, elemName);

			// 拡張機能（引数optionで渡された項目ごとのvalidate実行後関数の実行）
			if (Obj.options['func'] && Obj.options['func'][elemName] && Obj.options['func'][elemName]['after']) {
				if (typeof(Obj.options['func'][elemName]['after']) === 'function') {
					Obj.options['func'][elemName]['after']();
				}
			}
		});

		/**
		 * ヒントの表示
		 *
		 */
		function _showHint() {
			if ($hintBox && ($hintBox.length > 0) && !$hintBox.hasClass('open')) {
				if (Obj.options.hintShowAnimation) {
					$hintBox.slideDown(50).addClass('open');
				} else {
					$hintBox.show().addClass('open');
				}
			}
		}

		/**
		 * ヒントの非表示
		 *
		 */
		function _hideHint() {
			if ($hintBox && ($hintBox.length > 0)) {
				$hintBox.hide().removeClass('open');
			}
		}

	};


	/**
	 * submitボタンの初期化
	 *
	 */
	Obj.initSubmit = function(elem) {
		var $elem = $(elem);
		var elemName = $elem.attr('name');

		Obj.submits.push({
			'name': elemName,
			'elem': $elem
		});

		Obj.form.onsubmit = function() {
			Obj.validate(true);

			// Errorがある場合の自動スクロール
			if (Obj.hasError && Obj.options.errorMove) {

				for(var fieldName in Obj.fieldGroups) {
					if (Obj.fieldGroups[fieldName].hasError) {

						if (Obj.options.errorMoveFunc && (typeof(Obj.options.errorMoveFunc) == 'function')) {
							Obj.options.errorMoveFunc(fieldName, Obj.fieldGroups[fieldName]);
						} else {
							var $target = $('#' + Obj.fieldGroups[fieldName].errorID);
							if ($target.length) {
								$('html,body').animate({scrollTop: ($target.parent().offset().top - 20)}, 200);
							}
						}

						break;
					}
				}
			}

console.log('-- debug --');
console.log(Obj.fieldGroups);

			// エラーがない場合、離脱チェックフラグをOFFにする
			if (!Obj.hasError) {
				Obj.isUnloadCheck = false;
			}

			return Obj.hasError?false:true;
		};

		if (Obj.options.submitDisable) {
			$elem.attr('disabled', 'disabled');
		}
	};


	/**
	 * 入力チェックの実行
	 * ※全項目対象
	 * ※エラーの表示／非表示制御
	 * ※チェック結果の表示／非表示制御
	 */
	Obj.validate = function(showError, elemName) {

		Obj.hasError  = false;

		// field loop
		for(var fieldName in Obj.fieldGroups) {

			// 入力チェックの実行
			Obj.validateAt(showError, fieldName);

			// activeになっている項目より後の項目は、現在のエラー状態から変更しないため、フラグを下ろす
			// 拡張機能（一度、チェックOKになった場合は、全チェックを行う）
			// if (fieldName === elemName) {
			if (!Obj.isValidOnce && (fieldName === elemName)) {
				showError = false;
			}
		}

		// submitボタンの表示制御（エラーありの場合disabledを設定する）
		Obj.judgeSubmit();

		// 拡張機能（一度、チェックOKになった場合、フラグを立てる）
		// ※ フォーム内の選択肢により、チェックが切り替わる場合、このフラグをみて再チェックする必要あり
		//    （必須→任意のような切り替えがあった場合、エラーメッセージ・フラグ解除がされないため）
		if (!Obj.hasError) {
			Obj.isValidOnce = true;
		}

	};

	/**
	 * submitボタンの有効／無効切り替え
	 *
	 */
	Obj.judgeSubmit = function() {


		if (Obj.options.submitDisable) {
			// submitボタンの表示制御（エラーありの場合disabledを設定する）
			var disabled = Obj.hasError?'disabled':null;
			for(var i=0, imax=Obj.submits.length; i<imax; i++) {
				Obj.submits[i].elem.attr('disabled', disabled);
			}
		}

	};

	/**
	 * １項目の入力チェック
	 *
	 */
	Obj.validateAt = function(showError, fieldName) {

		var hasError = true;

		// checkbox（multiの場合の処理）
		var checkFieldName = fieldName;
		if (Obj.fieldGroups[checkFieldName].type === 'checkbox-multi') {
			checkFieldName = checkFieldName.replace(/\[|\]/g, "");
		}

		// ルールが存在している場合
		if (Obj.validates[checkFieldName]) {
			var field = Obj.form[fieldName];
			var rules = Obj.validates[checkFieldName];


			// 各フィールドの入力値・選択値の取得
			var fieldValue = Obj.getValue(Obj.fieldGroups[fieldName].type, field);

			// ルールの数分LOOP
			var errors = [];
			var isInvalid = false;
			var hasError = false;
			for(var i=0,imax=rules.length; i<imax; i++) {
				// チェックNGの場合、trueがかえってくる
				isInvalid = Mm.Validation.check(rules[i].rule, fieldValue, fieldName, rules[i].option, Obj.form);

				// チェックエラーの場合
				if (isInvalid) {
					// エラーメッセージ格納用配列にメッセージをセット
					errors.push(rules[i].message);
					// フラグを立てる
					hasError = true;
					// グローバル変数のフラグを立てる
					Obj.hasError = true;
				}
			}

			// チェック結果による制御
			// errorの表示用オブジェクトの取得
			// ※error表示用のboxがある場合、かつエラー表示対象の場合
			// ※エラー表示対象かどうかは、activeになっている項目以前の項目かどうか
			var errorID = Obj.fieldGroups[fieldName].errorID;
			var $error  = $('#' + errorID);

			// チェック結果表示用オブジェクトの取得
			var resultID = Obj.fieldGroups[fieldName].resultID;
			var $result = $('#' + resultID);

			// エラーありの場合
			if (hasError) {
				Obj.fieldGroups[fieldName].hasError = true;

				// エラー表示
				if (($error.length > 0) && showError) {
					$error.html(errors.join('<br>'));

					if (Obj.options.errorShowAnimation) {
						$error.slideDown(50);
					} else {
						$error.show();
					}
				}
				// チェック結果をNGに変更
				if (($result.length > 0) && showError) {
					$result.addClass(Obj.options.cls.error).removeClass(Obj.options.cls.success).show();
				}

			// エラーなしの場合
			} else {
				Obj.fieldGroups[fieldName].hasError = false;

				// エラー非表示
				if (($error.length > 0) && showError) {
					$error.hide();
				}
				// チェック結果をOKに変更
				if (($result.length > 0) && showError) {
					$result.addClass(Obj.options.cls.success).removeClass(Obj.options.cls.error).show();
				}
			}
		}

		return hasError;
	};

	Obj.getValue = function(type, field) {

		var result = null;

		// selectbox
		if (type == 'select') {
			var options = field.options;
			if(options.length){
				for(var j=0, jmax=options.length; j<jmax; j++){
					if(options[j].selected) {
						result = options[j].value;
						break;
					}
				}
			}

		// checkbox or radio
		} else if ((type == 'checkbox') || (type == 'radio')) {
			if (field.length) {
				for(var j=0, jmax=field.length; j<jmax; j++){
					if(field[j].checked) {
						result = field[j].value;
						break;
					};
				}
			} else {
				if (field.checked) {
					result = field.value;
				}
			}

		// checkbox(multi)
		} else if ((type == 'checkbox-multi')) {
			if (field.length) {
				result = [];
				for(var j=0, jmax=field.length; j<jmax; j++){
					if(field[j].checked) {
						result.push(field[j].value);
					};
				}
			}

		} else {
			result = field.value;
		}

		return result;
	}

	Mm.MailForm = Obj;

})(jQuery);


//# sourceMappingURL=mm.controller.min.js.map
