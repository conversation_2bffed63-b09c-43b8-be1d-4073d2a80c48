{"version": 3, "names": [], "mappings": "", "sources": ["contact/mm.util.js"], "sourcesContent": ["\n/* Common */\n/**\n * Debug\n *\n */\nfunction log() {\n\tif(typeof console == \"undefined\") return;\n\tvar args = jQuery.makeArray(arguments);\n\tconsole.log.apply(console, args);\n}\n\n/**\n * ひらがな・カタカナ変換\n *\n */\n(function ($) {\n\t$.fn.autoKana = function (element1, element2, passedOptions) {\n\n\t\tvar options = $.extend(\n\t\t\t{\n\t\t\t\t'katakana': false\n\t\t\t}, passedOptions);\n\n\t\tvar kana_extraction_pattern = new RegExp('[^ 　ぁあ-んー]', 'g');\n\t\tvar kana_compacting_pattern = new RegExp('[ぁぃぅぇぉっゃゅょ]', 'g');\n\t\tvar elName,\n\t\t\telKana,\n\t\t\tactive = false,\n\t\t\ttimer = null,\n\t\t\tflagConvert = true,\n\t\t\tinput,\n\t\t\tvalues,\n\t\t\tignoreString,\n\t\t\tbaseKana;\n\n\t\telName = $(element1);\n\t\telKana = $(element2);\n\t\tactive = true;\n\t\t_stateClear();\n\n\t\telName.blur(_eventBlur);\n\t\telName.focus(_eventFocus);\n\t\telName.keydown(_eventKeyDown);\n\n\t\tfunction start() {\n\t\t\tactive = true;\n\t\t};\n\n\t\tfunction stop() {\n\t\t\tactive = false;\n\t\t};\n\n\t\tfunction toggle(event) {\n\t\t\tvar ev = event || window.event;\n\t\t\tif (event) {\n\t\t\t\tvar el = Event.element(event);\n\t\t\t\tif (el.checked) {\n\t\t\t\t\tactive = true;\n\t\t\t\t} else {\n\t\t\t\t\tactive = false;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tactive = !active;\n\t\t\t}\n\t\t};\n\n\t\tfunction _checkConvert(new_values) {\n\t\t\tif (!flagConvert) {\n\t\t\t\tif (Math.abs(values.length - new_values.length) > 1) {\n\t\t\t\t\tvar tmp_values = new_values.join('').replace(kana_compacting_pattern, '').split('');\n\t\t\t\t\tif (Math.abs(values.length - tmp_values.length) > 1) {\n\t\t\t\t\t\t_stateConvert();\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tif (values.length == input.length && values.join('') != input) {\n\t\t\t\t\t\tif (input.match(kana_extraction_pattern)) {\n\t\t\t\t\t\t\t_stateConvert();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\n\t\tfunction _checkValue() {\n\t\t\tvar new_input, new_values;\n\t\t\tnew_input = elName.val()\n\t\t\tif (new_input == '') {\n\t\t\t\t_stateClear();\n\t\t\t\t_setKana();\n\t\t\t} else {\n\t\t\t\tnew_input = _removeString(new_input);\n\t\t\t\tif (input == new_input) {\n\t\t\t\t\treturn;\n\t\t\t\t} else {\n\t\t\t\t\tinput = new_input;\n\t\t\t\t\tif (!flagConvert) {\n\t\t\t\t\t\tnew_values = new_input.replace(kana_extraction_pattern, '').split('');\n\t\t\t\t\t\t_checkConvert(new_values);\n\t\t\t\t\t\t_setKana(new_values);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\n\t\tfunction _clearInterval() {\n\t\t\tclearInterval(timer);\n\t\t};\n\n\t\tfunction _eventBlur(event) {\n\t\t\t_clearInterval();\n\t\t};\n\t\tfunction _eventFocus(event) {\n\t\t\t_stateInput();\n\t\t\t_setInterval();\n\t\t};\n\t\tfunction _eventKeyDown(event) {\n\t\t\tif (flagConvert) {\n\t\t\t\t_stateInput();\n\t\t\t}\n\t\t};\n\t\tfunction _isHiragana(chara) {\n\t\t\treturn ((chara >= 12353 && chara <= 12435) || chara == 12445 || chara == 12446);\n\t\t};\n\t\tfunction _removeString(new_input) {\n\t\t\tif (new_input.match(ignoreString)) {\n\t\t\t\treturn new_input.replace(ignoreString, '');\n\t\t\t} else {\n\t\t\t\tvar i, ignoreArray, inputArray;\n\t\t\t\tignoreArray = ignoreString.split('');\n\t\t\t\tinputArray = new_input.split('');\n\t\t\t\tfor (i = 0; i < ignoreArray.length; i++) {\n\t\t\t\t\tif (ignoreArray[i] == inputArray[i]) {\n\t\t\t\t\t\tinputArray[i] = '';\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn inputArray.join('');\n\t\t\t}\n\t\t};\n\t\tfunction _setInterval() {\n\t\t\tvar self = this;\n\t\t\ttimer = setInterval(_checkValue, 30);\n\t\t};\n\t\tfunction _setKana(new_values) {\n\t\t\tif (!flagConvert) {\n\t\t\t\tif (new_values) {\n\t\t\t\t\tvalues = new_values;\n\t\t\t\t}\n\t\t\t\tif (active) {\n\t\t\t\t\tvar _val = _toKatakana(baseKana + values.join(''));\n\t\t\t\t\telKana.val(_val);\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t\tfunction _stateClear() {\n\t\t\tbaseKana = '';\n\t\t\tflagConvert = false;\n\t\t\tignoreString = '';\n\t\t\tinput = '';\n\t\t\tvalues = [];\n\t\t};\n\t\tfunction _stateInput() {\n\t\t\tbaseKana = elKana.val();\n\t\t\tflagConvert = false;\n\t\t\tignoreString = elName.val();\n\t\t};\n\t\tfunction _stateConvert() {\n\t\t\tbaseKana = baseKana + values.join('');\n\t\t\tflagConvert = true;\n\t\t\tvalues = [];\n\t\t};\n\t\tfunction _toKatakana(src) {\n\t\t\tif (options.katakana) {\n\t\t\t\tvar c, i, str;\n\t\t\t\tstr = new String;\n\t\t\t\tfor (i = 0; i < src.length; i++) {\n\t\t\t\t\tc = src.charCodeAt(i);\n\t\t\t\t\tif (_isHiragana(c)) {\n\t\t\t\t\t\tstr += String.fromCharCode(c + 96);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tstr += src.charAt(i);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn str;\n\t\t\t} else {\n\t\t\t\treturn src;\n\t\t\t}\n\t\t}\n\t};\n})(jQuery);\n"], "file": "mm.util.min.js"}