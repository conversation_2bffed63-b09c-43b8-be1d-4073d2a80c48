<?php

/**
 * LOGレベルの定義
 *
 */
if (!defined('LOG_LEVEL_FATAL')) {
	define('LOG_LEVEL_FATAL', 1);
}
if (!defined('LOG_LEVEL_ERROR')) {
	define('LOG_LEVEL_ERROR', 2);
}
if (!defined('LOG_LEVEL_WARNING')) {
	define('LOG_LEVEL_WARNING', 3);
}
if (!defined('LOG_LEVEL_NOTICE')) {
	define('LOG_LEVEL_NOTICE', 4);
}
if (!defined('LOG_LEVEL_INFO')) {
	define('LOG_LEVEL_INFO', 5);
}
if (!defined('LOG_LEVEL_TRACE')) {
	define('LOG_LEVEL_TRACE', 6);
}
if (!defined('LOG_LEVEL_DEBUG')) {
	define('LOG_LEVEL_DEBUG', 7);
}


/**
 *
 *
 */
class Log {


	private static $_LEVELS = array(
		LOG_LEVEL_FATAL   => 'fatal',
		LOG_LEVEL_ERROR   => 'error',
		LOG_LEVEL_WARNING => 'warning',
		LOG_LEVEL_NOTICE  => 'notice',
		LOG_LEVEL_INFO    => 'info',
		LOG_LEVEL_TRACE   => 'trace',
		LOG_LEVEL_DEBUG   => 'debug',
	);


	/**
	 * ログ出力
	 * 
	 */
	public static function write($str, $level=6, $method_name='') {

		// ログレベルの判断
		if ($level > LOG_LEVEL) {
			return false;
		}

		// ログレベル
		$level_str  = isset(self::$_LEVELS[$level])?self::$_LEVELS[$level]:'other';
		$level_code = '[' . $level_str . ']';

		// 関数名の取得
		$method_name = ($method_name.'' != '')?($method_name . '() - '):'';

		// 出力するフォーマット
		$line = date('Y/m/d H:i:s') . Chr(9) . $level_code . Chr(9) . $method_name . $str. Chr(10);

		// ファイル名の取得（yyyymmdd.log）
		$outfn = (LOG_FILE_SEPARATE === 1)?(date('Ymd') . '_' . $level_str . '.log'):(date('Ymd').'.log');



		// 出力処理
		$handler = fopen(LOG_PATH . '/' . $outfn, "a");
		if (flock($handler, LOCK_EX)) {
			fputs($handler, $line);
			flock($handler, LOCK_UN);
		}

		fclose($handler);
	}

	/**
	 * ログレベルごとのログ出力関数
	 * 
	 */
	public static function fatal($str, $method_name='') {
		self::write($str, LOG_LEVEL_FATAL, $method_name);
	}
	public static function error($str, $method_name='') {
		self::write($str, LOG_LEVEL_ERROR, $method_name);
	}
	public static function warn($str, $method_name='') {
		self::write($str, LOG_LEVEL_WARNING, $method_name);
	}
	public static function notice($str, $method_name='') {
		self::write($str, LOG_LEVEL_NOTICE, $method_name);
	}
	public static function info($str, $method_name='') {
		self::write($str, LOG_LEVEL_INFO, $method_name);
	}
	public static function trace($str, $method_name='') {
		self::write($str, LOG_LEVEL_TRACE, $method_name);
	}
	public static function debug($str, $method_name='') {
		self::write($str, LOG_LEVEL_DEBUG, $method_name);
	}

}

