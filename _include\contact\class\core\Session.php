<?php
/**
 * Session管理用class
 *
 */
class Session {

	protected $sessname;
	protected $timeout; // セッションタイムアウト時間

	/**
	 * default constructor
	 *
	 * @param string $sessname セッション名
	 * @param array $params オプション
	 */
	public function __construct($sessname=null, array $params=array()) {

		// Session名の設定
		// ※同ドメイン配下の別アプリケーションに干渉しないようにするため
		if(is_string($sessname) && !empty($sessname)){
				$this->sessname = $sessname;
//			session_name($sessname);
		}

		// タイムアウト時間が指定されていないときは
		// php.iniで設定されているSessionのガベージコレクションまで時間を使用する
		$gc_maxlifetime = ini_get('session.gc_maxlifetime');
		$this->timeout = $gc_maxlifetime;

		if(is_array($params) && count($params) > 0) {
			if(isset($params['timeout']) && ($params['timeout'] > 0)){
				if($gc_maxlifetime < $params['timeout']){
//					ini_set('session.gc_maxlifetime', $params['timeout']);
				}
				$this->timeout = $params['timeout'];
			}
		}

	}


	/**
	 * Sessionの存在チェック
	 *
	 * @return boolean true: セッション開始中
	 */
	public function isExists() {

		if(isset($_COOKIE[session_name()])) {
			return true;
		}
		return false;
	}

	/**
	 * Sessionの開始＆timeoutのチェック
	 *
	 * @return boolean true : timeoutしていない ／ false : timeoutしている
	 */
	public function start() {

		if($this->sessname && is_string($this->sessname) && !empty($this->sessname)){
				session_name($this->sessname);
		}

		session_start();

		$now = time();

		// 前回アクセス時刻を取得
		$lastreq = $this->get('lastreq', $now);
		// アクセス時刻を保存
		$this->set('lastreq', $now);

		// timeoutのチェック
		if(($lastreq + $this->timeout) <= $now) {
			return false;
		}

		return true;
	}

	/**
	 * SessionIDの再生成
	 * 
	 */
	public function regenerate() {
		// ※ログイン認証機能を実装する場合、セキュリティ上、ログインの成功後にセッションIDを変更した方がよい
		session_regenerate_id(true);
	}

	/**
	 * Sessionの有効期限の設定＆ディレクトリの設定
	 * 
	 * ※start()の呼び出し前に実行する
	 * ※ブラウザを閉じたときに破棄する場合は$lifetimeを0にする
	 */
	public function setRange($lifetime=0, $path='/') {
		session_set_cookie_params($lifetime, $path);
	}

	/**
	 * Cookieの削除
	 * ※setcookie関数でクッキーの有効期限を過去に設定
	 * 
	 */
	public function deleteCookie() {
		if($this->isExists()) {
			$params = session_get_cookie_params();
			// （削除）
			setcookie(
				session_name(), '', time() - 4200,
				$params['path'],
				$params['domain'],
				$params['secure'],
				$params['httponly']
			);
		}
	}

	/**
	 * Sessionの終了処理
	 * 
	 */
	public function end() {
		// Session変数（$_SESSION）のデータをすべて破棄
		$this->clear();
		// SessionIDを保持しているCookieを削除
		$this->deleteCookie();
		// サーバ側のSesionデータを削除
		session_destroy();
	}


	/**
	 * Sessionへのset関数
	 *
	 * @param string $key キー
	 * @param mixed $value 設定する値
	 */
	public function set($key, $value){
		$_SESSION[$key] = $value;
	}


	/**
	 * Sessionからのget関数
	 *
	 * @param string $key キー
	 * @param mixed $default 存在しない場合のデフォルト値
	 * @return string セッション変数値
	 */
	public function get($key, $default=null){

		if(isset($_SESSION[$key])) {
			return $_SESSION[$key];
		}

		return $default;
	}


	/**
	 * Session変数の削除
	 * 
	 * @param string $key キー
	 */
	public function remove($key) {
		if(isset($_SESSION[$key])) {
			unset($_SESSION[$key]);
		}
	}

	/**
	 * Session変数のclear
	 *
	 */
	public function clear(){
		$_SESSION = array();
	}
}
